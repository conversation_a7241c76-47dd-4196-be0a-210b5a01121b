"use client";
import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  useToast,
  FormControl,
  FormLabel,
  Input,
  Switch,
  HStack,
  VStack,
  Text,
  Badge,
  SimpleGrid,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
} from "@chakra-ui/react";
import axiosInstance from "@src/app/axios";
import { getData } from "@src/app/utils/functions";
import { useUser } from "@src/app/provider/UserContext";
import ReportTable from "@src/components/Custom/ReportTable";

const GodownPage = () => {
  const toast = useToast();
  const { user } = useUser();
  const isAdmin = user?.roleName === 'Admin' || user?.roleID === 1;

  const [godowns, setGodowns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [nextId, setNextId] = useState("");
  const [form, setForm] = useState({
    Id: "",
    Title: "",
    PH: 0,
    CompanyAddress1: "",
    CompanyAddress2: "",
    CompanyPhone: "",
    CompanyFax: "",
    CompanyEmail: "",
    CompanyURL: "",
    CompanySTN: "",
    CompanyNTN: "",
    AllowNegativeBalances: 0,
    Deactive: 0,
    Prp_ID: "",
    chk_id: "",
    app_id: "",
    FinancialAcc_ID: "",
    ComputerName: "",
  });
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState("");
  const [search, setSearch] = useState("");
  const [sortKey, setSortKey] = useState("Id");
  const [sortDir, setSortDir] = useState("asc");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (isAdmin) {
      refreshAll();
    }
  }, [isAdmin]);

  const refreshAll = async () => {
    setLoading(true);
    try {
      const [list, next] = await Promise.all([
        getData('godown'),
        getData('godown/next-id'),
      ]);
      setGodowns(list.godowns || []);
      if (next?.nextId) setNextId(next.nextId);
      if (!form.Id) setForm(prev => ({ ...prev, Id: next?.nextId || prev.Id }));
    } catch (e) {
      console.error(e);
      toast({ title: "Error", description: "Failed to load godowns", status: "error" });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setForm({
      Id: nextId || "",
      Title: "",
      PH: 0,
      CompanyAddress1: "",
      CompanyAddress2: "",
      CompanyPhone: "",
      CompanyFax: "",
      CompanyEmail: "",
      CompanyURL: "",
      CompanySTN: "",
      CompanyNTN: "",
      AllowNegativeBalances: 0,
      Deactive: 0,
      Prp_ID: "",
      chk_id: "",
      app_id: "",
      FinancialAcc_ID: "",
      ComputerName: "",
    });
    setLogoFile(null);
    setLogoPreview("");
  };

  const handleSelectRow = (g) => {
    setForm({
      Id: g.Id,
      Title: g.Title || "",
      PH: g.PH || 0,
      CompanyAddress1: g.CompanyAddress1 || "",
      CompanyAddress2: g.CompanyAddress2 || "",
      CompanyPhone: g.CompanyPhone || "",
      CompanyFax: g.CompanyFax || "",
      CompanyEmail: g.CompanyEmail || "",
      CompanyURL: g.CompanyURL || "",
      CompanySTN: g.CompanySTN || "",
      CompanyNTN: g.CompanyNTN || "",
      AllowNegativeBalances: g.AllowNegativeBalances || 0,
      Deactive: g.Deactive || 0,
      Prp_ID: g.Prp_ID || "",
      chk_id: g.chk_id || "",
      app_id: g.app_id || "",
      FinancialAcc_ID: g.FinancialAcc_ID || "",
      ComputerName: g.ComputerName || "",
    });
    setLogoFile(null);
    setLogoPreview("");
  };

  const handleChange = (key, value) => {
    setForm(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    if (!form.Title || !form.Id) {
      toast({ title: "Missing", description: "Id and Title are required", status: "warning" });
      return;
    }
    setSaving(true);
    try {
      const payload = { ...form };
      // Convert boolean-like switches to 0/1
      payload.PH = Number(payload.PH ? 1 : 0);
      payload.AllowNegativeBalances = Number(payload.AllowNegativeBalances ? 1 : 0);
      payload.Deactive = Number(payload.Deactive ? 1 : 0);

      // check if exists
      const exists = godowns.some(g => g.Id === form.Id);
      if (exists) {
        await axiosInstance.put(`godown/${form.Id}`, payload);
      } else {
        await axiosInstance.post('godown/create', payload);
      }

      if (logoFile) {
        const fd = new FormData();
        fd.append('godownId', form.Id);
        fd.append('image', logoFile);
        await axiosInstance.post('godown/upload-logo', fd, { headers: { 'Content-Type': 'multipart/form-data' } });
      }

      toast({ title: "Saved", description: "Godown saved successfully", status: "success" });
      await refreshAll();
      onClose();
    } catch (e) {
      console.error(e);
      toast({ title: "Error", description: "Failed to save godown", status: "error" });
    } finally {
      setSaving(false);
    }
  };

  const filteredSorted = React.useMemo(() => {
    const q = search.toLowerCase().trim();
    let rows = [...godowns];
    if (q) {
      rows = rows.filter(g =>
        (g.Id || "").toLowerCase().includes(q) ||
        (g.Title || "").toLowerCase().includes(q) ||
        (g.CompanyEmail || "").toLowerCase().includes(q) ||
        (g.CompanyPhone || "").toLowerCase().includes(q)
      );
    }
    rows.sort((a, b) => {
      const A = (a[sortKey] ?? "").toString().toLowerCase();
      const B = (b[sortKey] ?? "").toString().toLowerCase();
      if (A < B) return sortDir === 'asc' ? -1 : 1;
      if (A > B) return sortDir === 'asc' ? 1 : -1;
      return 0;
    });
    return rows;
  }, [godowns, search, sortKey, sortDir]);

  const onPickLogo = (file) => {
    setLogoFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = e => setLogoPreview(e.target?.result || "");
      reader.readAsDataURL(file);
    } else {
      setLogoPreview("");
    }
  };

  const openCreateModal = () => {
    resetForm();
    setIsEditing(false);
    onOpen();
  };

  const openEditModal = (g) => {
    handleSelectRow(g);
    setIsEditing(true);
    onOpen();
  };

  if (!isAdmin) {
    return (
      <div className="wrapper">
        <div className="page-inner">
          <Box className="bgWhite" textAlign="center" p={8}>
            <Text fontSize="xl" color="red.500">Access Denied: Admin privileges required</Text>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="wrapper">
      <div className="page-inner">
        <Box className="bgWhite" textAlign="center" p={3}>
          <h1 style={{ color: "#2B6CB0", fontSize: "24px", fontWeight: "bold" }}>Godown Management</h1>
        </Box>

        <Box className="bgWhite" p={4} mt={2}>
          <HStack justify="space-between" mb={3}>
            <HStack>
              <Text fontSize="lg" fontWeight="bold">Total: {godowns.length}</Text>
              <Badge colorScheme="blue">{filteredSorted.length} shown</Badge>
            </HStack>
            <HStack>
              <Button colorScheme="green" onClick={refreshAll} isLoading={loading}>Refresh</Button>
              <Button colorScheme="blue" onClick={openCreateModal}>New</Button>
            </HStack>
          </HStack>
          <ReportTable
            data={filteredSorted}
            columns={[
              {
                header: 'Logo',
                render: (g) => (
                  <HStack>
                    <Box boxSize="56px" borderRadius="md" overflow="hidden" bg="gray.100">
                      <img
                        src={`${process.env.NEXT_PUBLIC_BASE_URL ? process.env.NEXT_PUBLIC_BASE_URL : ''}godown/${g.Id}/logo?fallback=true`}
                        onError={(e) => { e.currentTarget.style.display = 'none'; }}
                        alt="logo"
                        style={{ width: '56px', height: '56px', objectFit: 'cover' }}
                      />
                    </Box>
                  </HStack>
                )
              },
              { header: 'ID', field: 'Id' },
              { header: 'Title', field: 'Title' },
              { header: 'Phone', field: 'CompanyPhone' },
              { header: 'Email', field: 'CompanyEmail' },
              {
                header: 'Flags',
                render: (g) => (
                  <HStack>
                    <Badge colorScheme={g.PH ? 'green' : 'gray'}>PH</Badge>
                    <Badge colorScheme={g.AllowNegativeBalances ? 'purple' : 'gray'}>AllowNeg</Badge>
                    <Badge colorScheme={g.Deactive ? 'red' : 'green'}>{g.Deactive ? 'Inactive' : 'Active'}</Badge>
                  </HStack>
                )
              },
              {
                header: 'Actions',
                render: (g) => (
                  <Button size="sm" onClick={(e) => { e.stopPropagation(); openEditModal(g); }}>Edit</Button>
                )
              },
            ]}
            showDateFilter={false}
            sortField={'Title'}
            disablePagination={true}
          />
        </Box>

        {/* Create/Update Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="4xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{isEditing ? 'Update Godown' : 'Create Godown'}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <VStack spacing={4} align="stretch">
                  <HStack>
                    <FormControl isRequired>
                      <FormLabel>Id</FormLabel>
                      <Input value={form.Id} onChange={(e) => handleChange('Id', e.target.value)} maxLength={8} isDisabled={isEditing} />
                    </FormControl>
                    <FormControl isRequired>
                      <FormLabel>Title</FormLabel>
                      <Input value={form.Title} onChange={(e) => handleChange('Title', e.target.value)} maxLength={70} />
                    </FormControl>
                  </HStack>
                  <HStack>
                    <FormControl>
                      <FormLabel>Phone</FormLabel>
                      <Input value={form.CompanyPhone} onChange={(e) => handleChange('CompanyPhone', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>Fax</FormLabel>
                      <Input value={form.CompanyFax} onChange={(e) => handleChange('CompanyFax', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>Email</FormLabel>
                      <Input value={form.CompanyEmail} onChange={(e) => handleChange('CompanyEmail', e.target.value)} />
                    </FormControl>
                  </HStack>
                  <HStack>
                    <FormControl>
                      <FormLabel>URL</FormLabel>
                      <Input value={form.CompanyURL} onChange={(e) => handleChange('CompanyURL', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>STN</FormLabel>
                      <Input value={form.CompanySTN} onChange={(e) => handleChange('CompanySTN', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>NTN</FormLabel>
                      <Input value={form.CompanyNTN} onChange={(e) => handleChange('CompanyNTN', e.target.value)} />
                    </FormControl>
                  </HStack>
                  <HStack>
                    <FormControl>
                      <FormLabel>Address 1</FormLabel>
                      <Input value={form.CompanyAddress1} onChange={(e) => handleChange('CompanyAddress1', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>Address 2</FormLabel>
                      <Input value={form.CompanyAddress2} onChange={(e) => handleChange('CompanyAddress2', e.target.value)} />
                    </FormControl>
                  </HStack>
                  <HStack>
                    <FormControl display="flex" alignItems="center">
                      <FormLabel mb="0">PH</FormLabel>
                      <Switch isChecked={!!form.PH} onChange={(e) => handleChange('PH', e.target.checked ? 1 : 0)} />
                    </FormControl>
                    <FormControl display="flex" alignItems="center">
                      <FormLabel mb="0">Allow Negative</FormLabel>
                      <Switch isChecked={!!form.AllowNegativeBalances} onChange={(e) => handleChange('AllowNegativeBalances', e.target.checked ? 1 : 0)} />
                    </FormControl>
                    <FormControl display="flex" alignItems="center">
                      <FormLabel mb="0">Inactive</FormLabel>
                      <Switch isChecked={!!form.Deactive} onChange={(e) => handleChange('Deactive', e.target.checked ? 1 : 0)} />
                    </FormControl>
                  </HStack>
                  <HStack>
                    <FormControl>
                      <FormLabel>FinancialAcc_ID</FormLabel>
                      <Input value={form.FinancialAcc_ID} onChange={(e) => handleChange('FinancialAcc_ID', e.target.value)} />
                    </FormControl>
                    <FormControl>
                      <FormLabel>Computer Name</FormLabel>
                      <Input value={form.ComputerName} onChange={(e) => handleChange('ComputerName', e.target.value)} />
                    </FormControl>
                  </HStack>
                </VStack>
                <VStack spacing={4} align="stretch">
                  <Box border="1px solid" borderColor="gray.200" borderRadius="md" p={4}>
                    <Text fontWeight="bold" mb={2}>Logo</Text>
                    <VStack align="stretch" spacing={3}>
                      {logoPreview ? (
                        <Box>
                          <img src={logoPreview} alt="Logo preview" style={{ maxHeight: 120, objectFit: 'contain' }} />
                        </Box>
                      ) : (
                        <Box bg="gray.50" border="1px dashed #CBD5E0" borderRadius="md" p={6} textAlign="center">
                          <Text color="gray.500">No logo selected</Text>
                        </Box>
                      )}
                      <Input type="file" accept="image/*" onChange={(e) => onPickLogo(e.target.files?.[0] || null)} />
                      {logoPreview && (
                        <Button size="sm" onClick={() => onPickLogo(null)}>Clear Logo</Button>
                      )}
                    </VStack>
                  </Box>
                </VStack>
              </SimpleGrid>
            </ModalBody>
            <ModalFooter>
              <Button mr={3} onClick={onClose}>Cancel</Button>
              <Button colorScheme="blue" onClick={handleSave} isLoading={saving}>{isEditing ? 'Update' : 'Create'}</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
};

export default GodownPage;


