"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/components/PrintModal.jsx":
/*!***************************************!*\
  !*** ./src/components/PrintModal.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-to-print */ \"(app-pages-browser)/./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PrintModal = (param)=>{\n    let { isOpen, onClose, children, formName = \"Form Name\", showHeader = true, buttonText = \"Print\", callback } = param;\n    _s();\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const userLocation = (user === null || user === void 0 ? void 0 : user.Location) || (user === null || user === void 0 ? void 0 : user.location) || \"\";\n    const logoSrc = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat(userLocation, \"/logo?fallback=true\");\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint)({\n        contentRef: componentRef\n    });\n    // Helper function to convert image URL to base64\n    const convertImageToBase64 = async (url)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = ()=>resolve(reader.result);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        } catch (error) {\n            console.warn(\"Failed to convert image to base64:\", error);\n            return null;\n        }\n    };\n    // Helper function to wait for all images to load\n    const waitForImages = async (element)=>{\n        const images = element.querySelectorAll(\"img\");\n        const imagePromises = Array.from(images).map(async (img)=>{\n            if (img.complete) return;\n            return new Promise((resolve, reject)=>{\n                const timeout = setTimeout(()=>{\n                    console.warn(\"Image load timeout:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                }, 5000);\n                img.onload = ()=>{\n                    clearTimeout(timeout);\n                    resolve();\n                };\n                img.onerror = ()=>{\n                    clearTimeout(timeout);\n                    console.warn(\"Image load error:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                };\n            });\n        });\n        await Promise.all(imagePromises);\n    };\n    const handleGeneratePDF = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            // Wait for all images to load\n            await waitForImages(componentRef.current);\n            // Convert external images to base64 to avoid CORS issues\n            const images = componentRef.current.querySelectorAll(\"img\");\n            for (const img of images){\n                if (img.src.startsWith(\"http\") && !img.src.includes(\"data:\")) {\n                    try {\n                        const base64 = await convertImageToBase64(img.src);\n                        if (base64) {\n                            img.src = base64;\n                        }\n                    } catch (error) {\n                        console.warn(\"Failed to convert image:\", img.src, error);\n                    }\n                }\n            }\n            // Small delay to ensure DOM updates\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                onclone: (clonedDoc)=>{\n                    // Ensure all images in the cloned document are properly loaded\n                    const clonedImages = clonedDoc.querySelectorAll(\"img\");\n                    clonedImages.forEach((img)=>{\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                    });\n                }\n            });\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"](\"p\", \"mm\", \"a4\");\n            // Calculate dimensions to fit the page properly\n            const pdfWidth = pdf.internal.pageSize.getWidth();\n            const pdfHeight = pdf.internal.pageSize.getHeight();\n            const canvasAspectRatio = canvas.height / canvas.width;\n            const pdfAspectRatio = pdfHeight / pdfWidth;\n            let imgWidth, imgHeight;\n            if (canvasAspectRatio > pdfAspectRatio) {\n                imgHeight = pdfHeight - 20; // 10mm margin on top and bottom\n                imgWidth = imgHeight / canvasAspectRatio;\n            } else {\n                imgWidth = pdfWidth - 20; // 10mm margin on left and right\n                imgHeight = imgWidth * canvasAspectRatio;\n            }\n            const x = (pdfWidth - imgWidth) / 2;\n            const y = (pdfHeight - imgHeight) / 2;\n            pdf.addImage(imgData, \"PNG\", x, y, imgWidth, imgHeight);\n            const pdfBlob = pdf.output(\"blob\");\n            if (callback) {\n                callback(pdfBlob);\n            } else {\n                handlePrint();\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            toast({\n                title: \"Error generating PDF\",\n                description: \"Please try again or contact support if the issue persists.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalContent, {\n                height: \"100vh\",\n                width: \"auto\",\n                overflow: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalHeader, {\n                        children: \"Print Preview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalBody, {\n                        maxHeight: \"90vh\",\n                        overflow: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Box, {\n                            ref: componentRef,\n                            minWidth: \"700px\",\n                            className: \"print-container\",\n                            paddingTop: 12,\n                            children: [\n                                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"center\",\n                                                alignItems: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                alt: \"logo\",\n                                                src: logoSrc,\n                                                style: {\n                                                    height: \"70px\",\n                                                    width: \"auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            fontSize: \"xl\",\n                                            fontStyle: \"italic\",\n                                            textAlign: \"center\",\n                                            margin: \"20px 0 20px 0 !important\",\n                                            children: formName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                children\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalFooter, {\n                        gap: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: handleGeneratePDF,\n                            isLoading: isGenerating,\n                            loadingText: \"Generating PDF...\",\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrintModal, \"GEIWinyei48BORxa3W6Bbzad+uA=\", false, function() {\n    return [\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint\n    ];\n});\n_c = PrintModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PrintModal);\nvar _c;\n$RefreshReg$(_c, \"PrintModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PrintModal.jsx\n"));

/***/ })

});