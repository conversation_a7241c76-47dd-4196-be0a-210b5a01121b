"use client";
import React, { useEffect, useState } from "react";
import { 
  <PERSON>, 
  Button, 
  Table, 
  Thead, 
  Tbody, 
  Tr, 
  Th, 
  Td, 
  useToast, 
  Modal, 
  ModalOverlay, 
  ModalContent, 
  ModalHeader, 
  ModalBody, 
  ModalCloseButton, 
  useDisclosure,
  FormControl,
  FormLabel,
  Input,
  Select,
  HStack,
  VStack,
  Text,
  Badge,
  IconButton,
  Tooltip,
} from "@chakra-ui/react";
import { EditIcon } from "@chakra-ui/icons";
import { getData } from "@src/app/utils/functions";
import axiosInstance from "@src/app/axios";
import { useUser } from "@src/app/provider/UserContext";

const EmployeeList = () => {
  const toast = useToast();
  const { user } = useUser();
  const [employees, setEmployees] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [locations, setLocations] = useState([]);
  
  // Modals
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  

  // Check if user is admin
  const isAdmin = user?.roleName === 'Admin' || user?.roleID === 1;

  useEffect(() => {
    if (isAdmin) {
      fetchEmployees();
      fetchRoles();
      fetchLocations();
    }
  }, [isAdmin]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await getData('employee');
      setEmployees(response.employees || []);
    } catch (error) {
      console.error('Error fetching employees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const roleList = await getData('getRecords/roles');
      setRoles(roleList);
    } catch (error) {
      console.error('Error fetching roles:', error);
    }
  };

  const fetchLocations = async () => {
    try {
      const locations = await getData('getRecords/locations');
      setLocations(locations);
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  const handleEditEmployee = (employee) => {
    setEditingEmployee(employee);
    onEditOpen();
  };

  const handleUpdateEmployee = async () => {
    try {
      const [firstName, ...lastNameParts] = editingEmployee.Title.split(' ');
      const lastName = lastNameParts.join(' ');
      
      const updateData = {
        firstName,
        lastName,
        email: editingEmployee.EMail || editingEmployee.EmailAddress,
        phoneNo: editingEmployee.Mobile,
        location: editingEmployee.Location,
        roleId: editingEmployee.RoleID,
        abn: editingEmployee.IBANNumber,
        licenseNo: editingEmployee.LicenseNo
      };

      await axiosInstance.put(`employee/${editingEmployee.ID}`, updateData);
      
      toast({
        title: "Success",
        description: "Employee updated successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      
      onEditClose();
      fetchEmployees();
    } catch (error) {
      console.error('Error updating employee:', error);
      toast({
        title: "Error",
        description: "Failed to update employee",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  

  

  if (!isAdmin) {
    return (
      <div className="wrapper">
        <div className="page-inner">
          <Box className="bgWhite" textAlign="center" p={8}>
            <Text fontSize="xl" color="red.500">
              Access Denied: Admin privileges required
            </Text>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="wrapper">
      <div className="page-inner">
        <Box className="bgWhite" textAlign="center" p={3}>
          <h1 style={{ color: "#2B6CB0", fontSize: "24px", fontWeight: "bold" }}>
            Employee Management
          </h1>
        </Box>

        <Box className="bgWhite" p={4} mt={2}>
          <HStack justify="space-between" mb={4}>
            <Text fontSize="lg" fontWeight="bold">
              Total Employees: {employees.length}
            </Text>
            <HStack>
              <Button
                colorScheme="green"
                onClick={fetchEmployees}
                isLoading={loading}
              >
                Refresh
              </Button>
            </HStack>
          </HStack>

          <Box overflowX="auto">
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th>ID</Th>
                  <Th>Name</Th>
                  <Th>Username</Th>
                  <Th>Email</Th>
                  <Th>Phone</Th>
                  <Th>Location</Th>
                  <Th>Role</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {employees.map((employee) => (
                  <Tr key={employee.ID}>
                    <Td>{employee.ID}</Td>
                    <Td>{employee.Title}</Td>
                    <Td>{employee.Username}</Td>
                    <Td>{employee.EMail || employee.EmailAddress}</Td>
                    <Td>{employee.Mobile}</Td>
                    <Td>
                      <Badge colorScheme="blue">{employee.Location || 'N/A'}</Badge>
                    </Td>
                    <Td>
                      <Badge colorScheme="green">{employee.RoleName || 'N/A'}</Badge>
                    </Td>
                    <Td>
                      <HStack>
                        <Tooltip label="Edit Employee">
                          <IconButton
                            icon={<EditIcon />}
                            size="sm"
                            colorScheme="blue"
                            onClick={() => handleEditEmployee(employee)}
                          />
                        </Tooltip>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        </Box>

        {/* Edit Employee Modal */}
        <Modal isOpen={isEditOpen} onClose={onEditClose} size="lg">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Edit Employee</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              {editingEmployee && (
                <VStack spacing={4}>
                  <FormControl>
                    <FormLabel>Name</FormLabel>
                    <Input
                      value={editingEmployee.Title}
                      onChange={(e) => setEditingEmployee({
                        ...editingEmployee,
                        Title: e.target.value
                      })}
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Email</FormLabel>
                    <Input
                      value={editingEmployee.EMail || editingEmployee.EmailAddress}
                      onChange={(e) => setEditingEmployee({
                        ...editingEmployee,
                        EMail: e.target.value
                      })}
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Phone</FormLabel>
                    <Input
                      value={editingEmployee.Mobile}
                      onChange={(e) => setEditingEmployee({
                        ...editingEmployee,
                        Mobile: e.target.value
                      })}
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Location</FormLabel>
                    <Select
                      value={editingEmployee.Location || ''}
                      onChange={(e) => setEditingEmployee({
                        ...editingEmployee,
                        Location: e.target.value
                      })}
                    >
                      <option value="">Select Location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>
                          {location.title} ({location.id})
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl>
                    <FormLabel>Role</FormLabel>
                    <Select
                      value={editingEmployee.RoleID}
                      onChange={(e) => setEditingEmployee({
                        ...editingEmployee,
                        RoleID: parseInt(e.target.value)
                      })}
                    >
                      {roles.map(role => (
                        <option key={role.id} value={role.id}>
                          {role.title}
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                  <HStack>
                    <Button colorScheme="blue" onClick={handleUpdateEmployee}>
                      Update
                    </Button>
                    <Button onClick={onEditClose}>Cancel</Button>
                  </HStack>
                </VStack>
              )}
            </ModalBody>
          </ModalContent>
        </Modal>

        
      </div>
    </div>
  );
};

export default EmployeeList;
