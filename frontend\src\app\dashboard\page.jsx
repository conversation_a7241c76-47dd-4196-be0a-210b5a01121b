"use client";
import React from "react";
import MainDashboard from "./MainDashboard";
import AssessorDashboard from "./AssessorDashboard";
import DeliveryDashboard from "./DeliveryDashboard";
import InstallerDashboard from "./InstallerDashboard";
import "@src/app/dashboard/dashboard.css";
import { useUser } from "../provider/UserContext";
import { Flex, Spinner } from "@chakra-ui/react";

const Dashboard = () => {
    const { userRole } = useUser();
    return (
        <>
            <div className="wrapper">
                {
                    userRole === "Assessor" ? (
                        <AssessorDashboard />
                    ) : userRole === "Delivery" ? (
                        <DeliveryDashboard />
                    ) : userRole === "Installer" ? (
                        <InstallerDashboard />
                    ) : userRole === "Admin" || userRole === "User" ? (
                        <MainDashboard />
                    ) : (
                        <Flex h="100vh" align="center" justify="center">
                            <Spinner size="xl" color="green.500" />
                        </Flex>
                    )
                }
            </div>
        </>
    );
};

export default Dashboard;
