name: Deploy Impex_Grac_Backend

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: [self-hosted, Windows, X64]

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Update project code
        run: |
          cd "C:\Users\<USER>\Documents\ImpexGrac_Backend"
          git restore .
          git pull origin main

      - name: Install dependencies
        run: |
          cd "C:\Users\<USER>\Documents\ImpexGrac_Backend"
          npm install

      - name: Restart app with PM2
        run: |
          cd "C:\Users\<USER>\Documents\ImpexGrac_Backend"
          pm2 restart Impex_Grac_Backend
