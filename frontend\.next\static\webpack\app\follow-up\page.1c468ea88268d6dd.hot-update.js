"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/components/PrintModal.jsx":
/*!***************************************!*\
  !*** ./src/components/PrintModal.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-to-print */ \"(app-pages-browser)/./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PrintModal = (param)=>{\n    let { isOpen, onClose, children, formName = \"Form Name\", showHeader = true, buttonText = \"Print\", callback } = param;\n    _s();\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const userLocation = (user === null || user === void 0 ? void 0 : user.Location) || (user === null || user === void 0 ? void 0 : user.location) || \"\";\n    const logoSrc = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat(userLocation, \"/logo?fallback=true\");\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint)({\n        contentRef: componentRef\n    });\n    // Helper function to convert image URL to base64\n    const convertImageToBase64 = async (url)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = ()=>resolve(reader.result);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        } catch (error) {\n            console.warn(\"Failed to convert image to base64:\", error);\n            return null;\n        }\n    };\n    // Helper function to wait for all images to load\n    const waitForImages = async (element)=>{\n        const images = element.querySelectorAll(\"img\");\n        const imagePromises = Array.from(images).map(async (img)=>{\n            if (img.complete) return;\n            return new Promise((resolve, reject)=>{\n                const timeout = setTimeout(()=>{\n                    console.warn(\"Image load timeout:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                }, 5000);\n                img.onload = ()=>{\n                    clearTimeout(timeout);\n                    resolve();\n                };\n                img.onerror = ()=>{\n                    clearTimeout(timeout);\n                    console.warn(\"Image load error:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                };\n            });\n        });\n        await Promise.all(imagePromises);\n    };\n    const handleGeneratePDF = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            console.log(\"Starting PDF generation...\");\n            // Wait for all images to load\n            await waitForImages(componentRef.current);\n            console.log(\"All images loaded\");\n            // Convert external images to base64 to avoid CORS issues\n            const images = componentRef.current.querySelectorAll(\"img\");\n            console.log(\"Found \".concat(images.length, \" images to process\"));\n            for (const img of images){\n                if (img.src.startsWith(\"http\") && !img.src.includes(\"data:\")) {\n                    console.log(\"Converting image to base64:\", img.src);\n                    try {\n                        const base64 = await convertImageToBase64(img.src);\n                        if (base64) {\n                            img.src = base64;\n                            console.log(\"Successfully converted image to base64\");\n                        }\n                    } catch (error) {\n                        console.warn(\"Failed to convert image:\", img.src, error);\n                    }\n                }\n            }\n            // Small delay to ensure DOM updates\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                onclone: (clonedDoc)=>{\n                    // Ensure all images in the cloned document are properly loaded\n                    const clonedImages = clonedDoc.querySelectorAll(\"img\");\n                    clonedImages.forEach((img)=>{\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                    });\n                }\n            });\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"](\"p\", \"mm\", \"a4\");\n            // Calculate dimensions to fit the page properly\n            const pdfWidth = pdf.internal.pageSize.getWidth();\n            const pdfHeight = pdf.internal.pageSize.getHeight();\n            const canvasAspectRatio = canvas.height / canvas.width;\n            const pdfAspectRatio = pdfHeight / pdfWidth;\n            let imgWidth, imgHeight;\n            if (canvasAspectRatio > pdfAspectRatio) {\n                imgHeight = pdfHeight - 20; // 10mm margin on top and bottom\n                imgWidth = imgHeight / canvasAspectRatio;\n            } else {\n                imgWidth = pdfWidth - 20; // 10mm margin on left and right\n                imgHeight = imgWidth * canvasAspectRatio;\n            }\n            const x = (pdfWidth - imgWidth) / 2;\n            const y = (pdfHeight - imgHeight) / 2;\n            pdf.addImage(imgData, \"PNG\", x, y, imgWidth, imgHeight);\n            const pdfBlob = pdf.output(\"blob\");\n            if (callback) {\n                callback(pdfBlob);\n            } else {\n                handlePrint();\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            toast({\n                title: \"Error generating PDF\",\n                description: \"Please try again or contact support if the issue persists.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalContent, {\n                height: \"100vh\",\n                width: \"auto\",\n                overflow: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalHeader, {\n                        children: \"Print Preview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalBody, {\n                        maxHeight: \"90vh\",\n                        overflow: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Box, {\n                            ref: componentRef,\n                            minWidth: \"700px\",\n                            className: \"print-container\",\n                            paddingTop: 12,\n                            children: [\n                                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"center\",\n                                                alignItems: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                alt: \"logo\",\n                                                src: logoSrc,\n                                                style: {\n                                                    height: \"70px\",\n                                                    width: \"auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            fontSize: \"xl\",\n                                            fontStyle: \"italic\",\n                                            textAlign: \"center\",\n                                            margin: \"20px 0 20px 0 !important\",\n                                            children: formName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                children\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalFooter, {\n                        gap: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: handleGeneratePDF,\n                            isLoading: isGenerating,\n                            loadingText: \"Generating PDF...\",\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrintModal, \"GEIWinyei48BORxa3W6Bbzad+uA=\", false, function() {\n    return [\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint\n    ];\n});\n_c = PrintModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PrintModal);\nvar _c;\n$RefreshReg$(_c, \"PrintModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PrintModal.jsx\n"));

/***/ })

});