"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        p: 8,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            src: logoImage,\n                            alt: \"Eco Assets Manager\",\n                            height: \"100px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});