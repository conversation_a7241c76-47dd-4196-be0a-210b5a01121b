// Bank Receipt & Bank Payment Voucher Starts Here--------------------------------------------------------------  

export const BankVoucherItemTableHeader = ["ID", "Title"];

export const BankVoucherHeaders = [
    {
        label: "A/C ID",
        key: "AC_ID",
        width: "110px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Title",
        key: "Title",
        width: "270px",
        isReadOnly: false,
        type: "text",
    },
    // {
    //     label: "Emp Title",
    //     key: "Emp_Title",
    //     width: "250px",
    //     isReadOnly: false,
    //     type: "text",
    // },
    {
        label: "Adj. Voucher No",
        key: "AdjVoucherNo",
        width: "140px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Total Amount",
        key: "TotalAmount",
        width: "120px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Paid Amount",
        key: "PaidAmount",
        width: "120px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Remaining Amount",
        key: "RemainingAmount",
        width: "130px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Received Amt",
        key: "ReceivedAmt",
        width: "120px",
        isReadOnly: false,
        type: "number",
    },
    // {
    //     label: "Tax %",
    //     key: "TaxPercentage",
    //     width: "40px",
    //     isReadOnly: false,
    //     type: "number",
    // },
    // {
    //     label: "Tax Amt",
    //     key: "TaxAmt",
    //     width: "90px",
    //     isReadOnly: true,
    //     type: "number",
    // },
    // {
    //     label: "Tax Account ID",
    //     key: "Tax_Acc_ID",
    //     width: "120px",
    //     isReadOnly: false,
    //     type: "number",
    // },
    // {
    //     label: "Tax Account Title",
    //     key: "Tax_Acc_Title",
    //     width: "250px",
    //     isReadOnly: false,
    //     type: "number",
    // },
    {
        label: "Narration",
        key: "Narration",
        width: "320px",
        isReadOnly: false,
        type: "text",
    },
];

export const BankVoucherSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "Date",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`,
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Voucher No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ],
    [
        {
            label: "Bank Code",
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "bank",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Title"],
                    keys: ["id", "title"],
                    nameFields: ["cashId", "cashTitle"],
                },
            ],
        },
        {
            label: "Cheque #",
            fields: [
                {
                    component: "Input",
                    placeholder: "Number",
                    width: "100%",
                    value: "refNo",
                    name: "refNo"
                },
            ]
        },
        // {
        //     label: "Cheque Title",
        //     fields: [
        //         {
        //             component: "Input",
        //             placeholder: "Number",
        //             width: "100%",
        //             value: "chqTitle",
        //             name: "chqTitle"
        //         },
        //     ]
        // },
            // {
            //     label: "Realization Date",
            //     fields: [
            //         {
            //             component: "Date",
            //             type: "Date",
            //             value: "realizationDate",
            //             name: "realizationDate"
            //         }
            //     ]
            // },
        {
            label: "Cheque Date",
            fields: [
                {
                    component: "Date",
                    type: "Date",
                    value: "chqDate",
                    name: "chqDate"
                }
            ]
        },
        // {
        //     fields: [
        //         {
        //             component: "Button",
        //             value: "Bid Money",
        //             name: "copyOfficialReceipt",
        //             type: "button",
        //         },
        //         {
        //             component: "Button",
        //             value: "Adjustments",
        //             name: "Adjustments",
        //             type: "button",
        //         },
        //     ],
        // },
    ],
    [
        {
            label: "Total Chq Amt",
            name: "totalChqAmt",
            style: { width: { base: "100%", sm: "100%", lg: "90%" } },
            value: "totalChqAmt",
            isReadOnly: true,
        }
    ]
];


export const BankVoucherCalculationFields = [
    "totalTaxAmt", "totalAmt", "totalAmtReceived"
];

export const CreateBankVoucherEmptyTableRow = () => ([{
    AC_ID: "", Title: "", Emp_ID: "", Emp_Title: null,
    AdjVoucherNo: "", TotalAmount: "", PaidAmount: "", RemainingAmount: "",
    ReceivedAmt: "", Narration: "",
    TaxPercentage: null, TaxAmt: null, Tax_Acc_ID: "", Tax_Acc_Title: "",
}]);

export const CreateBankVoucherInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "",
    mnth: "", cashId: "", cashTitle: "", totalChqAmt: 0,
    refNo: "", chqTitle: null, realizationDate: null, chqDate: "",
    narration: ""
});

export const BankVoucherRequiredFields = [
    "Dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
    "amt",
    "bc_id"
];

export const BankVoucherItemsRequiredFields = [
    "Dated",
    "VTP",
    "Mnth",
    "Location",
    "vno",
    "srno",
    "acc_id",
    "Currency_ID",
    "ln_rem",
    "Amt",
    "TotalAmt",
];

// Bank Receipt & Bank Payment Voucher Ends Here---------------------------------------------