import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Select,
  useToast,
} from "@chakra-ui/react";
import axiosInstance from "@src/app/axios";
import { formatDate } from "@src/app/utils/functions";
import React, { useState, useEffect } from 'react';

const MatureLeadModal = ({ isOpen, onClose, onSave, purposeId }) => {
  const toast = useToast();
  const [formData, setFormData] = useState({
    assessmentTime: '',
    assessmentDate: '',
    assessmentHour: '',
    contactPerson: '',
    remarks: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({ ...prevState, [name]: value }));

    if (name === 'assessmentDate' || name === 'assessmentHour') {
      const updatedFormData = { ...formData, [name]: value };

      if (updatedFormData.assessmentDate && updatedFormData.assessmentHour) {
        const dateObj = new Date(updatedFormData.assessmentDate);
        const hourValue = parseInt(updatedFormData.assessmentHour, 10);

        dateObj.setHours(hourValue);
        dateObj.setMinutes(0);
        dateObj.setSeconds(0);
        dateObj.setMilliseconds(0);

        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getDate()).padStart(2, "0");
        const hours = String(hourValue).padStart(2, "0");

        const formattedDateTime = `${year}-${month}-${day}T${hours}:00:00`;

        setFormData(prevState => ({
          ...prevState,
          [name]: value,
          assessmentTime: formattedDateTime
        }));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { assessmentTime, contactPerson, remarks } = formData;

    if (!formData.assessmentDate || !formData.assessmentHour) {
      toast({
        title: "Missing information",
        description: "Please select both date and hour for the assessment time.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    if (!contactPerson || !remarks) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);
    
    const submissionData = {
      assessmentTime: formatDate(new Date(assessmentTime)),
      contactPerson,
      remarks
    };
    
    try {
      await axiosInstance.put(`/purpose/mature/${purposeId}`, submissionData);
      onSave();
      toast({
        title: "Lead matured.",
        description: "The lead has been updated successfully and is now in-progress.",
        status: "success",
        duration: 5000,
        isClosable: true,
      });
      handleClose();
    } catch (error) {
      if (error.response && error.response.status === 409) {
        toast({
          title: "Assessor not available",
          description: error.response.data.details || "The assessor is not available at the selected time. Please choose a different time.",
          status: "warning",
          duration: 5000,
          isClosable: true,
        });
      } else {
        toast({
          title: "An error occurred.",
          description: "Unable to update lead.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      const now = new Date();
      now.setHours(now.getHours() + 1);
      now.setMinutes(0);
      now.setSeconds(0);
      now.setMilliseconds(0);

      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const defaultDate = `${year}-${month}-${day}`;

      const defaultHour = now.getHours().toString();

      const formattedDateTime = `${defaultDate}T${String(now.getHours()).padStart(2, "0")}:00:00`;

      setFormData(prev => ({
        ...prev,
        assessmentDate: defaultDate,
        assessmentHour: defaultHour,
        assessmentTime: formattedDateTime
      }));
    }
  }, [isOpen]);

  const handleClose = () => {
    setFormData({
      assessmentTime: '',
      assessmentDate: '',
      assessmentHour: '',
      contactPerson: '',
      remarks: ''
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader style={{ background: "#2d6651", color: "white" }}>Mature Lead</ModalHeader>
        <ModalCloseButton style={{ color: "white" }} />
        <form onSubmit={handleSubmit}>
          <ModalBody width={"100%"}>
            <VStack spacing={4}>
              {[
                { label: "Contact Person", name: "contactPerson", type: "text" },
                { label: "Remarks", name: "remarks", type: "text" }
              ].map(({ label, name, type }) => (
                <FormControl key={name} isRequired>
                  <FormLabel>{label}</FormLabel>
                  <Input 
                    name={name} 
                    type={type} 
                    placeholder={`Enter ${label.toLowerCase()}`} 
                    value={formData[name]} 
                    onChange={handleChange} 
                  />
                </FormControl>
              ))}

              {/* Separate Date and Hour selectors */}
              <FormControl isRequired>
                <FormLabel>Assessment Date</FormLabel>
                <Input
                  name="assessmentDate"
                  type="date"
                  value={formData.assessmentDate}
                  onChange={handleChange}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Assessment Hour</FormLabel>
                <Select
                  name="assessmentHour"
                  value={formData.assessmentHour}
                  onChange={handleChange}
                  placeholder="Select hour"
                >
                  {Array.from({ length: 24 }, (_, i) => {
                    const hour = i;
                    const displayHour = hour < 10 ? `0${hour}:00` : `${hour}:00`;
                    const period = hour < 12 ? 'AM' : 'PM';
                    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                    const displayText = `${displayHour} (${hour12}:00 ${period})`;
                    return (
                      <option key={hour} value={hour}>
                        {displayText}
                      </option>
                    );
                  })}
                </Select>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} mr={3} type="submit" isLoading={isLoading}>Save</Button>
            <Button onClick={handleClose} colorScheme="red" isDisabled={isLoading}>Cancel</Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default MatureLeadModal;
