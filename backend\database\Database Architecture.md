# 📊 ImpexGrace ERP Database Architecture  

<p align="center">
  <img src="./database.svg" alt="Database Schema" width="100%">
</p>

🔗 [Open Full Schema](./db-schema.svg)

## 🏗️ Database Overview  
The ImpexGrace ERP system uses Microsoft SQL Server as its database engine and follows a comprehensive Chart of Accounts (COA) based financial management structure. The database is designed to handle complete business operations from lead generation to financial reporting.  

---

## 🔧 Technical Configuration  
### Database Connection  
- **Engine:** Microsoft SQL Server (MSSQL)  
- **Driver:** mssql npm package with tedious driver  
- **Connection Pool:** Max 10 connections, idle timeout 30 seconds  
- **Security:** Non-encrypted connections with arithmetic abort enabled  

### Environment Variables  

---

## 📋 Core Database Structure  

### 1. Chart of Accounts (COA) Hierarchy 🏦  
The system implements a 3-level Chart of Accounts structure:  

#### Level 1: COA3 (Account Types)  
- **Purpose:** Defines major account categories  
- **Key Field:** `atp2_ID` (Account Type Identifier)  
- **Common Types:**  
  - `CT` = Client/Customer accounts  
  - `SP` = Supplier accounts  
  - `CA` = Cash accounts  
  - `BA` = Bank accounts  

#### Level 2: COA32 (Main Accounts)  
- **Purpose:** Individual account records  
- **Features:** Opening balances, group classifications, location-based accounts  
- **Key Fields:** `id`, `title`, `Opn_Amt`, `GroupID`, `Location`  

#### Level 3: COA321 (Account Details)  
- **Purpose:** Extended contact and address information  
- **Contains:** Names, addresses, phone numbers, emails, URLs  

#### Items/Inventory: COA31  
- **Purpose:** Product and service catalog  
- **Features:** Units, rates, categories, purchase/sale account linking  
- **Key Fields:** `Sale_Rate`, `Purc_Rate`, `Category_ID`, `Unit`  

---

### 2. Financial Voucher System 💰  
#### AC Table (Voucher Headers)  
- **Purpose:** Main voucher records for all financial transactions  
- **Voucher Types (vtp):**  
  - `CR` = Cash Receipt  
  - `CP` = Cash Payment  
  - `BR` = Bank Receipt  
  - `BP` = Bank Payment  
- **Structure:** `VTP/MNTH/LOCATION/VNO` (e.g., `CR/2024/EAM/001`)  

#### AC_DET Table (Voucher Details)  
- **Purpose:** Line items for each voucher  
- **Key Features:**  
  - Links to accounts via `acc_id`  
  - References other vouchers via `adj_VoucherNo`  
  - Supports employee assignments  
  - Tracks amounts and narrations  

---

### 3. Business Operations 🏢  
#### Offer System (Quotations/Proposals)  
- **Offer:** Main quotation header  
- **Offer_det:** Detailed line items with quantities, rates, amounts  
- **Features:** Multi-level approval workflow, project/cost center tracking  

#### Client Orders (CO)  
- **CO:** Client order headers  
- **CO_det:** Order line items  
- **Integration:** Links to delivery and installation personnel  

#### Delivery Challan (DC)  
- **Purpose:** Goods delivery documentation  
- **Features:** Godown management, approval workflows  

---

### 4. User Management & Security 👥  
#### Users Table  
- **Fields:** `id`, `user_name`, `EmailAddress`, `RoleID`  
- **Integration:** Links to employee details and role-based permissions  

#### User_Role Table  
- **Roles:**  
  - 1 = Admin  
  - 3 = Assessor  
  - 4 = Delivery Personnel  
  - 5 = Installer  

#### EmployeeDetails  
- **Purpose:** Complete employee information  
- **Links:** Designations, departments, shifts, cost centers, projects  

---

### 5. Master Data Management 📚  
#### Reference Tables:  
- **Categories:** Product categorization  
- **CostCenters:** Cost center definitions  
- **DefProjects:** Project master data  
- **Godown:** Warehouse/location management  
- **DefJobs:** Job definitions  
- **DefClientItemCategory/Type:** Client-specific item classifications  

---

### 6. Business Process Management 🔄  
#### ClientPurpose  
- **Purpose:** Tracks delivery and installation schedules  
- **Features:** Time update notifications, reason tracking  
- **Links:** References offer vouchers, assigns personnel  

#### RecoveryFollowUps  
- **Purpose:** Customer follow-up management  
- **Features:** Follow-up scheduling, comments tracking  

---

### 7. Notification System 🔔  
#### Modern Notification Architecture:  
- **NotificationTypes:** Defines notification categories  
- **NotificationTemplates:** Template-based messaging  
- **Notifications:** Individual notification instances  
- **NotificationRecipients:** Multi-target delivery (users, roles, employees)  

---

## 🔗 Key Relationships & Data Flow  
### Financial Flow:  
- Offer → AC_DET (via `adj_VoucherNo`)  
- AC ← AC_DET (voucher header ↔ details)  
- COA32 ← AC_DET (account assignments)  

### Business Process Flow:  
- Offer → ClientPurpose → CO → DC  
- Users → EmployeeDetails → Process Assignments  

### Payment Tracking:  
- **Unified Logic:** Combines cash (CR) and bank (BR/BP) payments  
- **Status Calculation:**  
  - Pending = No payments  
  - Partial = Some payments made  
  - Completed = Fully paid  

---

## 🚀 Advanced Features  
1. **Multi-Location Support**  
   - Location-based account segregation  
   - Cross-location reporting capabilities  

2. **Approval Workflows**  
   - Three-level approval: Prepared → Checked → Approved  
   - User tracking for each approval level  

3. **Real-time Notifications**  
   - Template-based messaging system  
   - Role-based and user-specific targeting  
   - Read/unread status tracking  

4. **Comprehensive Reporting**  
   - Unified voucher reports combining multiple payment methods  
   - Financial status tracking with real-time calculations  
   - Search and filtering capabilities  

---

## 💡 Database Design Strengths  
- **Flexibility:** COA structure supports diverse business needs  
- **Scalability:** Proper indexing and relationship design  
- **Auditability:** Complete transaction tracking and approval trails  
- **Integration:** Seamless flow between financial and operational modules  
- **Modern Features:** Notification system, real-time updates, search functionality  

---

This database architecture provides a solid foundation for a comprehensive ERP system, supporting everything from basic accounting to complex business process management with modern notification and reporting capabilities.  
