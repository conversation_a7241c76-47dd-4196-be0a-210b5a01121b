import { IconType } from 'react-icons';
import { FiHome, FiCompass, FiUploadCloud, FiUserPlus, FiBox, FiTrendingUp, FiStar, FiSettings, FiUsers } from 'react-icons/fi';
import { BiStore } from 'react-icons/bi';
import { FaRegMoneyBillAlt } from 'react-icons/fa';
import { TbDashboard, TbBrandPaypal } from 'react-icons/tb';
import { TfiMenuAlt } from 'react-icons/tfi';
import { IoMdContact } from 'react-icons/io';

interface LinkItemProps {
    name: string;
    icon: IconType;
    path?: string;
    subItems?: Array<LinkItemProps>; // For dropdown items
    href?: string;
    children?: Array<{
        name: string;
        href: string;
    }>;
}

export const AdminLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
    {
        name: 'Business Leads',
        icon: IoMdContact,
        path: '/forms/business-leads'
    },
    {
        name: 'Tasks',
        icon: FiUsers,
        path: '/tasks'
    },
    {
        name: 'Main Definitions',
        icon: FiUploadCloud,
        subItems: [
            { name: 'Define Employees', icon: FiUserPlus, path: '/forms/define-employees' },
            { name: 'Define Products', icon: FiBox, path: '/forms/define-products' },
            { name: 'Define Godown', icon: BiStore, path: '/forms/godown' },
            { name: 'Employee List', icon: FiUsers, path: '/forms/employee-list' },
        ]
    },
    {
        name: 'Financials',
        icon: TbBrandPaypal,
        subItems: [
            { name: 'Cash Receipt', icon: FiHome, path: '/forms/cash-receipt-voucher' },
            // { name: 'Cash Payment', icon: FiHome, path: '/forms/cash-payment-voucher' },
            { name: 'Bank Receipt', icon: FiHome, path: '/forms/bank-receipt-voucher' },
            // { name: 'Bank Payment', icon: FiHome, path: '/forms/bank-payment-voucher' },
            // { name: "Standards - Tax Deduction Policy", icon: MdPolicy, path: '/forms/StandardsTaxDeductionPolicy' },
            // { name: "Appointment Letter", icon: PiVideoConferenceFill, path: '/forms/AppointmentLetter' },
        ]
    },
    {
        name: 'Reports',
        icon: FaRegMoneyBillAlt,
        subItems: [
            { name: 'Assessors Report', icon: TfiMenuAlt, path: '/forms/assessors-report' },
            { name: 'Audit Report', icon: TfiMenuAlt, path: '/forms/audit-report' },
            { name: 'Delivery Report', icon: TfiMenuAlt, path: '/forms/delivery-report' },
            { name: 'Installer Report', icon: TfiMenuAlt, path: '/forms/installer-report' },
            { name: 'Payment Receipts', icon: TfiMenuAlt, path: '/forms/payment-receipts' },
        ]
    },
];

export const UserLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
    {
        name: 'Business Leads',
        icon: IoMdContact,
        path: '/forms/business-leads'
    },
    {
        name: 'Tasks',
        icon: FiUsers,
        path: '/tasks'
    },
    {
        name: 'Main Definitions',
        icon: FiUploadCloud,
        subItems: [
            { name: 'Define Employees', icon: FiUserPlus, path: '/forms/define-employees' },
            { name: 'Define Products', icon: FiBox, path: '/forms/define-products' },
        ]
    },
    {
        name: 'Financials',
        icon: TbBrandPaypal,
        subItems: [
            { name: 'Cash Receipt', icon: FiHome, path: '/forms/cash-receipt-voucher' },
            // { name: 'Cash Payment', icon: FiHome, path: '/forms/cash-payment-voucher' },
            { name: 'Bank Receipt', icon: FiHome, path: '/forms/bank-receipt-voucher' },
            // { name: 'Bank Payment', icon: FiHome, path: '/forms/bank-payment-voucher' },
            // { name: "Standards - Tax Deduction Policy", icon: MdPolicy, path: '/forms/StandardsTaxDeductionPolicy' },
            // { name: "Appointment Letter", icon: PiVideoConferenceFill, path: '/forms/AppointmentLetter' },
        ]
    },
    {
        name: 'Reports',
        icon: FaRegMoneyBillAlt,
        subItems: [
            { name: 'Assessors Report', icon: TfiMenuAlt, path: '/forms/assessors-report' },
            { name: 'Audit Report', icon: TfiMenuAlt, path: '/forms/audit-report' },
            { name: 'Delivery Report', icon: TfiMenuAlt, path: '/forms/delivery-report' },
            { name: 'Installer Report', icon: TfiMenuAlt, path: '/forms/installer-report' },
            { name: 'Payment Receipts', icon: TfiMenuAlt, path: '/forms/payment-receipts' },
        ]
    },
];

export const AssesserLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
    {
        name: 'Business Leads',
        icon: IoMdContact,
        path: '/forms/business-leads'
    },
];

export const InstallerLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
];

export const DeliveryLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
];
