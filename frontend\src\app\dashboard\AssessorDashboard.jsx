"use client";
import React, { useState, useEffect } from 'react';
import Sidebar from '@components/sidebar/sidebar';
import "@src/app/dashboard/dashboard.css";
import { useUser } from '@src/app/provider/UserContext';
import { capitalizeWords } from '@src/app/utils/functions';
import { Box, ButtonGroup, Button, Flex, Icon, useToast, Tooltip, IconButton } from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import ReportTable from '@src/components/Custom/ReportTable';
import { Calendar, momentLocalizer, Views } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import moment from 'moment';
import TimeUpdateModal from '@src/components/Custom/TimeUpdateModal/TimeUpdateModal';
import { FaCalendarAlt, Fa<PERSON>ist, FaChevronLeft, FaChevronRight, FaArrowUp, FaEye } from 'react-icons/fa';
import { FiClock } from 'react-icons/fi';
import MatureLeadModal from './MatureLeadModal';
import ViewLeadDetailsModal from './ViewLeadDetailsModal';
import { getData } from '@src/app/utils/functions';

const AssessorDashboard = () => {
    const router = useRouter();
    const { user, userRole } = useUser();
    const toast = useToast();
    const [tasks, setTasks] = useState([]);
    const [viewMode, setViewMode] = useState('calendar'); // 'list' or 'calendar'
    const [calendarView, setCalendarView] = useState('month'); // 'month', 'week', 'day'
    const [currentDate, setCurrentDate] = useState(new Date());
    const [isMatureLeadModalOpen, setIsMatureLeadModalOpen] = useState(false);
    const [selectedPurposeId, setSelectedPurposeId] = useState(null);
    const [isViewLeadDetailsModalOpen, setIsViewLeadDetailsModalOpen] = useState(false);
    const [selectedClientData, setSelectedClientData] = useState(null);
    const [clientDetailsCache, setClientDetailsCache] = useState({});
    const [isTimeUpdateModalOpen, setIsTimeUpdateModalOpen] = useState(false);
    const [selectedTaskForTimeUpdate, setSelectedTaskForTimeUpdate] = useState(null);
    const localizer = momentLocalizer(moment);

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("purpose/assessor");
            if (Array.isArray(data.data) && data.data.length > 0) {
                setTasks(data.data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    // Convert tasks to calendar events
    const calendarEvents = tasks.map(task => ({
        id: task.ID,
        title: `${task.clientTitle || 'Client'}`,
        start: new Date(task.assessmentTime),
        end: new Date(moment(task.assessmentTime).add(1, 'hours').toDate()),
        resource: task
    }));

    // Custom calendar toolbar component
    const CustomToolbar = (toolbar) => {
        const goToToday = () => {
            toolbar.onNavigate('TODAY');
            setCurrentDate(new Date());
        };

        const goToPrev = () => {
            toolbar.onNavigate('PREV');
            setCurrentDate(prevDate => {
                const newDate = new Date(prevDate);
                if (calendarView === 'month') {
                    newDate.setMonth(newDate.getMonth());
                } else if (calendarView === 'week') {
                    newDate.setDate(newDate.getDate());
                } else if (calendarView === 'day') {
                    newDate.setDate(newDate.getDate());
                }
                return newDate;
            });
        };

        const goToNext = () => {
            toolbar.onNavigate('NEXT');
            setCurrentDate(prevDate => {
                const newDate = new Date(prevDate);
                console.log(newDate);
                if (calendarView === 'month') {
                    newDate.setMonth(newDate.getMonth());
                } else if (calendarView === 'week') {
                    newDate.setDate(newDate.getDate());
                } else if (calendarView === 'day') {
                    newDate.setDate(newDate.getDate());
                }
                return newDate;
            });
        };

        const label = () => {
            const date = moment(toolbar.date);
            if (calendarView === 'month') {
                return date.format('MMMM YYYY');
            } else if (calendarView === 'week') {
                const start = date.startOf('week').format('MMM D');
                const end = date.endOf('week').format('MMM D, YYYY');
                return `${start} - ${end}`;
            } else if (calendarView === 'day') {
                return date.format('dddd, MMMM D, YYYY');
            } else {
                return date.format('MMMM YYYY');
            }
        };

        return (
            <Flex className="rbc-toolbar" justifyContent="space-between !important" alignItems="center" p={2}>
                <Flex gap={2}>
                    <Button size="sm" onClick={goToToday} mr={2}>Today</Button>
                    <ButtonGroup size="sm" isAttached>
                        <Button onClick={goToPrev}><Icon as={FaChevronLeft} /></Button>
                        <Button onClick={goToNext}><Icon as={FaChevronRight} /></Button>
                    </ButtonGroup>
                </Flex>
                <Box fontWeight="bold">{label()}</Box>
                <ButtonGroup size="sm" isAttached>
                    <Button
                        onClick={() => { toolbar.onView('month'); setCalendarView('month'); }}
                        colorScheme={calendarView === 'month' ? 'blue' : 'gray'}
                    >
                        Month
                    </Button>
                    <Button
                        onClick={() => { toolbar.onView('week'); setCalendarView('week'); }}
                        colorScheme={calendarView === 'week' ? 'blue' : 'gray'}
                    >
                        Week
                    </Button>
                    <Button
                        onClick={() => { toolbar.onView('day'); setCalendarView('day'); }}
                        colorScheme={calendarView === 'day' ? 'blue' : 'gray'}
                    >
                        Day
                    </Button>
                </ButtonGroup>
            </Flex>
        );
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'in-progress': return 'blue';
            case 'completed': return 'green';
            default: return 'gray';
        }
    };

    const getAuditStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'audited': return 'green';
            default: return 'gray';
        }
    };

    const getAuditStatus = (task) => {
        if (task.IsApproved === null) return 'Pending';
        if (task.IsApproved === false) return 'Rejected';
        if (task.IsApproved === true) return 'Audited';
        return 'Pending';
    };

    const handleTimeUpdate = (task) => {
        setSelectedTaskForTimeUpdate(task);
        setIsTimeUpdateModalOpen(true);
    };

    const handleTimeUpdateSave = () => {
        fetchTasks();
        setIsTimeUpdateModalOpen(false);
        setSelectedTaskForTimeUpdate(null);
    };

    const columns = [
        { header: '#', field: 'ID' },
        { header: 'Lead Name', field: 'clientTitle' },
        { header: 'Assessment Date', field: 'assessmentTime', type: 'date' },
        { header: 'Status', field: 'status', type: 'badge' },
        {
            header: 'Audit Status',
            field: 'auditStatus',
            type: 'badge',
            render: (task) => ({
                label: getAuditStatus(task),
                color: getAuditStatusColor(getAuditStatus(task))
            })
        },
        {
            header: 'Actions',
            field: 'actions',
            render: (task) => {
                return (
                    <Flex gap={2}>
                        {task.status === 'pending' && <Tooltip label="Mature Lead" hasArrow>
                            <IconButton
                                size="sm"
                                bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                                icon={<Icon as={FaArrowUp} />}
                                isDisabled={task.status !== 'pending'}
                                onClick={(e) => handleMatureLeadClick(task, e)}
                                aria-label="Mature Lead"
                            />
                        </Tooltip>}
                        {(task.status === 'in-progress' || task.status === 'completed') && (
                            <Tooltip label="Update Time" hasArrow>
                                <IconButton
                                    icon={<FiClock />}
                                    size="sm"
                                    bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                                    variant="outline"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleTimeUpdate(task);
                                    }}
                                    aria-label="Update Time"
                                />
                            </Tooltip>
                        )}
                        <Tooltip label="View Lead Details" hasArrow>
                            <IconButton
                                size="sm"
                                colorScheme="green"
                                icon={<Icon as={FaEye} />}
                                onClick={(e) => handleViewLeadDetailsClick(task, e)}
                                aria-label="View Lead Details"
                            />
                        </Tooltip>
                    </Flex>
                );

            }
        }
    ];

    const handleRowClick = (task) => {
        if (task.status === 'pending') return;
        router.push(`/follow-up?purpose_no=${task.ID}`);
    };

    const handleMatureLeadClick = (task, e) => {
        e.stopPropagation();
        setSelectedPurposeId(task.ID);
        setIsMatureLeadModalOpen(true);
    };

    const handleMatureLeadSave = () => {
        fetchTasks();
        setIsMatureLeadModalOpen(false);
        toast({
            title: "Lead matured",
            description: "The lead has been successfully matured and is now in-progress.",
            status: "success",
            duration: 5000,
            isClosable: true,
        });
    };

    const handleViewLeadDetailsClick = async (task, e) => {
        e.stopPropagation();

        if (clientDetailsCache[task.clientID]) {
            setSelectedClientData(clientDetailsCache[task.clientID]);
            setIsViewLeadDetailsModalOpen(true);
            return;
        }

        try {
            const { data: clientData } = await getData("client/" + task.clientID);

            if (clientData) {
                const data = {
                    name: clientData.title,
                    phone: clientData.tel,
                    email: clientData.email,
                    website: clientData.url,
                    portallink: clientData.url,
                    mobile: clientData.Mobile,
                    tags: clientData.tags,
                    shipping: clientData.add1,
                    currency: 'AUD',
                    id: clientData.id
                };

                setClientDetailsCache(prev => ({
                    ...prev,
                    [task.clientID]: data
                }));

                setSelectedClientData(data);
                setIsViewLeadDetailsModalOpen(true);
            }
        } catch (error) {
            console.error("Error fetching client details:", error);
            toast({
                title: "Error",
                description: "Failed to fetch client details. Please try again.",
                status: "error",
                duration: 5000,
                isClosable: true,
            });
        }
    };

    return (
        <>
            <div className="wrapper">
                <div>
                    <div className="container">
                        <div className="page-inner">
                            <div className="row">
                                <div className="col-md-12">
                                    {/* Welcome Card */}
                                    <div className="card card-round">
                                        <div
                                            className="card-header p-4"
                                            style={{
                                                display: "flex",
                                                flexDirection: "column",
                                                gap: "10px",
                                            }}
                                        >
                                            <p style={{ fontWeight: "500", fontSize: "20px" }}>
                                                Welcome to NexSol&apos;s ERP, {user?.userName ? capitalizeWords(user.userName) : 'Anonymous'} as {userRole}
                                            </p>
                                            <p
                                                style={{
                                                    fontSize: "12px",
                                                    fontWeight: "300",
                                                    fontFamily: "'__Inter_d65c78', '__Inter_Fallback_d65c78'",
                                                }}
                                            >
                                                Discover your tailored ERP application! Every feature is
                                                designed to show how NexSol streamlines your daily operations,
                                                enhances business efficiency, and eliminates the manual
                                                processes that slow you down.
                                            </p>
                                        </div>
                                    </div>

                                    {/* Tasks Section */}
                                    <Box className="card card-round mt-4">
                                        <Box className="card-header p-3" display="flex" justifyContent="space-between" alignItems="center">
                                            <h4>Assigned Tasks</h4>
                                            <ButtonGroup size="sm" isAttached variant="outline">
                                                <Button
                                                    leftIcon={<Icon as={FaList} />}
                                                    colorScheme={viewMode === 'list' ? 'blue' : 'gray'}
                                                    onClick={() => setViewMode('list')}
                                                >
                                                    List
                                                </Button>
                                                <Button
                                                    leftIcon={<Icon as={FaCalendarAlt} />}
                                                    colorScheme={viewMode === 'calendar' ? 'blue' : 'gray'}
                                                    onClick={() => setViewMode('calendar')}
                                                >
                                                    Calendar
                                                </Button>
                                            </ButtonGroup>
                                        </Box>

                                        {viewMode === 'list' ? (
                                            <ReportTable
                                                data={tasks}
                                                columns={columns}
                                                onRowClick={handleRowClick}
                                                getRowCursor={(task) => task.status !== 'pending' ? 'pointer' : 'default'}
                                                getBadgeColor={getStatusColor}
                                                dateField="assessmentTime"
                                                showDateFilter={false}
                                            />
                                        ) : (
                                            <Box p={4} height="600px">
                                                <Calendar
                                                    localizer={localizer}
                                                    events={calendarEvents}
                                                    startAccessor="start"
                                                    endAccessor="end"
                                                    style={{ height: '100%' }}
                                                    onSelectEvent={(event) => handleRowClick(event.resource)}
                                                    eventPropGetter={(event) => ({
                                                        style: {
                                                            backgroundColor: getStatusColor(event.resource.status),
                                                            borderRadius: '4px',
                                                            color: '#fff',
                                                            border: 'none'
                                                        }
                                                    })}
                                                    date={currentDate}
                                                    onNavigate={date => setCurrentDate(date)}
                                                    view={calendarView}
                                                    onView={view => setCalendarView(view)}
                                                    components={{
                                                        toolbar: CustomToolbar
                                                    }}
                                                    views={['month', 'week', 'day']}
                                                    popup={true}
                                                    selectable={true}
                                                    showMultiDayTimes={true}
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <MatureLeadModal
                isOpen={isMatureLeadModalOpen}
                onClose={() => setIsMatureLeadModalOpen(false)}
                onSave={handleMatureLeadSave}
                purposeId={selectedPurposeId}
            />

            <ViewLeadDetailsModal
                isOpen={isViewLeadDetailsModalOpen}
                onClose={() => setIsViewLeadDetailsModalOpen(false)}
                clientData={selectedClientData}
            />

            <TimeUpdateModal
                isOpen={isTimeUpdateModalOpen}
                onClose={() => {
                    setIsTimeUpdateModalOpen(false);
                    setSelectedTaskForTimeUpdate(null);
                }}
                onSave={handleTimeUpdateSave}
                taskData={selectedTaskForTimeUpdate}
                role="assessor"
            />
        </>
    )
}

export default AssessorDashboard;