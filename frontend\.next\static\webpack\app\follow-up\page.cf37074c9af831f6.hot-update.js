"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/components/PrintModal.jsx":
/*!***************************************!*\
  !*** ./src/components/PrintModal.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-to-print */ \"(app-pages-browser)/./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PrintModal = (param)=>{\n    let { isOpen, onClose, children, formName = \"Form Name\", showHeader = true, buttonText = \"Print\", callback } = param;\n    _s();\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const userLocation = (user === null || user === void 0 ? void 0 : user.Location) || (user === null || user === void 0 ? void 0 : user.location) || \"\";\n    const logoSrc = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat(userLocation, \"/logo?fallback=true\");\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint)({\n        contentRef: componentRef\n    });\n    // Helper function to convert image URL to base64\n    const convertImageToBase64 = async (url)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = ()=>resolve(reader.result);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        } catch (error) {\n            console.warn(\"Failed to convert image to base64:\", error);\n            return null;\n        }\n    };\n    // Helper function to wait for all images to load\n    const waitForImages = async (element)=>{\n        const images = element.querySelectorAll(\"img\");\n        const imagePromises = Array.from(images).map(async (img)=>{\n            if (img.complete) return;\n            return new Promise((resolve, reject)=>{\n                const timeout = setTimeout(()=>{\n                    console.warn(\"Image load timeout:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                }, 5000);\n                img.onload = ()=>{\n                    clearTimeout(timeout);\n                    resolve();\n                };\n                img.onerror = ()=>{\n                    clearTimeout(timeout);\n                    console.warn(\"Image load error:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                };\n            });\n        });\n        await Promise.all(imagePromises);\n    };\n    const handleGeneratePDF = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            // Wait for all images to load\n            await waitForImages(componentRef.current);\n            // Convert external images to base64 to avoid CORS issues\n            const images = componentRef.current.querySelectorAll(\"img\");\n            for (const img of images){\n                if (img.src.startsWith(\"http\") && !img.src.includes(\"data:\")) {\n                    try {\n                        const base64 = await convertImageToBase64(img.src);\n                        if (base64) {\n                            img.src = base64;\n                        }\n                    } catch (error) {\n                        console.warn(\"Failed to convert image:\", img.src, error);\n                    }\n                }\n            }\n            // Small delay to ensure DOM updates\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                onclone: (clonedDoc)=>{\n                    // Ensure all images in the cloned document are properly loaded\n                    const clonedImages = clonedDoc.querySelectorAll(\"img\");\n                    clonedImages.forEach((img)=>{\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                    });\n                }\n            });\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"](\"p\", \"mm\", \"a4\");\n            // Calculate dimensions to fit the page properly\n            const pdfWidth = pdf.internal.pageSize.getWidth();\n            const pdfHeight = pdf.internal.pageSize.getHeight();\n            const canvasAspectRatio = canvas.height / canvas.width;\n            const pdfAspectRatio = pdfHeight / pdfWidth;\n            let imgWidth, imgHeight;\n            if (canvasAspectRatio > pdfAspectRatio) {\n                imgHeight = pdfHeight - 20; // 10mm margin on top and bottom\n                imgWidth = imgHeight / canvasAspectRatio;\n            } else {\n                imgWidth = pdfWidth - 20; // 10mm margin on left and right\n                imgHeight = imgWidth * canvasAspectRatio;\n            }\n            const x = (pdfWidth - imgWidth) / 2;\n            const y = (pdfHeight - imgHeight) / 2;\n            pdf.addImage(imgData, \"PNG\", x, y, imgWidth, imgHeight);\n            const pdfBlob = pdf.output(\"blob\");\n            if (callback) {\n                callback(pdfBlob);\n            } else {\n                handlePrint();\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            toast({\n                title: \"Error generating PDF\",\n                description: \"Please try again or contact support if the issue persists.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalContent, {\n                height: \"100vh\",\n                width: \"auto\",\n                overflow: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalHeader, {\n                        children: \"Print Preview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalBody, {\n                        maxHeight: \"90vh\",\n                        overflow: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Box, {\n                            ref: componentRef,\n                            minWidth: \"700px\",\n                            className: \"print-container\",\n                            paddingTop: 12,\n                            children: [\n                                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"center\",\n                                                alignItems: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                alt: \"logo\",\n                                                src: logoSrc,\n                                                style: {\n                                                    height: \"70px\",\n                                                    width: \"auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            fontSize: \"xl\",\n                                            fontStyle: \"italic\",\n                                            textAlign: \"center\",\n                                            margin: \"20px 0 20px 0 !important\",\n                                            children: formName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                children\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalFooter, {\n                        gap: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: handleGeneratePDF,\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrintModal, \"GEIWinyei48BORxa3W6Bbzad+uA=\", false, function() {\n    return [\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint\n    ];\n});\n_c = PrintModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PrintModal);\nvar _c;\n$RefreshReg$(_c, \"PrintModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PrintModal.jsx\n"));

/***/ })

});