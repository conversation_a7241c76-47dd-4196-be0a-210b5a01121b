"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    _s();\n    const [logoSrc, setLogoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Preload the logo image to ensure it's available for PDF generation\n        const img = new next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            setLogoSrc(logoImage);\n        };\n        img.onerror = ()=>{\n            console.warn(\"Failed to load logo image:\", logoImage);\n            setLogoSrc(\"\"); // Set empty to hide broken image\n        };\n        img.src = logoImage;\n    }, [\n        logoImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        p: 8,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: logoSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: logoSrc,\n                            alt: \"Eco Assets Manager\",\n                            style: {\n                                height: \"100px\",\n                                width: \"auto\"\n                            },\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src || _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src || _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponentToPrint, \"bQCJrah25Bx45JT2l5QhtQPkQ6I=\");\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});