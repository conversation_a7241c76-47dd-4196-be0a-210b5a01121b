"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/signature_pad";
exports.ids = ["vendor-chunks/signature_pad"];
exports.modules = {

/***/ "(ssr)/./node_modules/signature_pad/dist/signature_pad.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/signature_pad/dist/signature_pad.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 Szymon Nowak\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width Bézier curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\n\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\n\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\n\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\n\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\n\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\n\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\n\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\n\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n        curve = _addPoint.curve,\n        widths = _addPoint.widths;\n\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\n\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\n\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\n\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\n\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\n\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n\n  points.push(point);\n\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n\n    return { curve: curve, widths: widths };\n  }\n\n  return {};\n};\n\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n\n  var m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n  var m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n\n  var k = l2 / (l1 + l2);\n  var cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\n\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = { start: null, end: null };\n\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n  var newWidth = this._strokeWidth(velocity);\n\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n\n  return widths;\n};\n\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\n\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n\n  ctx.beginPath();\n\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n              curve = _addPoint2.curve,\n              widths = _addPoint2.widths;\n\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\n\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n\n    svg.appendChild(circle);\n  });\n\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n\n    body = dummy.innerHTML;\n  }\n\n  var footer = '</svg>';\n  var data = header + body + footer;\n\n  return prefix + btoa(data);\n};\n\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n\n  this.clear();\n\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n\n  this._data = pointGroups;\n};\n\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SignaturePad);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/signature_pad/dist/signature_pad.mjs\n");

/***/ })

};
;