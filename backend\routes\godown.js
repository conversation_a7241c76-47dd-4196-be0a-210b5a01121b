const express = require('express');
const router = express.Router();
const godownController = require('../controllers/godownController');
const authorization = require('../middleware/authorization');
const multer = require('multer');
const cors = require('cors');
const godownValidationRules = require('../validations/godownValidation');

const upload = multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 5 * 1024 * 1024 },
    fileFilter: (req, file, cb) => {
        if (!file) return cb(null, true);
        if (file.mimetype && file.mimetype.startsWith('image/')) return cb(null, true);
        return cb(new Error('Only image files are allowed'));
    }
});

// Public logo fetch route (no auth header possible from <img>) with permissive CORS
router.get(
    '/:id/logo',
    cors({ origin: true, credentials: false }),
    godownController.getLogo
);

router.use(authorization);

// POST /api/godown/create
/**
 * @swagger
 * /api/godown/create:
 *   post:
 *     summary: Create a new godown (location)
 *     tags: [Godown]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Id:
 *                 type: string
 *               Title:
 *                 type: string
 *               PH:
 *                 type: integer
 *               CompanyAddress1:
 *                 type: string
 *               CompanyAddress2:
 *                 type: string
 *               CompanyPhone:
 *                 type: string
 *               CompanyFax:
 *                 type: string
 *               CompanyEmail:
 *                 type: string
 *               CompanyURL:
 *                 type: string
 *               CompanySTN:
 *                 type: string
 *               CompanyNTN:
 *                 type: string
 *               AllowNegativeBalances:
 *                 type: integer
 *               Deactive:
 *                 type: integer
 *               Prp_ID:
 *                 type: string
 *               chk_id:
 *                 type: string
 *               app_id:
 *                 type: string
 *               FinancialAcc_ID:
 *                 type: string
 *               ComputerName:
 *                 type: string
 *     responses:
 *       201:
 *         description: Godown created successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/create', godownValidationRules.create, godownController.createGodown);

// GET /api/godown/next-id
/**
 * @swagger
 * /api/godown/next-id:
 *   get:
 *     summary: Get next Godown Id
 *     tags: [Godown]
 *     responses:
 *       200:
 *         description: Next Id
 *       500:
 *         description: Server error
 */
router.get('/next-id', godownController.getNextGodownId);

// GET /api/godown
/**
 * @swagger
 * /api/godown:
 *   get:
 *     summary: List godowns
 *     tags: [Godown]
 *     responses:
 *       200:
 *         description: List of godowns
 *       500:
 *         description: Server error
 */
router.get('/', godownController.getAllGodowns);

// GET /api/godown/:id
/**
 * @swagger
 * /api/godown/{id}:
 *   get:
 *     summary: Get godown by Id
 *     tags: [Godown]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Godown object
 *       404:
 *         description: Not found
 *       500:
 *         description: Server error
 */
router.get('/:id', godownController.getGodownById);

// PUT /api/godown/:id
/**
 * @swagger
 * /api/godown/{id}:
 *   put:
 *     summary: Update godown
 *     tags: [Godown]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Updated
 *       400:
 *         description: Validation error
 *       404:
 *         description: Not found
 *       500:
 *         description: Server error
 */
router.put('/:id', godownValidationRules.update, godownController.updateGodown);

// POST /api/godown/upload-logo (multipart/form-data: image, godownId)
/**
 * @swagger
 * /api/godown/upload-logo:
 *   post:
 *     summary: Upload or replace a godown logo
 *     tags: [Godown]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *               godownId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Uploaded
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.post('/upload-logo', upload.single('image'), godownValidationRules.uploadLogo, godownController.uploadLogo);

// Note: /:id/logo is intentionally public to allow img tag access

module.exports = router;


