"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        p: 8,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            src: logoImage,\n                            alt: \"Eco Assets Manager\",\n                            height: \"100px\",\n                            width: \"auto\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});