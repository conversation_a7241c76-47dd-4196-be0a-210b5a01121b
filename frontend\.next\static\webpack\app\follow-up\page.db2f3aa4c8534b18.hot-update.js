"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    _s();\n    const [logoSrc, setLogoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Preload the logo image to ensure it's available for PDF generation\n        const img = new next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            setLogoSrc(logoImage);\n        };\n        img.onerror = ()=>{\n            console.warn(\"Failed to load logo image:\", logoImage);\n            setLogoSrc(\"\"); // Set empty to hide broken image\n        };\n        img.src = logoImage;\n    }, [\n        logoImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        p: 8,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: logoSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: logoSrc,\n                            alt: \"Eco Assets Manager\",\n                            style: {\n                                height: \"100px\",\n                                width: \"auto\"\n                            },\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src || _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src || _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    },\n                                    crossOrigin: \"anonymous\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponentToPrint, \"bQCJrah25Bx45JT2l5QhtQPkQ6I=\");\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZm9sbG93LXVwL0NvbXBvbmVudFRvUHJpbnQuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtRDtBQUMrQztBQUN4RTtBQUMyQjtBQUNPO0FBQzdCO0FBRS9CLE1BQU1rQixtQkFBbUI7UUFBQyxFQUFFQyxJQUFJLEVBQUU7UUFpRXZCQTs7SUFoRVQsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNcUIsWUFBWSxHQUFxRkgsT0FBbEZJLEtBQWdDLEdBQUdBLDRCQUFnQyxHQUFHLEdBQUcsV0FBaUMsT0FBeEJKLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTU8sUUFBUSxLQUFJLE9BQU07SUFFL0h4QixnREFBU0EsQ0FBQztRQUNSLHFFQUFxRTtRQUNyRSxNQUFNeUIsTUFBTSxJQUFJVixrREFBS0E7UUFDckJVLElBQUlDLFdBQVcsR0FBRztRQUNsQkQsSUFBSUUsTUFBTSxHQUFHO1lBQ1hSLFdBQVdDO1FBQ2I7UUFDQUssSUFBSUcsT0FBTyxHQUFHO1lBQ1pDLFFBQVFDLElBQUksQ0FBQyw4QkFBOEJWO1lBQzNDRCxXQUFXLEtBQUssaUNBQWlDO1FBQ25EO1FBQ0FNLElBQUlNLEdBQUcsR0FBR1g7SUFDWixHQUFHO1FBQUNBO0tBQVU7SUFDZCxxQkFDRSw4REFBQ25CLGlEQUFHQTtRQUFDK0IsR0FBRztRQUFHQyxNQUFLO1FBQVFDLElBQUc7UUFBT0MsSUFBRzs7MEJBRW5DLDhEQUFDMUIsa0RBQUlBO2dCQUFDMkIsU0FBUTtnQkFBZ0JDLE9BQU07Z0JBQVNDLElBQUk7Z0JBQUdDLGNBQWE7Z0JBQVlDLGFBQVk7Z0JBQVdDLElBQUk7O2tDQUN0Ryw4REFBQ3hDLGlEQUFHQTtrQ0FDRGlCLHlCQUNDLDhEQUFDTzs0QkFDQ00sS0FBS2I7NEJBQ0x3QixLQUFJOzRCQUNKQyxPQUFPO2dDQUFFQyxRQUFRO2dDQUFTQyxPQUFPOzRCQUFPOzRCQUN4Q25CLGFBQVk7Ozs7Ozs7Ozs7O2tDQUlsQiw4REFBQ3pCLGlEQUFHQTt3QkFBQzZDLFdBQVU7OzBDQUNiLDhEQUFDNUMsa0RBQUlBO2dDQUFDNkMsVUFBUztnQ0FBTUMsWUFBVztnQ0FBT0MsT0FBTTswQ0FBUTs7Ozs7OzBDQUNyRCw4REFBQy9DLGtEQUFJQTtnQ0FBQytDLE9BQU07Z0NBQVVELFlBQVc7Z0NBQU9ELFVBQVM7O29DQUFNOUIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxRQUFRLEtBQUk7b0NBQU07b0NBQUVQLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlDLEdBQUcsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLbEcsOERBQUN2QyxrREFBSUE7Z0JBQUN3QyxpQkFBZ0I7Z0JBQVVDLEtBQUs7Z0JBQUdkLElBQUk7O2tDQUMxQyw4REFBQ3JDLGlEQUFHQTs7MENBQ0YsOERBQUNDLGtEQUFJQTtnQ0FBQzZDLFVBQVM7Z0NBQUtFLE9BQU07Z0NBQVdYLElBQUk7Z0NBQUdlLGVBQWM7MENBQVk7Ozs7OzswQ0FDdEUsOERBQUNuRCxrREFBSUE7Z0NBQUM4QyxZQUFXO2dDQUFPRCxVQUFTOzBDQUFNOUIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNcUMsV0FBVyxLQUFJOzs7Ozs7MENBQzVELDhEQUFDcEQsa0RBQUlBOzBDQUFFZSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1zQyxLQUFLLEtBQUk7Ozs7OzswQ0FDdEIsOERBQUNyRCxrREFBSUE7MENBQUVlLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXVDLFdBQVcsS0FBSTs7Ozs7OzBDQUM1Qiw4REFBQ3RELGtEQUFJQTswQ0FBRWUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNd0MsT0FBTyxLQUFJOzs7Ozs7Ozs7Ozs7a0NBRTFCLDhEQUFDeEQsaURBQUdBO3dCQUFDNkMsV0FBVTs7MENBQ2IsOERBQUM1QyxrREFBSUE7Z0NBQUM4QyxZQUFXO2dDQUFPRCxVQUFTOzBDQUFNOUIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNeUMsYUFBYSxLQUFJOzs7Ozs7MENBQzlELDhEQUFDeEQsa0RBQUlBO2dDQUFDNkMsVUFBUztnQ0FBS0UsT0FBTTswQ0FBWWhDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTBDLFdBQVcsS0FBSTs7Ozs7OzBDQUMzRCw4REFBQ3pELGtEQUFJQTtnQ0FBQzZDLFVBQVM7Z0NBQUtFLE9BQU07MENBQVloQyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0yQyxlQUFlLEtBQUk7Ozs7OzswQ0FDL0QsOERBQUMxRCxrREFBSUE7Z0NBQUM2QyxVQUFTO2dDQUFLRSxPQUFNOzBDQUFZaEMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNNEMsWUFBWSxLQUFJOzs7Ozs7MENBQzVELDhEQUFDM0Qsa0RBQUlBO2dDQUFDNkMsVUFBUztnQ0FBS0UsT0FBTTswQ0FBWWhDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTZDLGFBQWEsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtqRSw4REFBQzNELG9EQUFLQTtnQkFBQzRELFNBQVE7Z0JBQVN6QixJQUFJOztrQ0FDMUIsOERBQUNsQyxvREFBS0E7a0NBQ0osNEVBQUNFLGlEQUFFQTs0QkFBQzZCLElBQUc7OzhDQUNMLDhEQUFDNUIsaURBQUVBO29DQUFDMEMsT0FBTTtvQ0FBUVQsYUFBWTs4Q0FBUTs7Ozs7OzhDQUN0Qyw4REFBQ2pDLGlEQUFFQTtvQ0FBQzBDLE9BQU07b0NBQVFULGFBQVk7b0NBQVFNLFdBQVU7OENBQVM7Ozs7Ozs4Q0FDekQsOERBQUN2QyxpREFBRUE7b0NBQUMwQyxPQUFNO29DQUFRVCxhQUFZO29DQUFRTSxXQUFVOzhDQUFROzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHNUQsOERBQUN6QyxvREFBS0E7a0NBQ0hZLENBQUFBLGlCQUFBQSw0QkFBQUEsY0FBQUEsS0FBTStDLEtBQUssY0FBWC9DLGtDQUFBQSxZQUFhZ0QsTUFBTSxJQUFHLElBQUloRCxLQUFLK0MsS0FBSyxDQUFDRSxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQy9DLDhEQUFDOUQsaURBQUVBOztrREFDRCw4REFBQ0UsaURBQUVBO3dDQUFDZ0MsYUFBWTtrREFBWTJCLEtBQUtFLFVBQVUsSUFBSUYsS0FBS0csT0FBTyxJQUFJOzs7Ozs7a0RBQy9ELDhEQUFDOUQsaURBQUVBO3dDQUFDZ0MsYUFBWTt3Q0FBV00sV0FBVTtrREFBVXFCLEtBQUtJLEdBQUcsSUFBSTs7Ozs7O2tEQUMzRCw4REFBQy9ELGlEQUFFQTt3Q0FBQ2dDLGFBQVk7d0NBQVdNLFdBQVU7a0RBQVNxQixLQUFLSyxJQUFJLElBQUlMLEtBQUtNLEtBQUssR0FBRyxJQUFtRCxPQUEvQ0MsV0FBV1AsS0FBS0ssSUFBSSxJQUFJTCxLQUFLTSxLQUFLLEVBQUVFLE9BQU8sQ0FBQyxNQUFPOzs7Ozs7OytCQUh4SFA7Ozs7MkRBTVQsOERBQUM5RCxpREFBRUE7c0NBQ0QsNEVBQUNFLGlEQUFFQTtnQ0FBQ2dDLGFBQVk7Z0NBQVdvQyxTQUFTO2dDQUFHOUIsV0FBVTtnQ0FBU0csT0FBTTswQ0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNbkYsOERBQUMvQyxrREFBSUE7Z0JBQUM2QyxVQUFTO2dCQUFLRSxPQUFNO2dCQUFXWCxJQUFJOzBCQUFHOzs7Ozs7MEJBRzVDLDhEQUFDM0Isa0RBQUlBO2dCQUFDd0MsaUJBQWdCO2dCQUFVQyxLQUFLOztrQ0FFbkMsOERBQUNuRCxpREFBR0E7d0JBQUM0RSxTQUFTO3dCQUFRQyxlQUFlO3dCQUFVQyxZQUFZO3dCQUFVQyxnQkFBZ0I7OzBDQUNuRiw4REFBQzlFLGtEQUFJQTtnQ0FBQzZDLFVBQVM7Z0NBQUtDLFlBQVc7Z0NBQU9DLE9BQU07Z0NBQVdYLElBQUk7MENBQUc7Ozs7OzswQ0FFOUQsOERBQUNyQyxpREFBR0E7O2tEQUNGLDhEQUFDUSxrREFBSUE7d0NBQUM0QixPQUFNO3dDQUFTQyxJQUFJO3dDQUFHMkMsSUFBSTt3Q0FBRzdCLEtBQUs7OzBEQUN0Qyw4REFBQzNCO2dEQUFJTSxLQUFLbEIsMEVBQUdBLENBQUNrQixHQUFHLElBQUlsQiwwRUFBR0E7Z0RBQUU2QixLQUFJO2dEQUFNQyxPQUFPO29EQUFFRSxPQUFPO29EQUFTcUMsV0FBVztnREFBVTs7Ozs7OzBEQUNsRiw4REFBQ3pEO2dEQUFJTSxLQUFLakIsNEVBQVFBLENBQUNpQixHQUFHLElBQUlqQiw0RUFBUUE7Z0RBQUU0QixLQUFJO2dEQUFXQyxPQUFPO29EQUFFRSxPQUFPO29EQUFTcUMsV0FBVztnREFBVTs7Ozs7Ozs7Ozs7O2tEQUVuRyw4REFBQ2hGLGtEQUFJQTt3Q0FBQzZDLFVBQVM7d0NBQUtDLFlBQVc7d0NBQU9DLE9BQU07d0NBQVdYLElBQUk7a0RBQUc7Ozs7OztrREFDOUQsOERBQUNwQyxrREFBSUE7d0NBQUM2QyxVQUFTO3dDQUFLRSxPQUFNO2tEQUFXOzs7Ozs7a0RBQ3JDLDhEQUFDL0Msa0RBQUlBO3dDQUFDNkMsVUFBUzt3Q0FBS0UsT0FBTTtrREFBVzs7Ozs7O2tEQUNyQyw4REFBQy9DLGtEQUFJQTt3Q0FBQzZDLFVBQVM7d0NBQUtFLE9BQU07a0RBQVc7Ozs7OztrREFDckMsOERBQUMvQyxrREFBSUE7d0NBQUM2QyxVQUFTO3dDQUFLRSxPQUFNO3dDQUFXZ0MsSUFBSTtrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtoRCw4REFBQ2hGLGlEQUFHQTs7MENBQ0YsOERBQUNRLGtEQUFJQTtnQ0FBQzJCLFNBQVE7Z0NBQWdCRSxJQUFJOztrREFDaEMsOERBQUNwQyxrREFBSUE7a0RBQUM7Ozs7OztrREFDTiw4REFBQ0Esa0RBQUlBO3dDQUFDOEMsWUFBVztrREFBUS9CLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWtFLFdBQVcsSUFBRyxJQUE0QyxPQUF4Q1QsV0FBV3pELEtBQUtrRSxXQUFXLEVBQUVSLE9BQU8sQ0FBQyxNQUFPOzs7Ozs7Ozs7Ozs7MENBRS9GLDhEQUFDbEUsa0RBQUlBO2dDQUFDMkIsU0FBUTtnQ0FBZ0JFLElBQUk7O2tEQUNoQyw4REFBQ3BDLGtEQUFJQTtrREFBQzs7Ozs7O2tEQUNOLDhEQUFDQSxrREFBSUE7d0NBQUM4QyxZQUFXO2tEQUFRL0IsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNbUUsY0FBYyxJQUFHLElBQStDLE9BQTNDVixXQUFXekQsS0FBS21FLGNBQWMsRUFBRVQsT0FBTyxDQUFDLE1BQU87Ozs7Ozs7Ozs7OzswQ0FFckcsOERBQUNsRSxrREFBSUE7Z0NBQUMyQixTQUFRO2dDQUFnQkUsSUFBSTs7a0RBQ2hDLDhEQUFDcEMsa0RBQUlBOzs0Q0FBQzs0Q0FBaUJlLENBQUFBLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW9FLFNBQVMsS0FBSSxLQUFNcEUsQ0FBQUEsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNcUUsU0FBUyxLQUFJOzRDQUFJOzs7Ozs7O2tEQUN2RSw4REFBQ3BGLGtEQUFJQTt3Q0FBQzhDLFlBQVc7a0RBQVEvQixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1zRSxVQUFVLElBQUcsSUFBMkMsT0FBdkNiLFdBQVd6RCxLQUFLc0UsVUFBVSxFQUFFWixPQUFPLENBQUMsTUFBTzs7Ozs7Ozs7Ozs7OzBDQUc3Riw4REFBQzFFLGlEQUFHQTtnQ0FBQ3VGLFdBQVU7Z0NBQVloRCxhQUFZO2dDQUFXaUQsSUFBSTtnQ0FBR1IsSUFBSTs7a0RBQzNELDhEQUFDeEUsa0RBQUlBO3dDQUFDMkIsU0FBUTt3Q0FBZ0JFLElBQUk7OzBEQUNoQyw4REFBQ3BDLGtEQUFJQTtnREFBQytDLE9BQU07Z0RBQVVELFlBQVc7MERBQU87Ozs7OzswREFDeEMsOERBQUM5QyxrREFBSUE7Z0RBQUMrQyxPQUFNO2dEQUFVRCxZQUFXOzBEQUFRL0IsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNeUUsWUFBWSxJQUFHLElBQTZDLE9BQXpDaEIsV0FBV3pELEtBQUt5RSxZQUFZLEVBQUVmLE9BQU8sQ0FBQyxNQUFPOzs7Ozs7Ozs7Ozs7a0RBRWpILDhEQUFDbEUsa0RBQUlBO3dDQUFDMkIsU0FBUTt3Q0FBZ0JFLElBQUk7OzBEQUNoQyw4REFBQ3BDLGtEQUFJQTtnREFBQytDLE9BQU07Z0RBQVVELFlBQVc7MERBQU87Ozs7OzswREFDeEMsOERBQUM5QyxrREFBSUE7Z0RBQUMrQyxPQUFNO2dEQUFVRCxZQUFXOzBEQUFPOzs7Ozs7Ozs7Ozs7a0RBRTFDLDhEQUFDdkMsa0RBQUlBO3dDQUFDMkIsU0FBUTt3Q0FBZ0JFLElBQUk7OzBEQUNoQyw4REFBQ3BDLGtEQUFJQTtnREFBQytDLE9BQU07Z0RBQVVELFlBQVc7MERBQU87Ozs7OzswREFDeEMsOERBQUM5QyxrREFBSUE7Z0RBQUMrQyxPQUFNO2dEQUFVRCxZQUFXOzBEQUFPOzs7Ozs7Ozs7Ozs7a0RBRzFDLDhEQUFDdkMsa0RBQUlBO3dDQUFDMkIsU0FBUTt3Q0FBZ0JDLE9BQU07d0NBQVNGLElBQUc7d0NBQVdILEdBQUc7d0NBQUcyRCxjQUFhOzswREFDNUUsOERBQUMxRixpREFBR0E7O2tFQUNGLDhEQUFDQyxrREFBSUE7d0RBQUM0QyxXQUFVO3dEQUFPQyxVQUFTO3dEQUFLQyxZQUFXO2tFQUFPOzs7Ozs7a0VBQ3ZELDhEQUFDOUMsa0RBQUlBO3dEQUFDNEMsV0FBVTt3REFBUUMsVUFBUzt3REFBS0UsT0FBTTtrRUFBVzs7Ozs7Ozs7Ozs7OzBEQUV6RCw4REFBQ2hELGlEQUFHQTtnREFBQzZDLFdBQVU7MERBQ2IsNEVBQUM1QyxrREFBSUE7b0RBQUM2QyxVQUFTO29EQUFLQyxZQUFXOzhEQUFRL0IsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMkUsYUFBYSxJQUFHLElBQThDLE9BQTFDbEIsV0FBV3pELEtBQUsyRSxhQUFhLEVBQUVqQixPQUFPLENBQUMsTUFBTzFELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRFLFNBQVMsSUFBRyxJQUEwQyxPQUF0Q25CLFdBQVd6RCxLQUFLNEUsU0FBUyxFQUFFbEIsT0FBTyxDQUFDLE1BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF6TCw4REFBQ2xFLGtEQUFJQTtnQkFBQzJCLFNBQVE7Z0JBQWdCNkMsSUFBSTtnQkFBSVEsSUFBSTtnQkFBR2pELGFBQVk7O2tDQUN2RCw4REFBQ3ZDLGlEQUFHQTt3QkFBQzZDLFdBQVU7d0JBQVNELE9BQU07OzBDQUM1Qiw4REFBQzVDLGlEQUFHQTtnQ0FBQ3NDLGNBQWE7Z0NBQVlDLGFBQVk7Z0NBQVdJLFFBQU87Z0NBQU9OLElBQUk7Z0NBQUd1QyxTQUFRO2dDQUFPRSxZQUFXO2dDQUFTQyxnQkFBZTswQ0FDekgvRCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU02RSxTQUFTLG1CQUNkLDhEQUFDckU7b0NBQ0NNLEtBQUtkLEtBQUs2RSxTQUFTO29DQUNuQnBELEtBQUk7b0NBQ0pDLE9BQU87d0NBQUVvRCxXQUFXO3dDQUFRQyxVQUFVO3dDQUFRZCxXQUFXO29DQUFVO29DQUNuRXhELGFBQVk7Ozs7Ozs7Ozs7OzBDQUlsQiw4REFBQ3hCLGtEQUFJQTtnQ0FBQzhDLFlBQVc7MENBQU87Ozs7Ozs7Ozs7OztrQ0FFMUIsOERBQUMvQyxpREFBR0E7d0JBQUM2QyxXQUFVO3dCQUFTRCxPQUFNOzswQ0FDNUIsOERBQUM1QyxpREFBR0E7Z0NBQUNzQyxjQUFhO2dDQUFZQyxhQUFZO2dDQUFXSSxRQUFPO2dDQUFPTixJQUFJO2dDQUFHdUMsU0FBUTtnQ0FBT0UsWUFBVztnQ0FBU0MsZ0JBQWU7MENBQzFILDRFQUFDOUUsa0RBQUlBOzhDQUFFVSw0Q0FBS0EsR0FBR3FGLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7MENBRXhCLDhEQUFDL0Ysa0RBQUlBO2dDQUFDOEMsWUFBVzswQ0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2xDO0dBdEtNaEM7S0FBQUE7QUF3S04sK0RBQWVBLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2ZvbGxvdy11cC9Db21wb25lbnRUb1ByaW50LmpzeD9iNmI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQm94LCBUZXh0LCBUYWJsZSwgVGhlYWQsIFRib2R5LCBUciwgVGgsIFRkLCBGbGV4LCBTcGFjZXIsIEdyaWQgfSBmcm9tIFwiQGNoYWtyYS11aS9yZWFjdFwiO1xuaW1wb3J0IGRheWpzIGZyb20gXCJkYXlqc1wiO1xuaW1wb3J0IHppcCBmcm9tICdAYXNzZXRzL2Fzc2V0cy9pbWdzL3ppcFBheUxvZ28uanBnJztcbmltcG9ydCBhZnRlcnBheSBmcm9tICdAYXNzZXRzL2Fzc2V0cy9pbWdzL2FmdGVyUGF5TG9nby5qcGcnO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5cbmNvbnN0IENvbXBvbmVudFRvUHJpbnQgPSAoeyBkYXRhIH0pID0+IHtcbiAgY29uc3QgW2xvZ29TcmMsIHNldExvZ29TcmNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBsb2dvSW1hZ2UgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTCA/IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JBU0VfVVJMIDogJyd9Z29kb3duLyR7ZGF0YT8ubG9jYXRpb24gfHwgJ0VBTSd9L2xvZ28/ZmFsbGJhY2s9dHJ1ZWA7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBQcmVsb2FkIHRoZSBsb2dvIGltYWdlIHRvIGVuc3VyZSBpdCdzIGF2YWlsYWJsZSBmb3IgUERGIGdlbmVyYXRpb25cbiAgICBjb25zdCBpbWcgPSBuZXcgSW1hZ2UoKTtcbiAgICBpbWcuY3Jvc3NPcmlnaW4gPSAnYW5vbnltb3VzJztcbiAgICBpbWcub25sb2FkID0gKCkgPT4ge1xuICAgICAgc2V0TG9nb1NyYyhsb2dvSW1hZ2UpO1xuICAgIH07XG4gICAgaW1nLm9uZXJyb3IgPSAoKSA9PiB7XG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBsb2FkIGxvZ28gaW1hZ2U6JywgbG9nb0ltYWdlKTtcbiAgICAgIHNldExvZ29TcmMoJycpOyAvLyBTZXQgZW1wdHkgdG8gaGlkZSBicm9rZW4gaW1hZ2VcbiAgICB9O1xuICAgIGltZy5zcmMgPSBsb2dvSW1hZ2U7XG4gIH0sIFtsb2dvSW1hZ2VdKTtcbiAgcmV0dXJuIChcbiAgICA8Qm94IHA9ezh9IG1heFc9XCI4MDBweFwiIG14PVwiYXV0b1wiIGJnPVwid2hpdGVcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8RmxleCBqdXN0aWZ5PVwic3BhY2UtYmV0d2VlblwiIGFsaWduPVwiY2VudGVyXCIgbWI9ezZ9IGJvcmRlckJvdHRvbT1cIjJweCBzb2xpZFwiIGJvcmRlckNvbG9yPVwiZ3JheS4zMDBcIiBwYj17NH0+XG4gICAgICAgIDxCb3g+XG4gICAgICAgICAge2xvZ29TcmMgJiYgKFxuICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICBzcmM9e2xvZ29TcmN9XG4gICAgICAgICAgICAgIGFsdD1cIkVjbyBBc3NldHMgTWFuYWdlclwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogXCIxMDBweFwiLCB3aWR0aDogXCJhdXRvXCIgfX1cbiAgICAgICAgICAgICAgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L0JveD5cbiAgICAgICAgPEJveCB0ZXh0QWxpZ249XCJyaWdodFwiPlxuICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwiM3hsXCIgZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cImJsYWNrXCI+UVVPVEFUSU9OPC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGNvbG9yPVwicmVkLjUwMFwiIGZvbnRXZWlnaHQ9XCJib2xkXCIgZm9udFNpemU9XCJsZ1wiPntkYXRhPy5sb2NhdGlvbiB8fCAnRUFNJ30ge2RhdGE/LnZubyB8fCAnTi9BJ308L1RleHQ+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9GbGV4PlxuXG4gICAgICB7LyogQ29tcGFueSBhbmQgQ2xpZW50IEluZm8gKi99XG4gICAgICA8R3JpZCB0ZW1wbGF0ZUNvbHVtbnM9XCIxZnIgMWZyXCIgZ2FwPXs4fSBtYj17Nn0+XG4gICAgICAgIDxCb3g+XG4gICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGNvbG9yPVwiZ3JheS42MDBcIiBtYj17Mn0gdGV4dFRyYW5zZm9ybT1cInVwcGVyY2FzZVwiPklOVk9JQ0UgVE88L1RleHQ+XG4gICAgICAgICAgPFRleHQgZm9udFdlaWdodD1cImJvbGRcIiBmb250U2l6ZT1cImxnXCI+e2RhdGE/LmNsaWVudFRpdGxlIHx8ICdObyBDbGllbnQnfTwvVGV4dD5cbiAgICAgICAgICA8VGV4dD57ZGF0YT8uZW1haWwgfHwgJ05vIEVtYWlsJ308L1RleHQ+XG4gICAgICAgICAgPFRleHQ+e2RhdGE/LnBob25lTnVtYmVyIHx8ICdObyBQaG9uZSd9PC9UZXh0PlxuICAgICAgICAgIDxUZXh0PntkYXRhPy5hZGRyZXNzIHx8ICdObyBBZGRyZXNzJ308L1RleHQ+XG4gICAgICAgIDwvQm94PlxuICAgICAgICA8Qm94IHRleHRBbGlnbj1cInJpZ2h0XCI+XG4gICAgICAgICAgPFRleHQgZm9udFdlaWdodD1cImJvbGRcIiBmb250U2l6ZT1cImxnXCI+e2RhdGE/LmxvY2F0aW9uVGl0bGUgfHwgJ0VjbyBBc3NldHMgTWFuYWdlciBQVFkgTFREJ308L1RleHQ+XG4gICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGNvbG9yPVwiZ3JheS42MDBcIj57ZGF0YT8ubG9jYXRpb25BQk4gfHwgJ05vIEFCTid9PC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+e2RhdGE/LmxvY2F0aW9uQWRkcmVzcyB8fCAnTm8gQWRkcmVzcyd9PC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+e2RhdGE/LmxvY2F0aW9uQ2l0eSB8fCAnTm8gQ2l0eSd9PC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+e2RhdGE/LmxvY2F0aW9uUGhvbmUgfHwgJ05vIFBob25lJ308L1RleHQ+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9HcmlkPlxuXG4gICAgICB7LyogUHJvZHVjdHMgVGFibGUgKi99XG4gICAgICA8VGFibGUgdmFyaWFudD1cInNpbXBsZVwiIG1iPXs2fT5cbiAgICAgICAgPFRoZWFkPlxuICAgICAgICAgIDxUciBiZz1cIiMyZDY2NTEgIWltcG9ydGFudFwiPlxuICAgICAgICAgICAgPFRoIGNvbG9yPVwid2hpdGVcIiBib3JkZXJDb2xvcj1cIndoaXRlXCI+UHJvZHVjdHM8L1RoPlxuICAgICAgICAgICAgPFRoIGNvbG9yPVwid2hpdGVcIiBib3JkZXJDb2xvcj1cIndoaXRlXCIgdGV4dEFsaWduPVwiY2VudGVyXCI+UXVhbnRpdHk8L1RoPlxuICAgICAgICAgICAgPFRoIGNvbG9yPVwid2hpdGVcIiBib3JkZXJDb2xvcj1cIndoaXRlXCIgdGV4dEFsaWduPVwicmlnaHRcIj5QcmljZTwvVGg+XG4gICAgICAgICAgPC9Ucj5cbiAgICAgICAgPC9UaGVhZD5cbiAgICAgICAgPFRib2R5PlxuICAgICAgICAgIHtkYXRhPy5pdGVtcz8ubGVuZ3RoID4gMCA/IGRhdGEuaXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPFRyIGtleT17aW5kZXh9PlxuICAgICAgICAgICAgICA8VGQgYm9yZGVyQ29sb3I9XCJncmF5LjMwMFwiPntpdGVtLkl0ZW1fVGl0bGUgfHwgaXRlbS5kZXRhaWxzIHx8ICcnfTwvVGQ+XG4gICAgICAgICAgICAgIDxUZCBib3JkZXJDb2xvcj1cImdyYXkuMzAwXCIgdGV4dEFsaWduPVwiY2VudGVyXCI+e2l0ZW0uUXR5IHx8ICcnfTwvVGQ+XG4gICAgICAgICAgICAgIDxUZCBib3JkZXJDb2xvcj1cImdyYXkuMzAwXCIgdGV4dEFsaWduPVwicmlnaHRcIj57aXRlbS5SYXRlIHx8IGl0ZW0uVG90YWwgPyBgJCR7cGFyc2VGbG9hdChpdGVtLlJhdGUgfHwgaXRlbS5Ub3RhbCkudG9GaXhlZCgyKX1gIDogJyd9PC9UZD5cbiAgICAgICAgICAgIDwvVHI+XG4gICAgICAgICAgKSkgOiAoXG4gICAgICAgICAgICA8VHI+XG4gICAgICAgICAgICAgIDxUZCBib3JkZXJDb2xvcj1cImdyYXkuMzAwXCIgY29sU3Bhbj17M30gdGV4dEFsaWduPVwiY2VudGVyXCIgY29sb3I9XCJncmF5LjUwMFwiPk5vIGl0ZW1zIGF2YWlsYWJsZTwvVGQ+XG4gICAgICAgICAgICA8L1RyPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvVGJvZHk+XG4gICAgICA8L1RhYmxlPlxuXG4gICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCIgY29sb3I9XCJncmF5LjYwMFwiIG1iPXs2fT4qIEFkZGl0aW9uYWwgY2hhcmdlcyBtYXkgYXBwbHkgc3ViamVjdCB0byBpbnN0YWxsZXIncyBhc3Nlc3NtZW50ICo8L1RleHQ+XG5cbiAgICAgIHsvKiBCb3R0b20gU2VjdGlvbiAqL31cbiAgICAgIDxHcmlkIHRlbXBsYXRlQ29sdW1ucz1cIjFmciAxZnJcIiBnYXA9ezh9PlxuICAgICAgICB7LyogTGVmdCBTaWRlIC0gUGF5bWVudCBJbmZvICovfVxuICAgICAgICA8Qm94IGRpc3BsYXk9eydmbGV4J30gZmxleERpcmVjdGlvbj17J2NvbHVtbid9IGFsaWduSXRlbXM9eydjZW50ZXInfSBqdXN0aWZ5Q29udGVudD17J2NlbnRlcid9PlxuICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwibGdcIiBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiZ3JheS43MDBcIiBtYj17NH0+RklOQU5DSU5HIEFWQUlMQUJMRTwvVGV4dD5cblxuICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICA8RmxleCBhbGlnbj1cImNlbnRlclwiIG1iPXs0fSBtdD17Mn0gZ2FwPXs0fT5cbiAgICAgICAgICAgICAgPGltZyBzcmM9e3ppcC5zcmMgfHwgemlwfSBhbHQ9XCJaaXBcIiBzdHlsZT17eyB3aWR0aDogXCIxMDBweFwiLCBvYmplY3RGaXQ6IFwiY29udGFpblwiIH19IC8+XG4gICAgICAgICAgICAgIDxpbWcgc3JjPXthZnRlcnBheS5zcmMgfHwgYWZ0ZXJwYXl9IGFsdD1cIkFmdGVycGF5XCIgc3R5bGU9e3sgd2lkdGg6IFwiMTAwcHhcIiwgb2JqZWN0Rml0OiBcImNvbnRhaW5cIiB9fSAvPlxuICAgICAgICAgICAgPC9GbGV4PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJsZ1wiIGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCJncmF5LjcwMFwiIG1iPXsyfT5QQVlNRU5UIE1FVEhPRDwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+QmFuayBOYW1lOiBCYW5rIG9mIE1lbGJvdXJuZTwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+QlNCOiAxOTM4Nzk8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCIgY29sb3I9XCJncmF5LjYwMFwiPkFjY291bnQgIzogNDUzNzc3NTMzPC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGNvbG9yPVwiZ3JheS42MDBcIiBtdD17Mn0+UGxlYXNlIGRlcG9zaXQgMTAlIGFuZCBiYWxhbmNlIG11c3QgYmUgcGFpZCBvbiBpbnN0YWxsYXRpb24gZGF5LjwvVGV4dD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgey8qIFJpZ2h0IFNpZGUgLSBUb3RhbHMgKi99XG4gICAgICAgIDxCb3g+XG4gICAgICAgICAgPEZsZXgganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBtYj17Mn0+XG4gICAgICAgICAgICA8VGV4dD5TdWJ0b3RhbCBJbmNsLiBHU1Q8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250V2VpZ2h0PVwiYm9sZFwiPntkYXRhPy50b3RhbEFtb3VudCA/IGAkJHtwYXJzZUZsb2F0KGRhdGEudG90YWxBbW91bnQpLnRvRml4ZWQoMil9YCA6ICdOL0EnfTwvVGV4dD5cbiAgICAgICAgICA8L0ZsZXg+XG4gICAgICAgICAgPEZsZXgganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBtYj17Mn0+XG4gICAgICAgICAgICA8VGV4dD5FQU0gZGlzY291bnQ8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250V2VpZ2h0PVwiYm9sZFwiPntkYXRhPy5kaXNjb3VudEFtb3VudCA/IGAkJHtwYXJzZUZsb2F0KGRhdGEuZGlzY291bnRBbW91bnQpLnRvRml4ZWQoMil9YCA6ICdOL0EnfTwvVGV4dD5cbiAgICAgICAgICA8L0ZsZXg+XG4gICAgICAgICAgPEZsZXgganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBtYj17Mn0+XG4gICAgICAgICAgICA8VGV4dD5UYXggSW5jbHVkZWQgKHsoKGRhdGE/LnNhbGVzVGF4UiB8fCAwKSArIChkYXRhPy5zYWxlc1RheEEgfHwgMCkpfSUpPC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgZm9udFdlaWdodD1cImJvbGRcIj57ZGF0YT8uc1RheEFtb3VudCA/IGAkJHtwYXJzZUZsb2F0KGRhdGEuc1RheEFtb3VudCkudG9GaXhlZCgyKX1gIDogJ04vQSd9PC9UZXh0PlxuICAgICAgICAgIDwvRmxleD5cblxuICAgICAgICAgIDxCb3ggYm9yZGVyVG9wPVwiMXB4IHNvbGlkXCIgYm9yZGVyQ29sb3I9XCJncmF5LjMwMFwiIHB0PXsyfSBtdD17NH0+XG4gICAgICAgICAgICA8RmxleCBqdXN0aWZ5PVwic3BhY2UtYmV0d2VlblwiIG1iPXsyfT5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIjMmQ2NjUxXCIgZm9udFdlaWdodD1cImJvbGRcIj5WRUVDIERJU0NPVU5UPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiMyZDY2NTFcIiBmb250V2VpZ2h0PVwiYm9sZFwiPntkYXRhPy52ZWVjRGlzY291bnQgPyBgJCR7cGFyc2VGbG9hdChkYXRhLnZlZWNEaXNjb3VudCkudG9GaXhlZCgyKX1gIDogJ04vQSd9PC9UZXh0PlxuICAgICAgICAgICAgPC9GbGV4PlxuICAgICAgICAgICAgPEZsZXgganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBtYj17Mn0+XG4gICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzJkNjY1MVwiIGZvbnRXZWlnaHQ9XCJib2xkXCI+U1RDIERJU0NPVU5UPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiMyZDY2NTFcIiBmb250V2VpZ2h0PVwiYm9sZFwiPk4vQTwvVGV4dD5cbiAgICAgICAgICAgIDwvRmxleD5cbiAgICAgICAgICAgIDxGbGV4IGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgbWI9ezR9PlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiMyZDY2NTFcIiBmb250V2VpZ2h0PVwiYm9sZFwiPlNPTEFSVklDIFJFQkFURTwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIjMmQ2NjUxXCIgZm9udFdlaWdodD1cImJvbGRcIj5OL0E8L1RleHQ+XG4gICAgICAgICAgICA8L0ZsZXg+XG5cbiAgICAgICAgICAgIDxGbGV4IGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgYWxpZ249XCJjZW50ZXJcIiBiZz1cImdyYXkuMTAwXCIgcD17M30gYm9yZGVyUmFkaXVzPVwibWRcIj5cbiAgICAgICAgICAgICAgPEJveD5cbiAgICAgICAgICAgICAgICA8VGV4dCB0ZXh0QWxpZ249XCJsZWZ0XCIgZm9udFNpemU9XCJsZ1wiIGZvbnRXZWlnaHQ9XCJib2xkXCI+VG90YWwgb3V0IG9mIHBvY2tldDwvVGV4dD5cbiAgICAgICAgICAgICAgICA8VGV4dCB0ZXh0QWxpZ249XCJyaWdodFwiIGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNjAwXCI+aW5jbC4gR1NUPC9UZXh0PlxuICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgPEJveCB0ZXh0QWxpZ249XCJyaWdodFwiPlxuICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwieGxcIiBmb250V2VpZ2h0PVwiYm9sZFwiPntkYXRhPy5uZXRQYXlhYmxlQW10ID8gYCQke3BhcnNlRmxvYXQoZGF0YS5uZXRQYXlhYmxlQW10KS50b0ZpeGVkKDIpfWAgOiBkYXRhPy5uZXRBbW91bnQgPyBgJCR7cGFyc2VGbG9hdChkYXRhLm5ldEFtb3VudCkudG9GaXhlZCgyKX1gIDogJ04vQSd9PC9UZXh0PlxuICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgIDwvRmxleD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0dyaWQ+XG5cbiAgICAgIHsvKiBTaWduYXR1cmUgU2VjdGlvbiAqL31cbiAgICAgIDxGbGV4IGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezE2fSBwdD17OH0gYm9yZGVyQ29sb3I9XCJncmF5LjMwMFwiPlxuICAgICAgICA8Qm94IHRleHRBbGlnbj1cImNlbnRlclwiIHdpZHRoPVwiMjAwcHhcIj5cbiAgICAgICAgICA8Qm94IGJvcmRlckJvdHRvbT1cIjFweCBzb2xpZFwiIGJvcmRlckNvbG9yPVwiZ3JheS40MDBcIiBoZWlnaHQ9XCI2MHB4XCIgbWI9ezJ9IGRpc3BsYXk9XCJmbGV4XCIgYWxpZ25JdGVtcz1cImNlbnRlclwiIGp1c3RpZnlDb250ZW50PVwiY2VudGVyXCI+XG4gICAgICAgICAgICB7ZGF0YT8uc2lnbmF0dXJlICYmIChcbiAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgIHNyYz17ZGF0YS5zaWduYXR1cmV9XG4gICAgICAgICAgICAgICAgYWx0PVwiQ3VzdG9tZXIgU2lnbmF0dXJlXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXhIZWlnaHQ6ICc2MHB4JywgbWF4V2lkdGg6ICcxMDAlJywgb2JqZWN0Rml0OiAnY29udGFpbicgfX1cbiAgICAgICAgICAgICAgICBjcm9zc09yaWdpbj1cImFub255bW91c1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIDxUZXh0IGZvbnRXZWlnaHQ9XCJib2xkXCI+Q3VzdG9tZXIgU2lnbmF0dXJlPC9UZXh0PlxuICAgICAgICA8L0JveD5cbiAgICAgICAgPEJveCB0ZXh0QWxpZ249XCJjZW50ZXJcIiB3aWR0aD1cIjIwMHB4XCI+XG4gICAgICAgICAgPEJveCBib3JkZXJCb3R0b209XCIxcHggc29saWRcIiBib3JkZXJDb2xvcj1cImdyYXkuNDAwXCIgaGVpZ2h0PVwiNjBweFwiIG1iPXsyfSBkaXNwbGF5PVwiZmxleFwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIiBqdXN0aWZ5Q29udGVudD1cImNlbnRlclwiPlxuICAgICAgICAgICAgPFRleHQ+e2RheWpzKCkuZm9ybWF0KFwiREQtTU1NLVlZWVlcIil9PC9UZXh0PlxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIDxUZXh0IGZvbnRXZWlnaHQ9XCJib2xkXCI+RGF0ZTwvVGV4dD5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0ZsZXg+XG4gICAgPC9Cb3g+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb21wb25lbnRUb1ByaW50OyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQm94IiwiVGV4dCIsIlRhYmxlIiwiVGhlYWQiLCJUYm9keSIsIlRyIiwiVGgiLCJUZCIsIkZsZXgiLCJTcGFjZXIiLCJHcmlkIiwiZGF5anMiLCJ6aXAiLCJhZnRlcnBheSIsIkltYWdlIiwiQ29tcG9uZW50VG9QcmludCIsImRhdGEiLCJsb2dvU3JjIiwic2V0TG9nb1NyYyIsImxvZ29JbWFnZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsImxvY2F0aW9uIiwiaW1nIiwiY3Jvc3NPcmlnaW4iLCJvbmxvYWQiLCJvbmVycm9yIiwiY29uc29sZSIsIndhcm4iLCJzcmMiLCJwIiwibWF4VyIsIm14IiwiYmciLCJqdXN0aWZ5IiwiYWxpZ24iLCJtYiIsImJvcmRlckJvdHRvbSIsImJvcmRlckNvbG9yIiwicGIiLCJhbHQiLCJzdHlsZSIsImhlaWdodCIsIndpZHRoIiwidGV4dEFsaWduIiwiZm9udFNpemUiLCJmb250V2VpZ2h0IiwiY29sb3IiLCJ2bm8iLCJ0ZW1wbGF0ZUNvbHVtbnMiLCJnYXAiLCJ0ZXh0VHJhbnNmb3JtIiwiY2xpZW50VGl0bGUiLCJlbWFpbCIsInBob25lTnVtYmVyIiwiYWRkcmVzcyIsImxvY2F0aW9uVGl0bGUiLCJsb2NhdGlvbkFCTiIsImxvY2F0aW9uQWRkcmVzcyIsImxvY2F0aW9uQ2l0eSIsImxvY2F0aW9uUGhvbmUiLCJ2YXJpYW50IiwiaXRlbXMiLCJsZW5ndGgiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJJdGVtX1RpdGxlIiwiZGV0YWlscyIsIlF0eSIsIlJhdGUiLCJUb3RhbCIsInBhcnNlRmxvYXQiLCJ0b0ZpeGVkIiwiY29sU3BhbiIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwibXQiLCJvYmplY3RGaXQiLCJ0b3RhbEFtb3VudCIsImRpc2NvdW50QW1vdW50Iiwic2FsZXNUYXhSIiwic2FsZXNUYXhBIiwic1RheEFtb3VudCIsImJvcmRlclRvcCIsInB0IiwidmVlY0Rpc2NvdW50IiwiYm9yZGVyUmFkaXVzIiwibmV0UGF5YWJsZUFtdCIsIm5ldEFtb3VudCIsInNpZ25hdHVyZSIsIm1heEhlaWdodCIsIm1heFdpZHRoIiwiZm9ybWF0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});