"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-signature-canvas";
exports.ids = ["vendor-chunks/react-signature-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-signature-canvas/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/react-signature-canvas/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureCanvas: () => (/* binding */ SignatureCanvas),\n/* harmony export */   \"default\": () => (/* binding */ SignatureCanvas)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_createSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/createSuper.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var signature_pad__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! signature_pad */ \"(ssr)/./node_modules/signature_pad/dist/signature_pad.mjs\");\n/* harmony import */ var trim_canvas__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! trim-canvas */ \"(ssr)/./node_modules/trim-canvas/build/index.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"canvasProps\", \"clearOnResize\"];\nvar SignatureCanvas = /*#__PURE__*/function (_Component) {\n  _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__(SignatureCanvas, _Component);\n  var _super = _babel_runtime_helpers_createSuper__WEBPACK_IMPORTED_MODULE_5__(SignatureCanvas);\n  function SignatureCanvas() {\n    var _this;\n    _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__(this, SignatureCanvas);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.staticThis = _this.constructor;\n    _this._sigPad = null;\n    _this._canvas = null;\n    _this.setRef = function (ref) {\n      _this._canvas = ref;\n      // if component is unmounted, set internal references to null\n      if (_this._canvas === null) {\n        _this._sigPad = null;\n      }\n    };\n    _this._excludeOurProps = function () {\n      var _this$props = _this.props;\n        _this$props.canvasProps;\n        _this$props.clearOnResize;\n        var sigPadProps = _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__(_this$props, _excluded);\n      return sigPadProps;\n    };\n    _this.componentDidMount = function () {\n      var canvas = _this.getCanvas();\n      _this._sigPad = new signature_pad__WEBPACK_IMPORTED_MODULE_7__[\"default\"](canvas, _this._excludeOurProps());\n      _this._resizeCanvas();\n      _this.on();\n    };\n    _this.componentWillUnmount = function () {\n      _this.off();\n    };\n    _this.componentDidUpdate = function () {\n      Object.assign(_this._sigPad, _this._excludeOurProps());\n    };\n    _this.getCanvas = function () {\n      if (_this._canvas === null) {\n        throw _this.staticThis.refNullError;\n      }\n      return _this._canvas;\n    };\n    _this.getTrimmedCanvas = function () {\n      // copy the canvas\n      var canvas = _this.getCanvas();\n      var copy = document.createElement('canvas');\n      copy.width = canvas.width;\n      copy.height = canvas.height;\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      copy.getContext('2d').drawImage(canvas, 0, 0);\n      // then trim it\n      return trim_canvas__WEBPACK_IMPORTED_MODULE_8__(copy);\n    };\n    _this.getSignaturePad = function () {\n      if (_this._sigPad === null) {\n        throw _this.staticThis.refNullError;\n      }\n      return _this._sigPad;\n    };\n    _this._checkClearOnResize = function () {\n      if (!_this.props.clearOnResize) {\n        // eslint-disable-line @typescript-eslint/strict-boolean-expressions -- this is backward compatible with the previous behavior, where null was treated as falsey\n        return;\n      }\n      _this._resizeCanvas();\n    };\n    _this._resizeCanvas = function () {\n      var _this$props$canvasPro, _window$devicePixelRa;\n      var canvasProps = (_this$props$canvasPro = _this.props.canvasProps) !== null && _this$props$canvasPro !== void 0 ? _this$props$canvasPro : {};\n      var width = canvasProps.width,\n        height = canvasProps.height;\n      // don't resize if the canvas has fixed width and height\n      if (typeof width !== 'undefined' && typeof height !== 'undefined') {\n        return;\n      }\n      var canvas = _this.getCanvas();\n      /* When zoomed out to less than 100%, for some very strange reason,\n        some browsers report devicePixelRatio as less than 1\n        and only part of the canvas is cleared then. */\n      var ratio = Math.max((_window$devicePixelRa = window.devicePixelRatio) !== null && _window$devicePixelRa !== void 0 ? _window$devicePixelRa : 1, 1);\n      if (typeof width === 'undefined') {\n        canvas.width = canvas.offsetWidth * ratio;\n      }\n      if (typeof height === 'undefined') {\n        canvas.height = canvas.offsetHeight * ratio;\n      }\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      canvas.getContext('2d').scale(ratio, ratio);\n      _this.clear();\n    };\n    _this.render = function () {\n      var canvasProps = _this.props.canvasProps;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"canvas\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({\n        ref: _this.setRef\n      }, canvasProps));\n    };\n    _this.on = function () {\n      window.addEventListener('resize', _this._checkClearOnResize);\n      return _this.getSignaturePad().on();\n    };\n    _this.off = function () {\n      window.removeEventListener('resize', _this._checkClearOnResize);\n      return _this.getSignaturePad().off();\n    };\n    _this.clear = function () {\n      return _this.getSignaturePad().clear();\n    };\n    _this.isEmpty = function () {\n      return _this.getSignaturePad().isEmpty();\n    };\n    _this.fromDataURL = function (dataURL, options) {\n      return _this.getSignaturePad().fromDataURL(dataURL, options);\n    };\n    _this.toDataURL = function (type, encoderOptions) {\n      return _this.getSignaturePad().toDataURL(type, encoderOptions);\n    };\n    _this.fromData = function (pointGroups) {\n      return _this.getSignaturePad().fromData(pointGroups);\n    };\n    _this.toData = function () {\n      return _this.getSignaturePad().toData();\n    };\n    return _this;\n  } // shortcut reference (https://stackoverflow.com/a/29244254/3431180)\n  // propagate prop updates to SignaturePad\n  // return the canvas ref for operations like toDataURL\n  // return a trimmed copy of the canvas\n  // return the internal SignaturePad reference\n  // all wrapper functions below render\n  //\n  return _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__(SignatureCanvas);\n}(react__WEBPACK_IMPORTED_MODULE_6__.Component);\nSignatureCanvas.propTypes = {\n  // signature_pad's props\n  velocityFilterWeight: prop_types__WEBPACK_IMPORTED_MODULE_9__.number,\n  minWidth: prop_types__WEBPACK_IMPORTED_MODULE_9__.number,\n  maxWidth: prop_types__WEBPACK_IMPORTED_MODULE_9__.number,\n  minDistance: prop_types__WEBPACK_IMPORTED_MODULE_9__.number,\n  dotSize: prop_types__WEBPACK_IMPORTED_MODULE_9__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_9__.number, prop_types__WEBPACK_IMPORTED_MODULE_9__.func]),\n  penColor: prop_types__WEBPACK_IMPORTED_MODULE_9__.string,\n  throttle: prop_types__WEBPACK_IMPORTED_MODULE_9__.number,\n  onEnd: prop_types__WEBPACK_IMPORTED_MODULE_9__.func,\n  onBegin: prop_types__WEBPACK_IMPORTED_MODULE_9__.func,\n  // props specific to the React wrapper\n  canvasProps: prop_types__WEBPACK_IMPORTED_MODULE_9__.object,\n  clearOnResize: prop_types__WEBPACK_IMPORTED_MODULE_9__.bool\n};\nSignatureCanvas.defaultProps = {\n  clearOnResize: true\n};\nSignatureCanvas.refNullError = new Error('react-signature-canvas is currently ' + 'mounting or unmounting: React refs are null during this phase.');\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-signature-canvas/dist/index.mjs\n");

/***/ })

};
;