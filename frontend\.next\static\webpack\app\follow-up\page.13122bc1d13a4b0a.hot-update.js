"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/AssesserFollowUp.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _src_components_sidebar_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @src/components/sidebar/sidebar */ \"(app-pages-browser)/./src/components/sidebar/sidebar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Custom/ComboBox/ComboBox */ \"(app-pages-browser)/./src/components/Custom/ComboBox/ComboBox.jsx\");\n/* harmony import */ var _components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Custom/TableComboBox/TableComboBox */ \"(app-pages-browser)/./src/components/Custom/TableComboBox/TableComboBox.jsx\");\n/* harmony import */ var _components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @components/Custom/AanzaDataTable/AanzaDataTable */ \"(app-pages-browser)/./src/components/Custom/AanzaDataTable/AanzaDataTable.jsx\");\n/* harmony import */ var _axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/Toolbar/Toolbar */ \"(app-pages-browser)/./src/components/Toolbar/Toolbar.jsx\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @utils/functions */ \"(app-pages-browser)/./src/app/utils/functions.js\");\n/* harmony import */ var _utils_constant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @utils/constant */ \"(app-pages-browser)/./src/app/utils/constant.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @components/Custom/SignaturePad/SignaturePad */ \"(app-pages-browser)/./src/components/Custom/SignaturePad/SignaturePad.jsx\");\n/* harmony import */ var _components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @components/Custom/MultipleImageUploader/MultipleImageUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleImageUploader/MultipleImageUploader.jsx\");\n/* harmony import */ var _components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @components/Custom/MultipleAudioUploader/MultipleAudioUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleAudioUploader/MultipleAudioUploader.jsx\");\n/* harmony import */ var _components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @components/Custom/NumberInput/NumberInput */ \"(app-pages-browser)/./src/components/Custom/NumberInput/NumberInput.jsx\");\n/* harmony import */ var _components_PrintModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @components/PrintModal */ \"(app-pages-browser)/./src/components/PrintModal.jsx\");\n/* harmony import */ var _ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./ComponentToPrint */ \"(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst QuotationFollowupRequiredFields = [];\nconst QuotationFollowupItemsRequiredFields = [];\nconst QuotationFollowupHeaders = [\n    {\n        label: \"Item ID\",\n        key: \"Item_ID\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Item Description\",\n        key: \"Item_Title\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Qty\",\n        key: \"Qty\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Rate\",\n        key: \"Rate\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Disc %\",\n        key: \"Disc\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Installation Charges\",\n        key: \"InstallationCharges\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Total\",\n        key: \"Total\",\n        width: \"100px\",\n        isReadOnly: true,\n        type: \"number\"\n    },\n    {\n        label: \"Delivery Date\",\n        key: \"Date\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"date\"\n    },\n    {\n        label: \"Details\",\n        key: \"Details\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    }\n];\nconst createQuotationFollowupEmptyTableRow = ()=>[\n        {\n            Item_ID: \"\",\n            Item_Title: \"\",\n            Qty: 0,\n            Rate: 0,\n            InstallationCharges: 0,\n            Disc: 0,\n            Total: 0,\n            Date: \"\",\n            Details: \"\"\n        }\n    ];\nconst createQuotationFollowupInitialFormData = ()=>({\n        location: \"\",\n        vno: \"\",\n        vtp: \"\",\n        voucherNo: \"\",\n        date: \"\",\n        mnth: \"\",\n        tenderNo: \"\",\n        exchangeRate: 0,\n        partyRef: \"\",\n        attentionPerson: \"\",\n        designation: \"\",\n        contactPerson: \"\",\n        email: \"\",\n        phoneNumber: \"\",\n        landline: \"\",\n        address: \"\",\n        lead: \"\",\n        clientId: \"\",\n        clientTitle: \"\",\n        currency: \"\",\n        subject: \"Quotation for Requested Items\",\n        quotation: \"Please find below the quotation for the items you inquired about. The prices mentioned are inclusive of all applicable charges. Let us know if you need any adjustments or have further queries.\",\n        totalAmount: 0,\n        freight: 0,\n        netAmount: 0,\n        validityDays: 0,\n        paymentTerms: \"\",\n        narration: \"\",\n        salesTaxR: \"\",\n        salesTaxA: \"\",\n        discountPercent: \"\",\n        discountAmount: 0,\n        netPayableAmt: 0,\n        sTaxAmount: 0,\n        assignerId: \"\",\n        assignerTitle: \"\",\n        assessmentTime: \"\",\n        assignerLocation: \"\"\n    });\nconst QuotationFollowupCalculationFields = [\n    \"salesTaxR\",\n    \"salesTaxA\",\n    \"netAmount\",\n    \"discountPercent\",\n    \"discountAmount\",\n    \"netPayableAmt\",\n    \"sTaxAmount\",\n    \"totalAmount\",\n    \"freight\"\n];\nconst AssesserFollowUp = ()=>{\n    _s();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const [isDisabled, setIsDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tableData, setTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupEmptyTableRow());\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupInitialFormData());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [printData, setPrintData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [audios, setAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedImages, setLoadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedAudios, setLoadedAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isPrintModalOpen, setPrintModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handlePrint = ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const data = {\n            ...formData,\n            items: tableData,\n            signature\n        };\n        setPrintData(data);\n        setPrintModalOpen(true);\n    };\n    const handleGeneratePDFFromComponent = async (pdfBlob)=>{\n        try {\n            // Ensure the component is properly rendered and attached to DOM\n            if (!pdfBlob) {\n                throw new Error(\"No PDF blob provided\");\n            }\n            const filename = \"Assessment_Report_\".concat((formData === null || formData === void 0 ? void 0 : formData.location) || \"EAM\", \"_\").concat((formData === null || formData === void 0 ? void 0 : formData.vno) || \"N/A\", \"_\").concat(dayjs__WEBPACK_IMPORTED_MODULE_13___default()().format(\"YYYY-MM-DD\"), \".pdf\");\n            const namedPdfBlob = new File([\n                pdfBlob\n            ], filename, {\n                type: \"application/pdf\",\n                lastModified: Date.now()\n            });\n            // Download PDF\n            const downloadUrl = URL.createObjectURL(namedPdfBlob);\n            const downloadLink = document.createElement(\"a\");\n            downloadLink.href = downloadUrl;\n            downloadLink.download = filename;\n            document.body.appendChild(downloadLink);\n            downloadLink.click();\n            document.body.removeChild(downloadLink);\n            URL.revokeObjectURL(downloadUrl);\n            // Send PDF via email\n            await handleSendPdf(namedPdfBlob);\n            // Save the form\n            await handleSave();\n            toast({\n                title: \"Process completed successfully\",\n                description: \"PDF generated, emailed, and form saved.\",\n                status: \"success\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n            // Close the modal after successful completion\n            setPrintModalOpen(false);\n        } catch (error) {\n            console.error(\"Error in PDF process:\", error);\n            toast({\n                title: \"Error in PDF process\",\n                description: error.message || \"There was an error during the PDF generation process.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        }\n    };\n    const handleSendPdf = async (pdfBlob)=>{\n        try {\n            if (formData.email && formData.subject && formData.clientTitle && formData.quotation) {\n                const newFormData = new FormData();\n                newFormData.append(\"pdf\", pdfBlob);\n                newFormData.append(\"to\", formData.email);\n                newFormData.append(\"subject\", formData.subject);\n                newFormData.append(\"clientName\", formData.clientTitle);\n                newFormData.append(\"quotation\", formData.quotation);\n                await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"email/quotation-email-with-file\", newFormData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error sending PDF:\", error);\n            toast({\n                title: \"Error sending PDF.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleInputChange = (singleInput, bulkInput)=>{\n        if (singleInput) {\n            let { name, value } = singleInput.target || singleInput;\n            if (QuotationFollowupCalculationFields.includes(name)) {\n                setFormData((prev)=>{\n                    const salesTaxR = name === \"salesTaxR\" ? Number(value) : Number(prev.salesTaxR);\n                    const salesTaxA = name === \"salesTaxA\" ? Number(value) : Number(prev.salesTaxA);\n                    const freight = name === \"freight\" ? Number(value) : Number(prev.freight);\n                    const discountPercent = name === \"discountPercent\" ? value : prev.discountPercent;\n                    let discountAmount = name === \"discountAmount\" ? value : prev.discountAmount;\n                    const totalAmount = prev.totalAmount;\n                    let sTaxAmount = prev.sTaxAmount;\n                    let netAmount = prev.netAmount;\n                    if (salesTaxR + salesTaxA > 100) {\n                        sTaxAmount = 0;\n                    } else {\n                        const totalPercentage = (salesTaxR + salesTaxA) / 100;\n                        sTaxAmount = totalAmount * totalPercentage;\n                    }\n                    if (name !== \"netAmount\") {\n                        netAmount = totalAmount + sTaxAmount;\n                    }\n                    discountAmount = discountPercent / 100 * netAmount;\n                    const netPayableAmt = netAmount + freight - discountAmount;\n                    return {\n                        ...prev,\n                        [name]: value,\n                        salesTaxR,\n                        salesTaxA,\n                        sTaxAmount,\n                        discountAmount,\n                        totalAmount,\n                        netAmount,\n                        netPayableAmt\n                    };\n                });\n            } else {\n                setFormData((prev)=>({\n                        ...prev,\n                        [name]: value\n                    }));\n            }\n        } else if (bulkInput) {\n            setFormData((prev)=>({\n                    ...prev,\n                    ...bulkInput\n                }));\n        }\n    };\n    const transformData = function() {\n        let orderData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, itemsArray = arguments.length > 1 ? arguments[1] : void 0, isNavigationdata = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (isNavigationdata) {\n            return itemsArray.map((item)=>{\n                return {\n                    Item_ID: item === null || item === void 0 ? void 0 : item.item_id,\n                    Item_Title: item === null || item === void 0 ? void 0 : item.itemTitle,\n                    Rate: Number(item === null || item === void 0 ? void 0 : item.Rate),\n                    InstallationCharges: Number(item === null || item === void 0 ? void 0 : item.InstallationCharges),\n                    Disc: Number(item === null || item === void 0 ? void 0 : item.Discount),\n                    Total: Number(item === null || item === void 0 ? void 0 : item.Total),\n                    Date: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(item === null || item === void 0 ? void 0 : item.VisitDate), true),\n                    Details: item === null || item === void 0 ? void 0 : item.details,\n                    Qty: Number(item === null || item === void 0 ? void 0 : item.Qty)\n                };\n            });\n        } else {\n            return itemsArray.map((item, index)=>{\n                return {\n                    Dated: orderData.Dated,\n                    VTP: orderData.VTP,\n                    Mnth: orderData.Mnth,\n                    Location: orderData.Location,\n                    vno: orderData.vno,\n                    srno: index + 1,\n                    item_id: item.Item_ID,\n                    Rate: Number(item.Rate),\n                    InstallationCharges: Number(item.InstallationCharges),\n                    Discount: Number(item.Disc),\n                    Total: Number(item.Total),\n                    VisitDate: item.Date,\n                    details: item.Details,\n                    Qty: Number(item.Qty)\n                };\n            });\n        }\n    };\n    const rowClickHandler = (data, rowIndex, colIndex)=>{\n        const isExist = tableData.find((modal)=>modal.Item_ID === data.Item_ID);\n        if (isExist) {\n            setTableData((prev)=>{\n                const updatedTableData = prev.map((item)=>{\n                    if (item.Item_ID === isExist.Item_ID) {\n                        return {\n                            ...item,\n                            qty: item.qty ? Number(item.qty) + 1 : 1\n                        };\n                    }\n                    return item;\n                });\n                return updatedTableData;\n            });\n        } else {\n            setTableData((prev)=>{\n                const updatedTableData = [\n                    ...prev\n                ];\n                updatedTableData[rowIndex] = {\n                    ...updatedTableData[rowIndex],\n                    Item_ID: data.Item_ID ? data.Item_ID : \"\",\n                    Item_Title: data.Item_Title ? data.Item_Title : \"\",\n                    Rate: data.Item_Sale_Rate ? data.Item_Sale_Rate : 0,\n                    Details: data.Item_Details ? data.Item_Details : \"\"\n                };\n                return updatedTableData;\n            });\n        }\n    };\n    const handleImagesChange = (newImages)=>{\n        setImages(newImages);\n    };\n    const handleAudiosChange = (newAudios)=>{\n        setAudios(newAudios);\n    };\n    const cellRender = (value, key, rowIndex, colIndex, cellData, handleInputChange)=>{\n        if ([\n            \"Item_ID\",\n            \"Item_Title\"\n        ].includes(key)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                rowIndex: rowIndex,\n                colIndex: colIndex,\n                inputWidth: cellData.width,\n                value: value,\n                onChange: (val)=>handleInputChange(val),\n                modalData: items,\n                modalHeaders: [\n                    \"ID\",\n                    \"Title\",\n                    \"Details\",\n                    \"Sale_Rate\"\n                ],\n                isDisabled: isDisabled,\n                rowClickHandler: rowClickHandler\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 470,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (cellData.type === \"number\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                width: cellData.width,\n                value: value,\n                onChange: (e)=>handleInputChange(e.target.value),\n                size: \"sm\",\n                isReadOnly: cellData.isReadOnly,\n                isDisabled: isDisabled\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n            width: cellData.width,\n            value: value,\n            onChange: (e)=>handleInputChange(e.target.value),\n            size: \"sm\",\n            type: cellData.type,\n            isReadOnly: cellData.isReadOnly,\n            isDisabled: isDisabled\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, undefined);\n    };\n    const calculation = (header, value, rowIndex)=>{\n        setTableData((prevData)=>{\n            return prevData.map((r, i)=>{\n                if (i === rowIndex) {\n                    const updatedRow = {\n                        ...r,\n                        [header.key]: value\n                    };\n                    const qty = header.key === \"Qty\" ? value : r.Qty;\n                    const rate = header.key === \"Rate\" ? value : r.Rate;\n                    const installationCharges = header.key === \"InstallationCharges\" ? value : r.InstallationCharges;\n                    const discountPercent = header.key === \"Disc\" ? value : r.Disc;\n                    const total = (Number(qty) || 0) * (Number(rate) || 0);\n                    const discountAmount = discountPercent / 100 * total;\n                    updatedRow.Total = total - discountAmount + (Number(installationCharges) || 0);\n                    return updatedRow;\n                }\n                return r;\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let totalQty = 0;\n        let totalAmount = 0;\n        tableData.forEach((data)=>{\n            totalAmount += Number(data.Total) || 0;\n        });\n        const salesTaxR = formData.salesTaxR;\n        const salesTaxA = formData.salesTaxA;\n        const freight = formData.freight;\n        const discountPercent = formData.discountPercent;\n        let discountAmount = formData.discountAmount;\n        let sTaxAmount = formData.sTaxAmount;\n        let netAmount = formData.netAmount;\n        if (salesTaxR + salesTaxA > 100) {\n            sTaxAmount = 0;\n        } else {\n            const totalPercentage = (salesTaxR + salesTaxA) / 100;\n            sTaxAmount = totalAmount * totalPercentage;\n        }\n        discountAmount = discountPercent / 100 * netAmount;\n        const netPayableAmt = netAmount + freight - discountAmount;\n        setFormData((prev)=>({\n                ...prev,\n                totalQty,\n                totalAmount,\n                netAmount,\n                netPayableAmt,\n                sTaxAmount,\n                discountAmount\n            }));\n    }, [\n        tableData\n    ]);\n    const getVoucherNo = async (date)=>{\n        if (date) {\n            setLoading(true);\n            const year = new Date(date).getFullYear();\n            try {\n                const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/getVoucherNo\", {\n                    Mnth: year.toString()\n                });\n                const { location, vno, vtp, Mnth, voucherNo } = response.data || {};\n                if (location, vno, vtp, Mnth, voucherNo) {\n                    setFormData((prevFormData)=>({\n                            ...prevFormData,\n                            location,\n                            vno,\n                            vtp,\n                            Mnth,\n                            voucherNo,\n                            mnth: Mnth\n                        }));\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Error fetching voucher number:\", error);\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please Select a Date First.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const navigateVoucherForm = async (voucherNo)=>{\n        setLoading(true);\n        setIsDisabled(true);\n        try {\n            const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/navigate\", {\n                goto: true,\n                voucher_no: voucherNo\n            });\n            const fileResponse = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"offer/get-files\", {\n                params: {\n                    refVoucherNo: voucherNo\n                }\n            });\n            // Process files by type\n            const allFiles = fileResponse.data;\n            const images = allFiles.filter((file)=>file.Type === \"image\" && file.FileName !== \"signature.png\");\n            const audios = allFiles.filter((file)=>file.Type === \"audio\");\n            // Find signature if it exists\n            const signatureFile = allFiles.find((file)=>file.FileName === \"signature.png\");\n            if (signatureFile) {\n                // Convert buffer to base64 string for signature pad\n                const buffer = Buffer.from(signatureFile.FileData.data);\n                const base64String = \"data:image/png;base64,\".concat(buffer.toString(\"base64\"));\n                setSignature(base64String);\n            }\n            setLoadedAudios(audios);\n            setLoadedImages(images);\n            const resData = response.data;\n            const { items } = response.data;\n            var _resData_client_id, _resData_ClientName, _resData_EmployeeID, _resData_EmployeeName, _resData_AssessmentLocation, _resData_SalesTaxA, _resData_SalesTaxR, _resData_Discount, _resData_DiscountPercent, _resData_GrossAmount, _resData_Lead, _resData_TenderNo, _resData_AttentionPerson, _resData_AttentionPerson_Desig, _resData_ContactPerson, _resData_Currency_ID, _resData_Subject, _resData_Quotation, _resData_GrdTotalPSTAmt, _resData_Freight, _resData_Validity, _resData_Terms, _resData_NetAmount, _resData_StartingComments;\n            const form = {\n                date: (resData === null || resData === void 0 ? void 0 : resData.Dated) ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(resData === null || resData === void 0 ? void 0 : resData.Dated), true) : null,\n                vtp: resData === null || resData === void 0 ? void 0 : resData.VTP,\n                mnth: resData === null || resData === void 0 ? void 0 : resData.Mnth,\n                location: resData === null || resData === void 0 ? void 0 : resData.Location,\n                vno: resData === null || resData === void 0 ? void 0 : resData.vno,\n                voucherNo: resData === null || resData === void 0 ? void 0 : resData.Voucher_No,\n                clientId: (_resData_client_id = resData === null || resData === void 0 ? void 0 : resData.client_id) !== null && _resData_client_id !== void 0 ? _resData_client_id : \"\",\n                clientTitle: (_resData_ClientName = resData === null || resData === void 0 ? void 0 : resData.ClientName) !== null && _resData_ClientName !== void 0 ? _resData_ClientName : \"\",\n                assignerId: (_resData_EmployeeID = resData === null || resData === void 0 ? void 0 : resData.EmployeeID) !== null && _resData_EmployeeID !== void 0 ? _resData_EmployeeID : \"\",\n                assignerTitle: (_resData_EmployeeName = resData === null || resData === void 0 ? void 0 : resData.EmployeeName) !== null && _resData_EmployeeName !== void 0 ? _resData_EmployeeName : \"\",\n                assessmentTime: (resData === null || resData === void 0 ? void 0 : resData.AssessmentTime) ? dayjs__WEBPACK_IMPORTED_MODULE_13___default()(resData === null || resData === void 0 ? void 0 : resData.AssessmentTime).format(\"YYYY-MM-DDTHH:mm\") : \"\",\n                assignerLocation: (_resData_AssessmentLocation = resData === null || resData === void 0 ? void 0 : resData.AssessmentLocation) !== null && _resData_AssessmentLocation !== void 0 ? _resData_AssessmentLocation : \"\",\n                salesTaxA: (_resData_SalesTaxA = resData === null || resData === void 0 ? void 0 : resData.SalesTaxA) !== null && _resData_SalesTaxA !== void 0 ? _resData_SalesTaxA : 0,\n                salesTaxR: (_resData_SalesTaxR = resData === null || resData === void 0 ? void 0 : resData.SalesTaxR) !== null && _resData_SalesTaxR !== void 0 ? _resData_SalesTaxR : 0,\n                discountAmount: (_resData_Discount = resData === null || resData === void 0 ? void 0 : resData.Discount) !== null && _resData_Discount !== void 0 ? _resData_Discount : 0,\n                discountPercent: (_resData_DiscountPercent = resData === null || resData === void 0 ? void 0 : resData.DiscountPercent) !== null && _resData_DiscountPercent !== void 0 ? _resData_DiscountPercent : 0,\n                netPayableAmt: (_resData_GrossAmount = resData === null || resData === void 0 ? void 0 : resData.GrossAmount) !== null && _resData_GrossAmount !== void 0 ? _resData_GrossAmount : 0,\n                lead: (_resData_Lead = resData === null || resData === void 0 ? void 0 : resData.Lead) !== null && _resData_Lead !== void 0 ? _resData_Lead : \"\",\n                tenderNo: (_resData_TenderNo = resData === null || resData === void 0 ? void 0 : resData.TenderNo) !== null && _resData_TenderNo !== void 0 ? _resData_TenderNo : \"\",\n                // exchangeRate: resData?.ExchRate ?? 0,\n                // partyRef: resData?.Party_ref ?? \"\",\n                attentionPerson: (_resData_AttentionPerson = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson) !== null && _resData_AttentionPerson !== void 0 ? _resData_AttentionPerson : \"\",\n                designation: (_resData_AttentionPerson_Desig = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson_Desig) !== null && _resData_AttentionPerson_Desig !== void 0 ? _resData_AttentionPerson_Desig : \"\",\n                contactPerson: (_resData_ContactPerson = resData === null || resData === void 0 ? void 0 : resData.ContactPerson) !== null && _resData_ContactPerson !== void 0 ? _resData_ContactPerson : \"\",\n                currency: (_resData_Currency_ID = resData === null || resData === void 0 ? void 0 : resData.Currency_ID) !== null && _resData_Currency_ID !== void 0 ? _resData_Currency_ID : \"\",\n                subject: (_resData_Subject = resData === null || resData === void 0 ? void 0 : resData.Subject) !== null && _resData_Subject !== void 0 ? _resData_Subject : \"\",\n                quotation: (_resData_Quotation = resData === null || resData === void 0 ? void 0 : resData.Quotation) !== null && _resData_Quotation !== void 0 ? _resData_Quotation : \"\",\n                totalAmount: (_resData_GrdTotalPSTAmt = resData === null || resData === void 0 ? void 0 : resData.GrdTotalPSTAmt) !== null && _resData_GrdTotalPSTAmt !== void 0 ? _resData_GrdTotalPSTAmt : 0,\n                freight: (_resData_Freight = resData === null || resData === void 0 ? void 0 : resData.Freight) !== null && _resData_Freight !== void 0 ? _resData_Freight : 0,\n                validityDays: (_resData_Validity = resData === null || resData === void 0 ? void 0 : resData.Validity) !== null && _resData_Validity !== void 0 ? _resData_Validity : 0,\n                paymentTerms: (_resData_Terms = resData === null || resData === void 0 ? void 0 : resData.Terms) !== null && _resData_Terms !== void 0 ? _resData_Terms : \"\",\n                netAmount: (_resData_NetAmount = resData === null || resData === void 0 ? void 0 : resData.NetAmount) !== null && _resData_NetAmount !== void 0 ? _resData_NetAmount : 0,\n                narration: (_resData_StartingComments = resData === null || resData === void 0 ? void 0 : resData.StartingComments) !== null && _resData_StartingComments !== void 0 ? _resData_StartingComments : \"\"\n            };\n            setTableData(()=>transformData([], items, true));\n            setFormData(form);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching goto voucher:\", error);\n            setLoading(false);\n            toast({\n                title: \"goto Voucher Fetching Failed !\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const loadInitialData = async ()=>{\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const { data: purposeData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"purpose/\" + purpose_no);\n        if (purposeData.RefVoucherNo) {\n            navigateVoucherForm(purposeData.RefVoucherNo);\n        } else {\n            const newDate = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(dayjs__WEBPACK_IMPORTED_MODULE_13___default()(), true);\n            setFormData((prev)=>({\n                    ...prev,\n                    date: newDate\n                }));\n            getVoucherNo(newDate);\n        }\n        const { data: clientData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"client/\" + (purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID));\n        const items = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"getRecords/items\");\n        setFormData((prev)=>({\n                ...prev,\n                assignerId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessorID,\n                assessmentTime: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessmentTime,\n                assignerLocation: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                address: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                email: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientEmail,\n                phoneNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientMobileNo,\n                landlineNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTelephone,\n                clientId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID,\n                clientTitle: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTitle,\n                contact: purposeData === null || purposeData === void 0 ? void 0 : purposeData.contactPerson,\n                comments: purposeData === null || purposeData === void 0 ? void 0 : purposeData.remarks,\n                ...clientData\n            }));\n        const itemData = [];\n        items.map((item)=>{\n            itemData.push({\n                Item_ID: item.id,\n                Item_Title: item.Title,\n                // Item_Unit: item.Unit,\n                Item_Details: item.Details,\n                Item_Sale_Rate: item.Sale_Rate\n            });\n        });\n        setItems(itemData);\n    };\n    // Toolbar funtions starts here\n    const handleSave = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const data = {\n            Dated: formData.date ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date) : null,\n            VTP: formData.vtp,\n            Mnth: formData.mnth,\n            Location: formData.location,\n            vno: formData.vno,\n            Voucher_No: formData.voucherNo,\n            Currency_ID: formData.currency,\n            Validity: formData.validityDays,\n            Lead: formData.lead,\n            EmployeeID: formData.assignerId,\n            AssessmentTime: formData.assessmentTime ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.assessmentTime) : null,\n            AssessmentLocation: formData.assignerLocation,\n            TenderNo: formData.tenderNo,\n            SalesTaxA: formData.salesTaxA,\n            SalesTaxR: formData.salesTaxR,\n            DiscountPercent: formData.discountPercent,\n            Discount: formData.discountAmount,\n            GrossAmount: formData.netPayableAmt,\n            ContactPerson: formData.contactPerson,\n            Subject: formData.subject,\n            Quotation: formData.quotation,\n            GrdTotalPSTAmt: formData.totalAmount,\n            prp_id: user.id,\n            Freight: formData.freight,\n            NetAmount: formData.netAmount,\n            Terms: formData.paymentTerms,\n            StartingComments: formData.narration,\n            client_id: formData.clientId,\n            CreationDate: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date()),\n            items: transformData({\n                Dated: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date),\n                VTP: formData.vtp,\n                Mnth: formData.mnth,\n                Location: formData.location,\n                vno: formData.vno\n            }, tableData)\n        };\n        const isValidateObjectFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateObjectFields)(data, QuotationFollowupRequiredFields);\n        const isValidateArrayFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateArrayFields)(data.items, QuotationFollowupItemsRequiredFields);\n        if (isValidateObjectFields.error) {\n            toast({\n                title: isValidateObjectFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateArrayFields.error) {\n            toast({\n                title: isValidateArrayFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {\n            setLoading(true);\n            try {\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"post\",\n                    url: \"offer/create\",\n                    data: data\n                });\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"put\",\n                    url: \"purpose/update/\" + purpose_no,\n                    data: {\n                        RefVoucherNo: formData.voucherNo,\n                        RefVTP: formData.vtp,\n                        RefMnth: formData.mnth,\n                        RefLocation: formData.location,\n                        RefVNo: formData.vno\n                    }\n                });\n                handleStoreImages();\n                handleStoreAudios();\n                handleStoreSignature();\n                setIsDisabled(true);\n                toast({\n                    title: \"Quotation Followup Form saved successfully :)\",\n                    status: \"success\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error saving Quotation Followup Form:\", error);\n                toast({\n                    title: \"Error saving Quotation Followup Form :(\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please fill out all required fields and ensure all items have valid fields\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleStoreImages = async ()=>{\n        if (!images || images.length === 0) {\n            toast({\n                title: \"No files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = images.map(async (image)=>{\n                const formData = new FormData();\n                formData.append(\"file\", image.file); // Attach the file\n                formData.append(\"timestamp\", image.timestamp);\n                formData.append(\"location\", JSON.stringify(image.location));\n                formData.append(\"googleMapsLink\", image.googleMapsLink);\n                formData.append(\"type\", \"image\"); // Adjust type if needed (e.g., 'video', 'audio')\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Image \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Image \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading files:\", error);\n            toast({\n                title: \"Error uploading files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreAudios = async ()=>{\n        if (!audios || audios.length === 0) {\n            toast({\n                title: \"No audio files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = audios.map(async (audio)=>{\n                const formData = new FormData();\n                formData.append(\"file\", audio.file); // Attach the file\n                formData.append(\"timestamp\", audio.timestamp);\n                formData.append(\"location\", JSON.stringify(audio.location));\n                formData.append(\"googleMapsLink\", audio.googleMapsLink);\n                formData.append(\"type\", \"audio\"); // Specify type as 'audio'\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Audio \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Audio \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading audio files:\", error);\n            toast({\n                title: \"Error uploading audio files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreSignature = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"No signature to upload\",\n                status: \"info\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            // Convert data URL to a Blob\n            const fetchResponse = await fetch(signature);\n            const signatureBlob = await fetchResponse.blob();\n            // Create a File object\n            const signatureFile = new File([\n                signatureBlob\n            ], \"signature.png\", {\n                type: \"image/png\"\n            });\n            const signatureFormData = new FormData();\n            signatureFormData.append(\"file\", signatureFile);\n            signatureFormData.append(\"timestamp\", new Date().toISOString());\n            signatureFormData.append(\"location\", JSON.stringify({}));\n            signatureFormData.append(\"googleMapsLink\", \"\");\n            signatureFormData.append(\"refVoucherNo\", refVoucherNo);\n            signatureFormData.append(\"refVTP\", refVTP);\n            signatureFormData.append(\"refMnth\", refMnth);\n            signatureFormData.append(\"refLocation\", refLocation);\n            signatureFormData.append(\"refVNo\", refVNo);\n            await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-signature\", signatureFormData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading signature:\", error);\n            toast({\n                title: \"Error uploading signature.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Toolbar funtions ends here\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 1103,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"20px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Site Assessment Follow-Up\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        style: {\n                                            gap: \"10px\",\n                                            paddingTop: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            margin: \"0\",\n                                                            textAlign: \"center\",\n                                                            color: \"#2B6CB0\",\n                                                            fontSize: \"20px\",\n                                                            fontWeight: \"bold\"\n                                                        },\n                                                        children: \"SOP's\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"1. Is the property more than 2 years old ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1155,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"2. Is the combined yearly income of the household equal to or less than 180,000 ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1164,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"3. Are you ready to decommission the ducted gas heated system ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1174,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1173,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1168,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        flexWrap: {\n                                                            base: \"wrap\",\n                                                            sm: \"wrap\",\n                                                            md: \"wrap\",\n                                                            lg: \"nowrap\"\n                                                        },\n                                                        gap: \"10px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"80%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                className: \"\",\n                                                                sx: {\n                                                                    display: \"grid\",\n                                                                    gridTemplateColumns: {\n                                                                        base: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        sm: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        md: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                                    },\n                                                                    gap: \"5px\"\n                                                                },\n                                                                children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[0].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            flexWrap: {\n                                                                                base: \"wrap\",\n                                                                                sm: \"wrap\",\n                                                                                md: \"wrap\",\n                                                                                lg: \"nowrap\"\n                                                                            },\n                                                                            flexDirection: {\n                                                                                base: \"column\",\n                                                                                sm: \"column\",\n                                                                                md: \"row\"\n                                                                            },\n                                                                            marginTop: \"10px\"\n                                                                        },\n                                                                        isRequired: field.isRequired,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                                htmlFor: field.id,\n                                                                                sx: {\n                                                                                    marginBottom: \"0\",\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"27%\"\n                                                                                    }\n                                                                                },\n                                                                                children: field.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1246,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            field.type === \"date\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                // min={field.minDate}\n                                                                                // max={field.maxDate}\n                                                                                disabled: isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1261,\n                                                                                columnNumber: 37\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                disabled: field.name === \"voucherNo\" ? isDisabled : isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1288,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, field.id, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 33\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1211,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"20%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                sx: {\n                                                                    height: \"100%\"\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                                    sx: {\n                                                                        width: \"100%\",\n                                                                        height: {\n                                                                            base: \"inherit\",\n                                                                            md: \"inherit\",\n                                                                            lg: \"calc(100% - 10px)\"\n                                                                        },\n                                                                        margin: \"10px 0\"\n                                                                    },\n                                                                    bg: \"#2d6651\",\n                                                                    color: \"white\",\n                                                                    _hover: {\n                                                                        bg: \"#3a866a\"\n                                                                    },\n                                                                    onClick: ()=>getVoucherNo(formData.date),\n                                                                    isDisabled: isDisabled,\n                                                                    children: \"Generate Voucher No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1333,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1322,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    className: \"pt-4 pb-4\",\n                                                    sx: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: {\n                                                            base: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            sm: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            md: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                        },\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[1].map((control, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                },\n                                                                marginTop: \"10px\",\n                                                                flexWrap: \"nowrap\"\n                                                            },\n                                                            isRequired: control.isRequired,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                    htmlFor: control.fields[0].name,\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"20%\"\n                                                                        }\n                                                                    },\n                                                                    children: control.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1394,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"80%\"\n                                                                        },\n                                                                        display: \"flex\",\n                                                                        gap: control.fields.length > 1 ? \"10px\" : \"0\"\n                                                                    },\n                                                                    children: control.fields.map((field, fieldIndex)=>field.component === \"ComboBox\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            target: true,\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            inputWidths: field.inputWidths,\n                                                                            buttonWidth: field.buttonWidth,\n                                                                            styleButton: {\n                                                                                padding: \"3px !important\"\n                                                                            },\n                                                                            tableData: [],\n                                                                            tableHeaders: field.tableHeaders,\n                                                                            nameFields: field.nameFields,\n                                                                            placeholders: field.placeholders,\n                                                                            keys: field.keys,\n                                                                            form: formData,\n                                                                            isDisabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1419,\n                                                                            columnNumber: 37\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            placeholder: field.placeholder,\n                                                                            value: formData[field.value],\n                                                                            _placeholder: field._placeholder,\n                                                                            type: field.type,\n                                                                            style: {\n                                                                                width: field.inputWidth\n                                                                            },\n                                                                            disabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 37\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1406,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1354,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    tableData: tableData,\n                                                    setTableData: setTableData,\n                                                    headers: QuotationFollowupHeaders,\n                                                    tableWidth: \"100%\",\n                                                    tableHeight: \"400px\",\n                                                    fontSize: \"lg\",\n                                                    cellRender: cellRender,\n                                                    styleHead: {\n                                                        background: \"#3275bb\",\n                                                        color: \"white !important\"\n                                                    },\n                                                    styleBody: {\n                                                        background: \"white !important\"\n                                                    },\n                                                    calculation: calculation,\n                                                    isDisabled: isDisabled\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            initial: loadedImages,\n                                                            onChange: handleImagesChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1518,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1511,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1496,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            initial: loadedAudios,\n                                                            onChange: handleAudiosChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1559,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1552,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1537,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 pb-2\",\n                                                    style: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: \"repeat(auto-fit,minmax(300px,1fr))\",\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Total Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1601,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"totalAmount\",\n                                                                        value: formData.totalAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1612,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1589,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1588,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1643,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxR\",\n                                                                                value: formData.salesTaxR,\n                                                                                placeholder: \"R\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1665,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxA\",\n                                                                                value: formData.salesTaxA,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1681,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1654,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1631,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1630,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax Amt.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1713,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"sTaxAmount\",\n                                                                        value: formData.sTaxAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1724,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1701,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1700,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1755,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netAmount\",\n                                                                        value: formData.netAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1766,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1743,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1742,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Discount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1797,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountPercent\",\n                                                                                value: formData.discountPercent,\n                                                                                placeholder: \"%\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1819,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountAmount\",\n                                                                                value: formData.discountAmount,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: true,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1835,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1808,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1785,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1784,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Freight\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1867,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"freight\",\n                                                                        value: formData.freight,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1878,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1855,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1854,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Payable Amt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1909,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netPayableAmt\",\n                                                                        value: formData.netPayableAmt,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1920,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1897,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1896,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Validity Days\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1951,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"validityDays\",\n                                                                        value: formData.validityDays,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1962,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1939,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1938,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            gridColumn: \"1 / -1\",\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"10%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Payment Terms\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1996,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"paymentTerms\",\n                                                                        value: formData.paymentTerms,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        type: \"text\",\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"90%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2007,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1984,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1980,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1579,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1567,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(75% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Remarks\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                                                            _placeholder: {\n                                                                color: \"gray.500\"\n                                                            },\n                                                            resize: \"vertical\",\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                height: \"100%\"\n                                                            },\n                                                            onChange: handleInputChange,\n                                                            name: \"narration\",\n                                                            value: formData.narration,\n                                                            disabled: isDisabled,\n                                                            rows: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2056,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2039,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2028,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(25% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Digital Signature\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2092,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                display: \"flex\",\n                                                                flexDirection: \"column\",\n                                                                alignItems: \"center\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                onSave: (url)=>setSignature(url),\n                                                                onClose: ()=>setSignature(null),\n                                                                initialSignature: signature,\n                                                                isDisabled: isDisabled\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 2107,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2099,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2082,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2071,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                lineNumber: 1109,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                            lineNumber: 1108,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 1107,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 1106,\n                    columnNumber: 11\n                }, undefined),\n                !isDisabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    save: handlePrint,\n                    hide: [\n                        \"First\",\n                        \"Edit\",\n                        \"Last\",\n                        \"Previous\",\n                        \"Next\",\n                        \"Delete\",\n                        \"Goto\",\n                        \"Cancel\",\n                        \"Clear\",\n                        \"Check\",\n                        \"Print\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2122,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PrintModal__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isOpen: isPrintModalOpen,\n                    onClose: ()=>setPrintModalOpen(false),\n                    formName: \"Site Assessment Quotation\",\n                    showHeader: false,\n                    buttonText: \"Save & Generate PDF\",\n                    callback: handleGeneratePDFFromComponent,\n                    paddingTop: 0,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        data: printData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 2148,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2139,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(AssesserFollowUp, \"Y00UvtCWtYp7LkfYjThmtmHCCrE=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast,\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = AssesserFollowUp;\nconst AssesserFollowUpPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2157,\n            columnNumber: 23\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AssesserFollowUp, {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2158,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n        lineNumber: 2157,\n        columnNumber: 3\n    }, undefined);\n_c1 = AssesserFollowUpPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AssesserFollowUpPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"AssesserFollowUp\");\n$RefreshReg$(_c1, \"AssesserFollowUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx\n"));

/***/ })

});