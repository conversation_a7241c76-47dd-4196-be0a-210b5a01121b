"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/AssesserFollowUp.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _src_components_sidebar_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @src/components/sidebar/sidebar */ \"(app-pages-browser)/./src/components/sidebar/sidebar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Custom/ComboBox/ComboBox */ \"(app-pages-browser)/./src/components/Custom/ComboBox/ComboBox.jsx\");\n/* harmony import */ var _components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Custom/TableComboBox/TableComboBox */ \"(app-pages-browser)/./src/components/Custom/TableComboBox/TableComboBox.jsx\");\n/* harmony import */ var _components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @components/Custom/AanzaDataTable/AanzaDataTable */ \"(app-pages-browser)/./src/components/Custom/AanzaDataTable/AanzaDataTable.jsx\");\n/* harmony import */ var _axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/Toolbar/Toolbar */ \"(app-pages-browser)/./src/components/Toolbar/Toolbar.jsx\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @utils/functions */ \"(app-pages-browser)/./src/app/utils/functions.js\");\n/* harmony import */ var _utils_constant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @utils/constant */ \"(app-pages-browser)/./src/app/utils/constant.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @components/Custom/SignaturePad/SignaturePad */ \"(app-pages-browser)/./src/components/Custom/SignaturePad/SignaturePad.jsx\");\n/* harmony import */ var _components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @components/Custom/MultipleImageUploader/MultipleImageUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleImageUploader/MultipleImageUploader.jsx\");\n/* harmony import */ var _components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @components/Custom/MultipleAudioUploader/MultipleAudioUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleAudioUploader/MultipleAudioUploader.jsx\");\n/* harmony import */ var _components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @components/Custom/NumberInput/NumberInput */ \"(app-pages-browser)/./src/components/Custom/NumberInput/NumberInput.jsx\");\n/* harmony import */ var _components_PrintModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @components/PrintModal */ \"(app-pages-browser)/./src/components/PrintModal.jsx\");\n/* harmony import */ var _ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./ComponentToPrint */ \"(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst QuotationFollowupRequiredFields = [];\nconst QuotationFollowupItemsRequiredFields = [];\nconst QuotationFollowupHeaders = [\n    {\n        label: \"Item ID\",\n        key: \"Item_ID\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Item Description\",\n        key: \"Item_Title\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Qty\",\n        key: \"Qty\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Rate\",\n        key: \"Rate\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Disc %\",\n        key: \"Disc\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Installation Charges\",\n        key: \"InstallationCharges\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Total\",\n        key: \"Total\",\n        width: \"100px\",\n        isReadOnly: true,\n        type: \"number\"\n    },\n    {\n        label: \"Delivery Date\",\n        key: \"Date\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"date\"\n    },\n    {\n        label: \"Details\",\n        key: \"Details\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    }\n];\nconst createQuotationFollowupEmptyTableRow = ()=>[\n        {\n            Item_ID: \"\",\n            Item_Title: \"\",\n            Qty: 0,\n            Rate: 0,\n            InstallationCharges: 0,\n            Disc: 0,\n            Total: 0,\n            Date: \"\",\n            Details: \"\"\n        }\n    ];\nconst createQuotationFollowupInitialFormData = ()=>({\n        location: \"\",\n        vno: \"\",\n        vtp: \"\",\n        voucherNo: \"\",\n        date: \"\",\n        mnth: \"\",\n        tenderNo: \"\",\n        exchangeRate: 0,\n        partyRef: \"\",\n        attentionPerson: \"\",\n        designation: \"\",\n        contactPerson: \"\",\n        email: \"\",\n        phoneNumber: \"\",\n        landline: \"\",\n        address: \"\",\n        lead: \"\",\n        clientId: \"\",\n        clientTitle: \"\",\n        currency: \"\",\n        subject: \"Quotation for Requested Items\",\n        quotation: \"Please find below the quotation for the items you inquired about. The prices mentioned are inclusive of all applicable charges. Let us know if you need any adjustments or have further queries.\",\n        totalAmount: 0,\n        freight: 0,\n        netAmount: 0,\n        validityDays: 0,\n        paymentTerms: \"\",\n        narration: \"\",\n        salesTaxR: \"\",\n        salesTaxA: \"\",\n        discountPercent: \"\",\n        discountAmount: 0,\n        netPayableAmt: 0,\n        sTaxAmount: 0,\n        assignerId: \"\",\n        assignerTitle: \"\",\n        assessmentTime: \"\",\n        assignerLocation: \"\"\n    });\nconst QuotationFollowupCalculationFields = [\n    \"salesTaxR\",\n    \"salesTaxA\",\n    \"netAmount\",\n    \"discountPercent\",\n    \"discountAmount\",\n    \"netPayableAmt\",\n    \"sTaxAmount\",\n    \"totalAmount\",\n    \"freight\"\n];\nconst AssesserFollowUp = ()=>{\n    _s();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const [isDisabled, setIsDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tableData, setTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupEmptyTableRow());\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupInitialFormData());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [printData, setPrintData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [audios, setAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedImages, setLoadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedAudios, setLoadedAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isPrintModalOpen, setPrintModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handlePrint = ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const data = {\n            ...formData,\n            items: tableData,\n            signature\n        };\n        setPrintData(data);\n        setPrintModalOpen(true);\n    };\n    const convertImageToBase64 = (url)=>{\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            img.crossOrigin = \"anonymous\";\n            img.onload = ()=>{\n                const canvas = document.createElement(\"canvas\");\n                const ctx = canvas.getContext(\"2d\");\n                canvas.width = img.width;\n                canvas.height = img.height;\n                ctx.drawImage(img, 0, 0);\n                try {\n                    const dataURL = canvas.toDataURL(\"image/png\");\n                    resolve(dataURL);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n            img.src = url;\n        });\n    };\n    const handleGeneratePDFFromComponent = async (componentRef)=>{\n        try {\n            // Ensure the component is properly rendered and attached to DOM\n            if (!componentRef || !componentRef.current) {\n                throw new Error(\"Component reference is not available\");\n            }\n            // Convert logo images to base64 before capturing\n            const logoImages = componentRef.current.querySelectorAll('img[src*=\"godown\"]');\n            const logoPromises = Array.from(logoImages).map(async (img)=>{\n                try {\n                    const base64 = await convertImageToBase64(img.src);\n                    img.src = base64;\n                } catch (error) {\n                    console.warn(\"Could not convert logo to base64:\", error);\n                }\n            });\n            // Wait for all logo conversions to complete\n            await Promise.all(logoPromises);\n            // Wait a bit more for the component to fully render with new images\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            const canvas = await html2canvas(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                imageTimeout: 15000,\n                removeContainer: true\n            });\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jsPDF(\"p\", \"mm\", \"a4\");\n            const imgWidth = 210;\n            const pageHeight = 295;\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n            let heightLeft = imgHeight;\n            let position = 0;\n            pdf.addImage(imgData, \"PNG\", 0, position, imgWidth, imgHeight);\n            heightLeft -= pageHeight;\n            while(heightLeft >= 0){\n                position = heightLeft - imgHeight;\n                pdf.addPage();\n                pdf.addImage(imgData, \"PNG\", 0, position, imgWidth, imgHeight);\n                heightLeft -= pageHeight;\n            }\n            const pdfBlob = pdf.output(\"blob\");\n            const filename = \"Assessment_Report_EAM_\".concat((formData === null || formData === void 0 ? void 0 : formData.vno) || \"N/A\", \"_\").concat(dayjs__WEBPACK_IMPORTED_MODULE_13___default()().format(\"YYYY-MM-DD\"), \".pdf\");\n            const namedPdfBlob = new File([\n                pdfBlob\n            ], filename, {\n                type: \"application/pdf\",\n                lastModified: Date.now()\n            });\n            // Download PDF\n            const downloadUrl = URL.createObjectURL(namedPdfBlob);\n            const downloadLink = document.createElement(\"a\");\n            downloadLink.href = downloadUrl;\n            downloadLink.download = filename;\n            document.body.appendChild(downloadLink);\n            downloadLink.click();\n            document.body.removeChild(downloadLink);\n            URL.revokeObjectURL(downloadUrl);\n            // Send PDF via email\n            await handleSendPdf(namedPdfBlob);\n            // Save the form\n            await handleSave();\n            toast({\n                title: \"Process completed successfully\",\n                description: \"PDF generated, emailed, and form saved.\",\n                status: \"success\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n            // Close the modal after successful completion\n            setPrintModalOpen(false);\n        } catch (error) {\n            console.error(\"Error in PDF process:\", error);\n            toast({\n                title: \"Error in PDF process\",\n                description: error.message || \"There was an error during the PDF generation process.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        }\n    };\n    const handleSendPdf = async (pdfBlob)=>{\n        try {\n            if (formData.email && formData.subject && formData.clientTitle && formData.quotation) {\n                const newFormData = new FormData();\n                newFormData.append(\"pdf\", pdfBlob);\n                newFormData.append(\"to\", \"<EMAIL>\");\n                newFormData.append(\"subject\", formData.subject);\n                newFormData.append(\"clientName\", formData.clientTitle);\n                newFormData.append(\"quotation\", formData.quotation);\n                await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"email/quotation-email-with-file\", newFormData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error sending PDF:\", error);\n            toast({\n                title: \"Error sending PDF.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleInputChange = (singleInput, bulkInput)=>{\n        if (singleInput) {\n            let { name, value } = singleInput.target || singleInput;\n            if (QuotationFollowupCalculationFields.includes(name)) {\n                setFormData((prev)=>{\n                    const salesTaxR = name === \"salesTaxR\" ? Number(value) : Number(prev.salesTaxR);\n                    const salesTaxA = name === \"salesTaxA\" ? Number(value) : Number(prev.salesTaxA);\n                    const freight = name === \"freight\" ? Number(value) : Number(prev.freight);\n                    const discountPercent = name === \"discountPercent\" ? value : prev.discountPercent;\n                    let discountAmount = name === \"discountAmount\" ? value : prev.discountAmount;\n                    const totalAmount = prev.totalAmount;\n                    let sTaxAmount = prev.sTaxAmount;\n                    let netAmount = prev.netAmount;\n                    if (salesTaxR + salesTaxA > 100) {\n                        sTaxAmount = 0;\n                    } else {\n                        const totalPercentage = (salesTaxR + salesTaxA) / 100;\n                        sTaxAmount = totalAmount * totalPercentage;\n                    }\n                    if (name !== \"netAmount\") {\n                        netAmount = totalAmount + sTaxAmount;\n                    }\n                    discountAmount = discountPercent / 100 * netAmount;\n                    const netPayableAmt = netAmount + freight - discountAmount;\n                    return {\n                        ...prev,\n                        [name]: value,\n                        salesTaxR,\n                        salesTaxA,\n                        sTaxAmount,\n                        discountAmount,\n                        totalAmount,\n                        netAmount,\n                        netPayableAmt\n                    };\n                });\n            } else {\n                setFormData((prev)=>({\n                        ...prev,\n                        [name]: value\n                    }));\n            }\n        } else if (bulkInput) {\n            setFormData((prev)=>({\n                    ...prev,\n                    ...bulkInput\n                }));\n        }\n    };\n    const transformData = function() {\n        let orderData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, itemsArray = arguments.length > 1 ? arguments[1] : void 0, isNavigationdata = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (isNavigationdata) {\n            return itemsArray.map((item)=>{\n                return {\n                    Item_ID: item === null || item === void 0 ? void 0 : item.item_id,\n                    Item_Title: item === null || item === void 0 ? void 0 : item.itemTitle,\n                    Rate: Number(item === null || item === void 0 ? void 0 : item.Rate),\n                    InstallationCharges: Number(item === null || item === void 0 ? void 0 : item.InstallationCharges),\n                    Disc: Number(item === null || item === void 0 ? void 0 : item.Discount),\n                    Total: Number(item === null || item === void 0 ? void 0 : item.Total),\n                    Date: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(item === null || item === void 0 ? void 0 : item.VisitDate), true),\n                    Details: item === null || item === void 0 ? void 0 : item.details,\n                    Qty: Number(item === null || item === void 0 ? void 0 : item.Qty)\n                };\n            });\n        } else {\n            return itemsArray.map((item, index)=>{\n                return {\n                    Dated: orderData.Dated,\n                    VTP: orderData.VTP,\n                    Mnth: orderData.Mnth,\n                    Location: orderData.Location,\n                    vno: orderData.vno,\n                    srno: index + 1,\n                    item_id: item.Item_ID,\n                    Rate: Number(item.Rate),\n                    InstallationCharges: Number(item.InstallationCharges),\n                    Discount: Number(item.Disc),\n                    Total: Number(item.Total),\n                    VisitDate: item.Date,\n                    details: item.Details,\n                    Qty: Number(item.Qty)\n                };\n            });\n        }\n    };\n    const rowClickHandler = (data, rowIndex, colIndex)=>{\n        const isExist = tableData.find((modal)=>modal.Item_ID === data.Item_ID);\n        if (isExist) {\n            setTableData((prev)=>{\n                const updatedTableData = prev.map((item)=>{\n                    if (item.Item_ID === isExist.Item_ID) {\n                        return {\n                            ...item,\n                            qty: item.qty ? Number(item.qty) + 1 : 1\n                        };\n                    }\n                    return item;\n                });\n                return updatedTableData;\n            });\n        } else {\n            setTableData((prev)=>{\n                const updatedTableData = [\n                    ...prev\n                ];\n                updatedTableData[rowIndex] = {\n                    ...updatedTableData[rowIndex],\n                    Item_ID: data.Item_ID ? data.Item_ID : \"\",\n                    Item_Title: data.Item_Title ? data.Item_Title : \"\",\n                    Rate: data.Item_Sale_Rate ? data.Item_Sale_Rate : 0,\n                    Details: data.Item_Details ? data.Item_Details : \"\"\n                };\n                return updatedTableData;\n            });\n        }\n    };\n    const handleImagesChange = (newImages)=>{\n        setImages(newImages);\n    };\n    const handleAudiosChange = (newAudios)=>{\n        setAudios(newAudios);\n    };\n    const cellRender = (value, key, rowIndex, colIndex, cellData, handleInputChange)=>{\n        if ([\n            \"Item_ID\",\n            \"Item_Title\"\n        ].includes(key)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                rowIndex: rowIndex,\n                colIndex: colIndex,\n                inputWidth: cellData.width,\n                value: value,\n                onChange: (val)=>handleInputChange(val),\n                modalData: items,\n                modalHeaders: [\n                    \"ID\",\n                    \"Title\",\n                    \"Details\",\n                    \"Sale_Rate\"\n                ],\n                isDisabled: isDisabled,\n                rowClickHandler: rowClickHandler\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 539,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (cellData.type === \"number\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                width: cellData.width,\n                value: value,\n                onChange: (e)=>handleInputChange(e.target.value),\n                size: \"sm\",\n                isReadOnly: cellData.isReadOnly,\n                isDisabled: isDisabled\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 554,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n            width: cellData.width,\n            value: value,\n            onChange: (e)=>handleInputChange(e.target.value),\n            size: \"sm\",\n            type: cellData.type,\n            isReadOnly: cellData.isReadOnly,\n            isDisabled: isDisabled\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 566,\n            columnNumber: 7\n        }, undefined);\n    };\n    const calculation = (header, value, rowIndex)=>{\n        setTableData((prevData)=>{\n            return prevData.map((r, i)=>{\n                if (i === rowIndex) {\n                    const updatedRow = {\n                        ...r,\n                        [header.key]: value\n                    };\n                    const qty = header.key === \"Qty\" ? value : r.Qty;\n                    const rate = header.key === \"Rate\" ? value : r.Rate;\n                    const installationCharges = header.key === \"InstallationCharges\" ? value : r.InstallationCharges;\n                    const discountPercent = header.key === \"Disc\" ? value : r.Disc;\n                    const total = (Number(qty) || 0) * (Number(rate) || 0);\n                    const discountAmount = discountPercent / 100 * total;\n                    updatedRow.Total = total - discountAmount + (Number(installationCharges) || 0);\n                    return updatedRow;\n                }\n                return r;\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let totalQty = 0;\n        let totalAmount = 0;\n        tableData.forEach((data)=>{\n            totalAmount += Number(data.Total) || 0;\n        });\n        const salesTaxR = formData.salesTaxR;\n        const salesTaxA = formData.salesTaxA;\n        const freight = formData.freight;\n        const discountPercent = formData.discountPercent;\n        let discountAmount = formData.discountAmount;\n        let sTaxAmount = formData.sTaxAmount;\n        let netAmount = formData.netAmount;\n        if (salesTaxR + salesTaxA > 100) {\n            sTaxAmount = 0;\n        } else {\n            const totalPercentage = (salesTaxR + salesTaxA) / 100;\n            sTaxAmount = totalAmount * totalPercentage;\n        }\n        discountAmount = discountPercent / 100 * netAmount;\n        const netPayableAmt = netAmount + freight - discountAmount;\n        setFormData((prev)=>({\n                ...prev,\n                totalQty,\n                totalAmount,\n                netAmount,\n                netPayableAmt,\n                sTaxAmount,\n                discountAmount\n            }));\n    }, [\n        tableData\n    ]);\n    const getVoucherNo = async (date)=>{\n        if (date) {\n            setLoading(true);\n            const year = new Date(date).getFullYear();\n            try {\n                const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/getVoucherNo\", {\n                    Mnth: year.toString()\n                });\n                const { location, vno, vtp, Mnth, voucherNo } = response.data || {};\n                if (location, vno, vtp, Mnth, voucherNo) {\n                    setFormData((prevFormData)=>({\n                            ...prevFormData,\n                            location,\n                            vno,\n                            vtp,\n                            Mnth,\n                            voucherNo,\n                            mnth: Mnth\n                        }));\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Error fetching voucher number:\", error);\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please Select a Date First.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const navigateVoucherForm = async (voucherNo)=>{\n        setLoading(true);\n        setIsDisabled(true);\n        try {\n            const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/navigate\", {\n                goto: true,\n                voucher_no: voucherNo\n            });\n            const fileResponse = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"offer/get-files\", {\n                params: {\n                    refVoucherNo: voucherNo\n                }\n            });\n            // Process files by type\n            const allFiles = fileResponse.data;\n            const images = allFiles.filter((file)=>file.Type === \"image\" && file.FileName !== \"signature.png\");\n            const audios = allFiles.filter((file)=>file.Type === \"audio\");\n            // Find signature if it exists\n            const signatureFile = allFiles.find((file)=>file.FileName === \"signature.png\");\n            if (signatureFile) {\n                // Convert buffer to base64 string for signature pad\n                const buffer = Buffer.from(signatureFile.FileData.data);\n                const base64String = \"data:image/png;base64,\".concat(buffer.toString(\"base64\"));\n                setSignature(base64String);\n            }\n            setLoadedAudios(audios);\n            setLoadedImages(images);\n            const resData = response.data;\n            const { items } = response.data;\n            var _resData_client_id, _resData_ClientName, _resData_EmployeeID, _resData_EmployeeName, _resData_AssessmentLocation, _resData_SalesTaxA, _resData_SalesTaxR, _resData_Discount, _resData_DiscountPercent, _resData_GrossAmount, _resData_Lead, _resData_TenderNo, _resData_AttentionPerson, _resData_AttentionPerson_Desig, _resData_ContactPerson, _resData_Currency_ID, _resData_Subject, _resData_Quotation, _resData_GrdTotalPSTAmt, _resData_Freight, _resData_Validity, _resData_Terms, _resData_NetAmount, _resData_StartingComments;\n            const form = {\n                date: (resData === null || resData === void 0 ? void 0 : resData.Dated) ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(resData === null || resData === void 0 ? void 0 : resData.Dated), true) : null,\n                vtp: resData === null || resData === void 0 ? void 0 : resData.VTP,\n                mnth: resData === null || resData === void 0 ? void 0 : resData.Mnth,\n                location: resData === null || resData === void 0 ? void 0 : resData.Location,\n                vno: resData === null || resData === void 0 ? void 0 : resData.vno,\n                voucherNo: resData === null || resData === void 0 ? void 0 : resData.Voucher_No,\n                clientId: (_resData_client_id = resData === null || resData === void 0 ? void 0 : resData.client_id) !== null && _resData_client_id !== void 0 ? _resData_client_id : \"\",\n                clientTitle: (_resData_ClientName = resData === null || resData === void 0 ? void 0 : resData.ClientName) !== null && _resData_ClientName !== void 0 ? _resData_ClientName : \"\",\n                assignerId: (_resData_EmployeeID = resData === null || resData === void 0 ? void 0 : resData.EmployeeID) !== null && _resData_EmployeeID !== void 0 ? _resData_EmployeeID : \"\",\n                assignerTitle: (_resData_EmployeeName = resData === null || resData === void 0 ? void 0 : resData.EmployeeName) !== null && _resData_EmployeeName !== void 0 ? _resData_EmployeeName : \"\",\n                assessmentTime: (resData === null || resData === void 0 ? void 0 : resData.AssessmentTime) ? dayjs__WEBPACK_IMPORTED_MODULE_13___default()(resData === null || resData === void 0 ? void 0 : resData.AssessmentTime).format(\"YYYY-MM-DDTHH:mm\") : \"\",\n                assignerLocation: (_resData_AssessmentLocation = resData === null || resData === void 0 ? void 0 : resData.AssessmentLocation) !== null && _resData_AssessmentLocation !== void 0 ? _resData_AssessmentLocation : \"\",\n                salesTaxA: (_resData_SalesTaxA = resData === null || resData === void 0 ? void 0 : resData.SalesTaxA) !== null && _resData_SalesTaxA !== void 0 ? _resData_SalesTaxA : 0,\n                salesTaxR: (_resData_SalesTaxR = resData === null || resData === void 0 ? void 0 : resData.SalesTaxR) !== null && _resData_SalesTaxR !== void 0 ? _resData_SalesTaxR : 0,\n                discountAmount: (_resData_Discount = resData === null || resData === void 0 ? void 0 : resData.Discount) !== null && _resData_Discount !== void 0 ? _resData_Discount : 0,\n                discountPercent: (_resData_DiscountPercent = resData === null || resData === void 0 ? void 0 : resData.DiscountPercent) !== null && _resData_DiscountPercent !== void 0 ? _resData_DiscountPercent : 0,\n                netPayableAmt: (_resData_GrossAmount = resData === null || resData === void 0 ? void 0 : resData.GrossAmount) !== null && _resData_GrossAmount !== void 0 ? _resData_GrossAmount : 0,\n                lead: (_resData_Lead = resData === null || resData === void 0 ? void 0 : resData.Lead) !== null && _resData_Lead !== void 0 ? _resData_Lead : \"\",\n                tenderNo: (_resData_TenderNo = resData === null || resData === void 0 ? void 0 : resData.TenderNo) !== null && _resData_TenderNo !== void 0 ? _resData_TenderNo : \"\",\n                // exchangeRate: resData?.ExchRate ?? 0,\n                // partyRef: resData?.Party_ref ?? \"\",\n                attentionPerson: (_resData_AttentionPerson = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson) !== null && _resData_AttentionPerson !== void 0 ? _resData_AttentionPerson : \"\",\n                designation: (_resData_AttentionPerson_Desig = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson_Desig) !== null && _resData_AttentionPerson_Desig !== void 0 ? _resData_AttentionPerson_Desig : \"\",\n                contactPerson: (_resData_ContactPerson = resData === null || resData === void 0 ? void 0 : resData.ContactPerson) !== null && _resData_ContactPerson !== void 0 ? _resData_ContactPerson : \"\",\n                currency: (_resData_Currency_ID = resData === null || resData === void 0 ? void 0 : resData.Currency_ID) !== null && _resData_Currency_ID !== void 0 ? _resData_Currency_ID : \"\",\n                subject: (_resData_Subject = resData === null || resData === void 0 ? void 0 : resData.Subject) !== null && _resData_Subject !== void 0 ? _resData_Subject : \"\",\n                quotation: (_resData_Quotation = resData === null || resData === void 0 ? void 0 : resData.Quotation) !== null && _resData_Quotation !== void 0 ? _resData_Quotation : \"\",\n                totalAmount: (_resData_GrdTotalPSTAmt = resData === null || resData === void 0 ? void 0 : resData.GrdTotalPSTAmt) !== null && _resData_GrdTotalPSTAmt !== void 0 ? _resData_GrdTotalPSTAmt : 0,\n                freight: (_resData_Freight = resData === null || resData === void 0 ? void 0 : resData.Freight) !== null && _resData_Freight !== void 0 ? _resData_Freight : 0,\n                validityDays: (_resData_Validity = resData === null || resData === void 0 ? void 0 : resData.Validity) !== null && _resData_Validity !== void 0 ? _resData_Validity : 0,\n                paymentTerms: (_resData_Terms = resData === null || resData === void 0 ? void 0 : resData.Terms) !== null && _resData_Terms !== void 0 ? _resData_Terms : \"\",\n                netAmount: (_resData_NetAmount = resData === null || resData === void 0 ? void 0 : resData.NetAmount) !== null && _resData_NetAmount !== void 0 ? _resData_NetAmount : 0,\n                narration: (_resData_StartingComments = resData === null || resData === void 0 ? void 0 : resData.StartingComments) !== null && _resData_StartingComments !== void 0 ? _resData_StartingComments : \"\"\n            };\n            setTableData(()=>transformData([], items, true));\n            setFormData(form);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching goto voucher:\", error);\n            setLoading(false);\n            toast({\n                title: \"goto Voucher Fetching Failed !\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const loadInitialData = async ()=>{\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const { data: purposeData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"purpose/\" + purpose_no);\n        if (purposeData.RefVoucherNo) {\n            navigateVoucherForm(purposeData.RefVoucherNo);\n        } else {\n            const newDate = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(dayjs__WEBPACK_IMPORTED_MODULE_13___default()(), true);\n            setFormData((prev)=>({\n                    ...prev,\n                    date: newDate\n                }));\n            getVoucherNo(newDate);\n        }\n        const { data: clientData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"client/\" + (purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID));\n        const items = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"getRecords/items\");\n        setFormData((prev)=>({\n                ...prev,\n                assignerId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessorID,\n                assessmentTime: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessmentTime,\n                assignerLocation: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                address: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                email: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientEmail,\n                phoneNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientMobileNo,\n                landlineNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTelephone,\n                clientId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID,\n                clientTitle: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTitle,\n                contact: purposeData === null || purposeData === void 0 ? void 0 : purposeData.contactPerson,\n                comments: purposeData === null || purposeData === void 0 ? void 0 : purposeData.remarks,\n                ...clientData\n            }));\n        const itemData = [];\n        items.map((item)=>{\n            itemData.push({\n                Item_ID: item.id,\n                Item_Title: item.Title,\n                // Item_Unit: item.Unit,\n                Item_Details: item.Details,\n                Item_Sale_Rate: item.Sale_Rate\n            });\n        });\n        setItems(itemData);\n    };\n    // Toolbar funtions starts here\n    const handleSave = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const data = {\n            Dated: formData.date ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date) : null,\n            VTP: formData.vtp,\n            Mnth: formData.mnth,\n            Location: formData.location,\n            vno: formData.vno,\n            Voucher_No: formData.voucherNo,\n            Currency_ID: formData.currency,\n            Validity: formData.validityDays,\n            Lead: formData.lead,\n            EmployeeID: formData.assignerId,\n            AssessmentTime: formData.assessmentTime ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.assessmentTime) : null,\n            AssessmentLocation: formData.assignerLocation,\n            TenderNo: formData.tenderNo,\n            SalesTaxA: formData.salesTaxA,\n            SalesTaxR: formData.salesTaxR,\n            DiscountPercent: formData.discountPercent,\n            Discount: formData.discountAmount,\n            GrossAmount: formData.netPayableAmt,\n            ContactPerson: formData.contactPerson,\n            Subject: formData.subject,\n            Quotation: formData.quotation,\n            GrdTotalPSTAmt: formData.totalAmount,\n            prp_id: user.id,\n            Freight: formData.freight,\n            NetAmount: formData.netAmount,\n            Terms: formData.paymentTerms,\n            StartingComments: formData.narration,\n            client_id: formData.clientId,\n            CreationDate: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date()),\n            items: transformData({\n                Dated: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date),\n                VTP: formData.vtp,\n                Mnth: formData.mnth,\n                Location: formData.location,\n                vno: formData.vno\n            }, tableData)\n        };\n        const isValidateObjectFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateObjectFields)(data, QuotationFollowupRequiredFields);\n        const isValidateArrayFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateArrayFields)(data.items, QuotationFollowupItemsRequiredFields);\n        if (isValidateObjectFields.error) {\n            toast({\n                title: isValidateObjectFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateArrayFields.error) {\n            toast({\n                title: isValidateArrayFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {\n            setLoading(true);\n            try {\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"post\",\n                    url: \"offer/create\",\n                    data: data\n                });\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"put\",\n                    url: \"purpose/update/\" + purpose_no,\n                    data: {\n                        RefVoucherNo: formData.voucherNo,\n                        RefVTP: formData.vtp,\n                        RefMnth: formData.mnth,\n                        RefLocation: formData.location,\n                        RefVNo: formData.vno\n                    }\n                });\n                handleStoreImages();\n                handleStoreAudios();\n                handleStoreSignature();\n                setIsDisabled(true);\n                toast({\n                    title: \"Quotation Followup Form saved successfully :)\",\n                    status: \"success\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error saving Quotation Followup Form:\", error);\n                toast({\n                    title: \"Error saving Quotation Followup Form :(\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please fill out all required fields and ensure all items have valid fields\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleStoreImages = async ()=>{\n        if (!images || images.length === 0) {\n            toast({\n                title: \"No files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = images.map(async (image)=>{\n                const formData = new FormData();\n                formData.append(\"file\", image.file); // Attach the file\n                formData.append(\"timestamp\", image.timestamp);\n                formData.append(\"location\", JSON.stringify(image.location));\n                formData.append(\"googleMapsLink\", image.googleMapsLink);\n                formData.append(\"type\", \"image\"); // Adjust type if needed (e.g., 'video', 'audio')\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Image \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Image \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading files:\", error);\n            toast({\n                title: \"Error uploading files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreAudios = async ()=>{\n        if (!audios || audios.length === 0) {\n            toast({\n                title: \"No audio files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = audios.map(async (audio)=>{\n                const formData = new FormData();\n                formData.append(\"file\", audio.file); // Attach the file\n                formData.append(\"timestamp\", audio.timestamp);\n                formData.append(\"location\", JSON.stringify(audio.location));\n                formData.append(\"googleMapsLink\", audio.googleMapsLink);\n                formData.append(\"type\", \"audio\"); // Specify type as 'audio'\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Audio \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Audio \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading audio files:\", error);\n            toast({\n                title: \"Error uploading audio files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreSignature = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"No signature to upload\",\n                status: \"info\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            // Convert data URL to a Blob\n            const fetchResponse = await fetch(signature);\n            const signatureBlob = await fetchResponse.blob();\n            // Create a File object\n            const signatureFile = new File([\n                signatureBlob\n            ], \"signature.png\", {\n                type: \"image/png\"\n            });\n            const signatureFormData = new FormData();\n            signatureFormData.append(\"file\", signatureFile);\n            signatureFormData.append(\"timestamp\", new Date().toISOString());\n            signatureFormData.append(\"location\", JSON.stringify({}));\n            signatureFormData.append(\"googleMapsLink\", \"\");\n            signatureFormData.append(\"refVoucherNo\", refVoucherNo);\n            signatureFormData.append(\"refVTP\", refVTP);\n            signatureFormData.append(\"refMnth\", refMnth);\n            signatureFormData.append(\"refLocation\", refLocation);\n            signatureFormData.append(\"refVNo\", refVNo);\n            await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-signature\", signatureFormData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading signature:\", error);\n            toast({\n                title: \"Error uploading signature.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Toolbar funtions ends here\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 1172,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"20px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Site Assessment Follow-Up\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1181,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1179,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        style: {\n                                            gap: \"10px\",\n                                            paddingTop: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            margin: \"0\",\n                                                            textAlign: \"center\",\n                                                            color: \"#2B6CB0\",\n                                                            fontSize: \"20px\",\n                                                            fontWeight: \"bold\"\n                                                        },\n                                                        children: \"SOP's\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"1. Is the property more than 2 years old ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1221,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1224,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"2. Is the combined yearly income of the household equal to or less than 180,000 ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"3. Are you ready to decommission the ducted gas heated system ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        flexWrap: {\n                                                            base: \"wrap\",\n                                                            sm: \"wrap\",\n                                                            md: \"wrap\",\n                                                            lg: \"nowrap\"\n                                                        },\n                                                        gap: \"10px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"80%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                className: \"\",\n                                                                sx: {\n                                                                    display: \"grid\",\n                                                                    gridTemplateColumns: {\n                                                                        base: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        sm: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        md: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                                    },\n                                                                    gap: \"5px\"\n                                                                },\n                                                                children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[0].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            flexWrap: {\n                                                                                base: \"wrap\",\n                                                                                sm: \"wrap\",\n                                                                                md: \"wrap\",\n                                                                                lg: \"nowrap\"\n                                                                            },\n                                                                            flexDirection: {\n                                                                                base: \"column\",\n                                                                                sm: \"column\",\n                                                                                md: \"row\"\n                                                                            },\n                                                                            marginTop: \"10px\"\n                                                                        },\n                                                                        isRequired: field.isRequired,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                                htmlFor: field.id,\n                                                                                sx: {\n                                                                                    marginBottom: \"0\",\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"27%\"\n                                                                                    }\n                                                                                },\n                                                                                children: field.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1315,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            field.type === \"date\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                // min={field.minDate}\n                                                                                // max={field.maxDate}\n                                                                                disabled: isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1330,\n                                                                                columnNumber: 37\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                disabled: field.name === \"voucherNo\" ? isDisabled : isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1357,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, field.id, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1295,\n                                                                        columnNumber: 33\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1270,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"20%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                sx: {\n                                                                    height: \"100%\"\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                                    sx: {\n                                                                        width: \"100%\",\n                                                                        height: {\n                                                                            base: \"inherit\",\n                                                                            md: \"inherit\",\n                                                                            lg: \"calc(100% - 10px)\"\n                                                                        },\n                                                                        margin: \"10px 0\"\n                                                                    },\n                                                                    bg: \"#2d6651\",\n                                                                    color: \"white\",\n                                                                    _hover: {\n                                                                        bg: \"#3a866a\"\n                                                                    },\n                                                                    onClick: ()=>getVoucherNo(formData.date),\n                                                                    isDisabled: isDisabled,\n                                                                    children: \"Generate Voucher No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1402,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1401,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1391,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1258,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    className: \"pt-4 pb-4\",\n                                                    sx: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: {\n                                                            base: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            sm: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            md: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                        },\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[1].map((control, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                },\n                                                                marginTop: \"10px\",\n                                                                flexWrap: \"nowrap\"\n                                                            },\n                                                            isRequired: control.isRequired,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                    htmlFor: control.fields[0].name,\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"20%\"\n                                                                        }\n                                                                    },\n                                                                    children: control.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1463,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"80%\"\n                                                                        },\n                                                                        display: \"flex\",\n                                                                        gap: control.fields.length > 1 ? \"10px\" : \"0\"\n                                                                    },\n                                                                    children: control.fields.map((field, fieldIndex)=>field.component === \"ComboBox\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            target: true,\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            inputWidths: field.inputWidths,\n                                                                            buttonWidth: field.buttonWidth,\n                                                                            styleButton: {\n                                                                                padding: \"3px !important\"\n                                                                            },\n                                                                            tableData: [],\n                                                                            tableHeaders: field.tableHeaders,\n                                                                            nameFields: field.nameFields,\n                                                                            placeholders: field.placeholders,\n                                                                            keys: field.keys,\n                                                                            form: formData,\n                                                                            isDisabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1488,\n                                                                            columnNumber: 37\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            placeholder: field.placeholder,\n                                                                            value: formData[field.value],\n                                                                            _placeholder: field._placeholder,\n                                                                            type: field.type,\n                                                                            style: {\n                                                                                width: field.inputWidth\n                                                                            },\n                                                                            disabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1507,\n                                                                            columnNumber: 37\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1475,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1448,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1433,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    tableData: tableData,\n                                                    setTableData: setTableData,\n                                                    headers: QuotationFollowupHeaders,\n                                                    tableWidth: \"100%\",\n                                                    tableHeight: \"400px\",\n                                                    fontSize: \"lg\",\n                                                    cellRender: cellRender,\n                                                    styleHead: {\n                                                        background: \"#3275bb\",\n                                                        color: \"white !important\"\n                                                    },\n                                                    styleBody: {\n                                                        background: \"white !important\"\n                                                    },\n                                                    calculation: calculation,\n                                                    isDisabled: isDisabled\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1537,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1527,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            initial: loadedImages,\n                                                            onChange: handleImagesChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1587,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1580,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1565,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1554,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            initial: loadedAudios,\n                                                            onChange: handleAudiosChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1628,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1621,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1606,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1595,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 pb-2\",\n                                                    style: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: \"repeat(auto-fit,minmax(300px,1fr))\",\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Total Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"totalAmount\",\n                                                                        value: formData.totalAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1681,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1658,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1657,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1712,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxR\",\n                                                                                value: formData.salesTaxR,\n                                                                                placeholder: \"R\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1734,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxA\",\n                                                                                value: formData.salesTaxA,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1750,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1723,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1700,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1699,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax Amt.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1782,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"sTaxAmount\",\n                                                                        value: formData.sTaxAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1793,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1770,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1769,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1824,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netAmount\",\n                                                                        value: formData.netAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1835,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1812,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1811,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Discount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1866,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountPercent\",\n                                                                                value: formData.discountPercent,\n                                                                                placeholder: \"%\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1888,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountAmount\",\n                                                                                value: formData.discountAmount,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: true,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1904,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1877,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1854,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1853,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Freight\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1936,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"freight\",\n                                                                        value: formData.freight,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1947,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1924,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1923,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Payable Amt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1978,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netPayableAmt\",\n                                                                        value: formData.netPayableAmt,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1989,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1966,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1965,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Validity Days\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2020,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"validityDays\",\n                                                                        value: formData.validityDays,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2031,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 2008,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2007,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            gridColumn: \"1 / -1\",\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"10%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Payment Terms\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2065,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"paymentTerms\",\n                                                                        value: formData.paymentTerms,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        type: \"text\",\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"90%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2076,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 2053,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1648,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(75% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Remarks\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2118,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                                                            _placeholder: {\n                                                                color: \"gray.500\"\n                                                            },\n                                                            resize: \"vertical\",\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                height: \"100%\"\n                                                            },\n                                                            onChange: handleInputChange,\n                                                            name: \"narration\",\n                                                            value: formData.narration,\n                                                            disabled: isDisabled,\n                                                            rows: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2125,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2108,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2097,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(25% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Digital Signature\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2161,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                display: \"flex\",\n                                                                flexDirection: \"column\",\n                                                                alignItems: \"center\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                onSave: (url)=>setSignature(url),\n                                                                onClose: ()=>setSignature(null),\n                                                                initialSignature: signature,\n                                                                isDisabled: isDisabled\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 2176,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2168,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2151,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2140,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                lineNumber: 1178,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                            lineNumber: 1177,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 1176,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 1175,\n                    columnNumber: 11\n                }, undefined),\n                !isDisabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    save: handlePrint,\n                    hide: [\n                        \"First\",\n                        \"Edit\",\n                        \"Last\",\n                        \"Previous\",\n                        \"Next\",\n                        \"Delete\",\n                        \"Goto\",\n                        \"Cancel\",\n                        \"Clear\",\n                        \"Check\",\n                        \"Print\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2191,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PrintModal__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isOpen: isPrintModalOpen,\n                    onClose: ()=>setPrintModalOpen(false),\n                    formName: \"Site Assessment Quotation\",\n                    showHeader: false,\n                    buttonText: \"Save & Generate PDF\",\n                    callback: handleGeneratePDFFromComponent,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        data: printData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 2216,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2208,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(AssesserFollowUp, \"Y00UvtCWtYp7LkfYjThmtmHCCrE=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast,\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = AssesserFollowUp;\nconst AssesserFollowUpPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2225,\n            columnNumber: 23\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AssesserFollowUp, {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2226,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n        lineNumber: 2225,\n        columnNumber: 3\n    }, undefined);\n_c1 = AssesserFollowUpPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AssesserFollowUpPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"AssesserFollowUp\");\n$RefreshReg$(_c1, \"AssesserFollowUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx\n"));

/***/ })

});