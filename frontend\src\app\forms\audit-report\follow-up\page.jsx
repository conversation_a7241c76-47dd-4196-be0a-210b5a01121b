"use client";
import React, { Suspense, useEffect, useState } from "react";
import axiosInstance from "../../../axios";
import { Box, useToast, Flex, Text, Table, Thead, Tbody, Tr, Th, Td, Grid, IconButton, Link } from "@chakra-ui/react";
import { keyframes } from "@emotion/react";
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Textarea,
  useDisclosure,
} from "@chakra-ui/react";
import { CheckIcon, CloseIcon } from '@chakra-ui/icons';
import {
  formatDate,
  getData,
} from "@utils/functions";
import { useSearchParams } from "next/navigation";
import Loader from "@components/Loader/Loader";
import { useUser } from "@src/app/provider/UserContext";
import dayjs from "dayjs";
import MultipleImageUploader from "@components/Custom/MultipleImageUploader/MultipleImageUploader";
import MultipleAudioUploader from "@components/Custom/MultipleAudioUploader/MultipleAudioUploader";
import Image from "next/image";

// Define animations
const fadeInKeyframes = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const slideInKeyframes = keyframes`
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

const scaleInKeyframes = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const fadeInAnimation = `${fadeInKeyframes} 0.5s ease-out`;
const slideInAnimation = `${slideInKeyframes} 0.5s ease-out`;
const scaleInAnimation = `${scaleInKeyframes} 0.5s ease-out`;

const zoomInKeyframes = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const RecoveryFollowUp = () => {
  const toast = useToast();
  const { user } = useUser();
  const searchParams = useSearchParams();
  const [isDisabled, setIsDisabled] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [loading, setLoading] = useState(false);
  const [printData, setPrintData] = useState({});
  const [loadedImages, setLoadedImages] = useState([]);
  const [loadedAudios, setLoadedAudios] = useState([]);
  const [signature, setSignature] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rejectionCause, setRejectionCause] = useState("");
  const [isLoaded, setIsLoaded] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const { isOpen: isImageOpen, onOpen: onImageOpen, onClose: onImageClose } = useDisclosure();

  useEffect(() => {
    // Trigger animations after component mounts
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          Item_ID: item?.item_id,
          Item_Title: item?.itemTitle,
          Rate: Number(item?.Rate),
          InstallationCharges: Number(item?.InstallationCharges),
          Disc: Number(item?.Discount),
          Total: Number(item?.Total),
          Date: formatDate(new Date(item?.VisitDate), true),
          Details: item?.details,
          Qty: Number(item?.Qty),
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          item_id: item.Item_ID,
          Rate: Number(item.Rate),
          InstallationCharges: Number(item.InstallationCharges),
          Discount: Number(item.Disc),
          Total: Number(item.Total),
          VisitDate: item.Date,
          details: item.Details,
          Qty: Number(item.Qty),
        };
      });
    }
  };

  const navigateVoucherForm = async (voucherNo) => {
    setLoading(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("offer/navigate", {
        goto: true,
        voucher_no: voucherNo,
      });
      const fileResponse = await axiosInstance.get("offer/get-files", {
        params: {
          refVoucherNo: voucherNo,
        },
      });
      const allFiles = fileResponse.data;
      const images = allFiles.filter(file => file.Type === 'image' && file.FileName !== 'signature.png');
      const audios = allFiles.filter(file => file.Type === 'audio');
      
      // Find signature if it exists
      const signatureFile = allFiles.find(file => file.FileName === 'signature.png');
      if (signatureFile) {
        // Convert buffer to base64 string for signature pad
        const buffer = Buffer.from(signatureFile.FileData.data);
        const base64String = `data:image/png;base64,${buffer.toString('base64')}`;
        setSignature(base64String);
      }
      setLoadedAudios(audios);
      setLoadedImages(images);
      const resData = response.data;
      const { items } = response.data;
      const form = {
        date: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.VTP,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        clientId: resData?.client_id ?? "",
        clientTitle: resData?.ClientName ?? "",
        assignerId: resData?.EmployeeID ?? "",
        assignerTitle: resData?.EmployeeName ?? "",
        assessmentTime: resData?.AssessmentTime
          ? dayjs(resData?.AssessmentTime).format("YYYY-MM-DDTHH:mm")
          : "",
        assignerLocation: resData?.AssessmentLocation ?? "",
        salesTaxA: resData?.SalesTaxA ?? 0,
        salesTaxR: resData?.SalesTaxR ?? 0,
        discountAmount: resData?.Discount ?? 0,
        discountPercent: resData?.DiscountPercent ?? 0,
        netPayableAmt: resData?.GrossAmount ?? 0,
        lead: resData?.Lead ?? "",
        tenderNo: resData?.TenderNo ?? "",
        // exchangeRate: resData?.ExchRate ?? 0,
        // partyRef: resData?.Party_ref ?? "",
        attentionPerson: resData?.AttentionPerson ?? "",
        designation: resData?.AttentionPerson_Desig ?? "",
        contactPerson: resData?.ContactPerson ?? "",
        currency: resData?.Currency_ID ?? "",
        subject: resData?.Subject ?? "",
        quotation: resData?.Quotation ?? "",
        totalAmount: resData?.GrdTotalPSTAmt ?? 0,
        freight: resData?.Freight ?? 0,
        validityDays: resData?.Validity ?? 0,
        paymentTerms: resData?.Terms ?? "",
        netAmount: resData?.NetAmount ?? 0,
        narration: resData?.StartingComments ?? "",
      };
      setLoading(false);
      setPrintData((prev) => ({
        ...form,
        ...prev,
        items: transformData([], items, true),
      }));
    } catch (error) {
      console.error(`Error fetching goto voucher:`, error);
      setLoading(false);
      toast({
        title: `goto Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const purpose_no = searchParams.get("purpose_no");
    const { data: purposeData } = await getData("purpose/" + purpose_no);
    const { data: clientData } = await getData("client/" + purposeData?.clientID);
    setPrintData((prev) => ({
      ...prev,
      assignerId: purposeData?.assessorID,
      assessmentTime: purposeData?.assessmentTime,
      assignerLocation: purposeData?.clientAddress,
      address: purposeData?.clientAddress,
      email: purposeData?.clientEmail,
      phoneNumber: purposeData?.clientMobileNo,
      landlineNo: purposeData?.clientTelephone,
      clientId: purposeData?.clientID,
      clientTitle: purposeData?.clientTitle,
      contact: purposeData?.contactPerson,
      comments: purposeData?.remarks,
      ...clientData
    }));

    navigateVoucherForm(purposeData.RefVoucherNo);
    setIsApproved(purposeData.IsApproved == null ? false : true);
  };

  const handleApproval = (value) => {
    if (value === 0) {
      setIsModalOpen(true);
      return;
    }

    submitApproval(value);
  };

  const submitApproval = (value, cause = "") => {
    const purpose_no = searchParams.get("purpose_no");
    setLoading(true);
    axiosInstance({
      method: "put",
      url: `purpose/approval/${purpose_no}`,
      data: {
        isApproved: value,
        rejectionCause: cause
      },
    })
      .then(() => {
        if (value === 1) {
          toast({
            title: "Approved successfully :)",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        } else {
          toast({
            title: "Rejected successfully :)",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
        setIsApproved(true);
        setLoading(false);
        setIsModalOpen(false);
        setRejectionCause("");
      })
      .catch((error) => {
        console.error("Error while checking Followup Form:", error);
        toast({
          title: "Error while checking Followup Form :(",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        setLoading(false);
      });
  };

  const handleImageClick = (image) => {
    setSelectedImage(image);
    onImageOpen();
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <Box 
            p={8} 
            maxWidth="1200px" 
            margin="10px auto" 
            bg="white" 
            borderRadius="xl" 
            boxShadow="lg"
            sx={{
              animation: fadeInAnimation
            }}
          >
            <Flex 
              justify="space-between" 
              align="center" 
              mb={8} 
              pb={4} 
              borderBottom="2px solid" 
              borderColor="gray.200"
              sx={{
                animation: slideInAnimation
              }}
            >
              <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                <Text 
                  fontSize="3xl" 
                  fontWeight="bold" 
                  bgGradient="linear(to-r, #2d6651, #2d6651)" 
                  bgClip="text"
                  sx={{
                    transition: "0.3s",
                    _hover: {
                      bgGradient: "linear(to-r, #2d6651, #2d6651)"
                    }
                  }}
                >
                  QUOTATION
                </Text>
                <Text 
                  color="red.500" 
                  fontWeight="bold" 
                  fontSize="xl"
                  px={4}
                  py={1}
                  bg="red.50"
                  borderRadius="md"
                  sx={{
                    animation: scaleInAnimation,
                    transition: "0.3s",
                    _hover: {
                      transform: "scale(1.05)"
                    }
                  }}
                >
                  {searchParams.get("purpose_no") ?? 'N/A'}
                </Text>
              </Box>
            </Flex>

            <Flex 
              justify="space-between" 
              align="start" 
              mb={8} 
              gap={8}
              flexWrap={{ base: "wrap", md: "nowrap" }}
              sx={{
                '& > *': {
                  animation: fadeInAnimation,
                  animationFillMode: 'both'
                },
                '& > *:nth-of-type(2)': {
                  animationDelay: '0.1s'
                }
              }}
            >
              <Box 
                flex="1" 
                bg="gray.50" 
                p={6} 
                borderRadius="lg" 
                borderLeft="4px solid" 
                borderColor="#2d6651"
                transition="0.3s"
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "md"
                }}
              >
                <Text fontSize="sm" color="gray.500" mb={3} textTransform="uppercase">INVOICE TO</Text>
                <Text fontSize="lg" fontWeight="medium" mb={2}>{printData?.clientTitle ?? 'N/A'}</Text>
                <Text color="gray.600">{printData?.email ?? 'N/A'}</Text>
                <Text color="gray.600">{printData?.phoneNumber ?? 'N/A'}</Text>
                <Text color="gray.600">{printData?.address ?? 'N/A'}</Text>
              </Box>
              <Box 
                flex="1" 
                bg="gray.50" 
                p={6} 
                borderRadius="lg" 
                textAlign="right"
                borderRight="4px solid" 
                borderColor="#2d6651"
                transition="0.3s"
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "md"
                }}
              >
                <Text fontSize="xl" fontWeight="bold" color="#2d6651" mb={1}>Eco Assets Manager PTY LTD</Text>
                <Text fontWeight="medium" mb={3}>ABN **************</Text>
                <Text color="gray.600">3/36 Zakwell Court</Text>
                <Text color="gray.600">Coolaroo 3048 VIC</Text>
                <Text fontSize="lg" fontWeight="bold" color="#2d6651" mt={2}>1300 5333 733</Text>
              </Box>
            </Flex>

            <Box 
              mb={8} 
              borderRadius="lg" 
              overflow="hidden" 
              boxShadow="sm" 
              border="1px solid" 
              borderColor="gray.200"
              sx={{
                animation: fadeInAnimation,
                animationDelay: '0.2s',
                animationFillMode: 'both'
              }}
            >
              <Table variant="simple">
                <Thead>
                  <Tr bg="#2d6651">
                    <Th color="white" fontSize="md">Products</Th>
                    <Th color="white" fontSize="md">Quantity</Th>
                    <Th color="white" fontSize="md">Price</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {printData?.items && printData?.items?.map((item, index) => (
                    <Tr 
                      key={index}
                      sx={{
                        transition: "0.2s",
                        _hover: { bg: "gray.50" }
                      }}
                    >
                      <Td fontWeight="medium">{item?.Item_Title ?? 'N/A'}</Td>
                      <Td fontWeight="medium">{parseFloat(item?.Qty).toFixed(2) ?? 'N/A'}</Td>
                      <Td fontWeight="medium">${parseFloat(item?.Total).toFixed(2) ?? 'N/A'}</Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>

            {/* Images Display Section */}
            <Box
              mb={8}
              p={6}
              borderRadius="lg"
              bg="white"
              boxShadow="sm"
              border="1px solid"
              borderColor="gray.200"
              sx={{
                animation: fadeInAnimation,
                animationDelay: '0.3s',
                animationFillMode: 'both'
              }}
            >
              <Text fontSize="lg" fontWeight="600" mb={4} color="#2d6651">Uploaded Images</Text>
              <Box 
                p={4} 
                bg="rgba(58, 134, 106, 0.05)" 
                borderRadius="md"
              >
                {loadedImages.length > 0 ? (
                  <Box 
                    display="grid" 
                    gridTemplateColumns={{
                      base: "1fr",
                      md: "repeat(2, 1fr)",
                      lg: "repeat(3, 1fr)"
                    }}
                    gap={6}
                  >
                    {loadedImages.map((image, index) => (
                      <Box 
                        key={index} 
                        bg="white" 
                        p={4} 
                        borderRadius="lg" 
                        boxShadow="sm"
                        transition="all 0.2s"
                        _hover={{ 
                          transform: 'translateY(-2px)',
                          boxShadow: 'md'
                        }}
                        cursor="pointer"
                        onClick={() => handleImageClick(image)}
                      >
                        <Box 
                          position="relative" 
                          height="200px" 
                          mb={3}
                          borderRadius="md"
                          overflow="hidden"
                        >
                          <Image
                            src={`data:image/jpeg;base64,${Buffer.from(image.FileData.data).toString('base64')}`}
                            alt={`Image ${index + 1}`}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        </Box>
                        <Box>
                          <Text fontSize="sm" color="gray.600" mb={1}>
                            <strong>Location:</strong>{' '}
                            <Link 
                              href={`https://www.google.com/maps?q=${image.Latitude},${image.Longitude}`} 
                              isExternal 
                              color="blue.500"
                              textDecoration="underline"
                            >
                              View on Maps
                            </Link>
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            <strong>Timestamp:</strong> {new Date(image.Timestamp).toLocaleString()}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Text color="gray.500" textAlign="center" py={8}>No images uploaded</Text>
                )}
              </Box>
            </Box>

            {/* Audio Files Display Section */}
            <Box
              mb={8}
              p={6}
              borderRadius="lg"
              bg="white"
              boxShadow="sm"
              border="1px solid"
              borderColor="gray.200"
              sx={{
                animation: fadeInAnimation,
                animationDelay: '0.4s',
                animationFillMode: 'both'
              }}
            >
              <Text fontSize="lg" fontWeight="600" mb={4} color="#2d6651">Voice Notes</Text>
              <Box 
                p={4} 
                bg="rgba(58, 134, 106, 0.05)" 
                borderRadius="md"
              >
                {loadedAudios.length > 0 ? (
                  <Box 
                    display="grid" 
                    gridTemplateColumns={{
                      base: "1fr",
                      md: "repeat(2, 1fr)"
                    }}
                    gap={6}
                  >
                    {loadedAudios.map((audio, index) => (
                      <Box 
                        key={index} 
                        bg="white" 
                        p={4} 
                        borderRadius="lg" 
                        boxShadow="sm"
                        transition="transform 0.2s"
                        _hover={{ transform: 'translateY(-2px)' }}
                      >
                        <Box mb={3}>
                          <audio 
                            controls 
                            src={`data:audio/mp3;base64,${Buffer.from(audio.FileData.data).toString('base64')}`}
                            style={{ width: '100%' }}
                          />
                        </Box>
                        <Box>
                          <Text fontSize="sm" color="gray.600" mb={1}>
                            <strong>Location:</strong>{' '}
                            <Link 
                              href={`https://www.google.com/maps?q=${audio.Latitude},${audio.Longitude}`} 
                              isExternal 
                              color="blue.500"
                              textDecoration="underline"
                            >
                              View on Maps
                            </Link>
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            <strong>Timestamp:</strong> {new Date(audio.Timestamp).toLocaleString()}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Text color="gray.500" textAlign="center" py={8}>No voice notes uploaded</Text>
                )}
              </Box>
            </Box>

            <Box 
              bg="gray.50" 
              p={6} 
              borderRadius="lg" 
              boxShadow="sm"
              border="1px solid"
              borderColor="gray.200"
              sx={{
                animation: fadeInAnimation,
                animationDelay: '0.2s',
                animationFillMode: 'both'
              }}
            >
              <Grid templateColumns="1fr auto" gap={4}>
                <Text color="gray.600">Subtotal Incl. GST</Text>
                <Text fontWeight="medium">${parseFloat(printData?.netAmount).toFixed(2) ?? '0.00'}</Text>
                
                <Text color="#2d6651" fontWeight="medium">EAM Discount</Text>
                <Text color="#2d6651" fontWeight="medium">-$0.00</Text>
                
                <Text color="gray.600">Tax Included ({(printData?.salesTaxR ?? 0) + (printData?.salesTaxA ?? 0)}%)</Text>
                <Text fontWeight="medium">${parseFloat(printData?.sTaxAmount).toFixed(2) ?? '0'}</Text>
                
                <Text color="#2d6651" fontWeight="medium">VEEC Discount</Text>
                <Text color="#2d6651" fontWeight="medium">${parseFloat(printData?.discountAmount).toFixed(2) ?? '0.00'}</Text>
                
                <Text color="#2d6651" fontWeight="medium">STC Discount</Text>
                <Text color="#2d6651" fontWeight="medium">N/A</Text>
                
                <Text color="#2d6651" fontWeight="medium">SOLARVIC Rebate</Text>
                <Text color="#2d6651" fontWeight="medium">N/A</Text>
                
                <Box 
                  gridColumn="1/-1" 
                  borderTop="2px solid" 
                  borderColor="gray.200" 
                  mt={4} 
                  pt={4}
                >
                  <Flex justify="space-between" align="center">
                    <Box>
                      <Text fontSize="xl" fontWeight="bold" lineHeight="1.2">Total Out of Pocket</Text>
                      <Text fontSize="sm" color="gray.500">Incl. GST</Text>
                    </Box>
                    <Text 
                      fontSize="2xl" 
                      fontWeight="bold" 
                      color="#2d6651"
                      sx={{
                        transition: "0.3s",
                        _hover: {
                          transform: "scale(1.05)",
                        }
                      }}
                    >
                      ${parseFloat(printData?.netPayableAmt).toFixed(2) ?? '0.00'}
                    </Text>
                  </Flex>
                </Box>
              </Grid>
              
              <Flex justify="center" mt={12}>
                <Flex 
                  flexDirection="column" 
                  alignItems="center" 
                  maxWidth="300px"
                  sx={{
                    animation: fadeInAnimation,
                    animationDelay: '0.6s',
                    animationFillMode: 'both'
                  }}
                >
                  <Box 
                    border="1px solid" 
                    borderColor="gray.200" 
                    p={4} 
                    borderRadius="md" 
                    bg="white"
                    mb={2}
                    transition="0.3s"
                    _hover={{
                      transform: "scale(1.02)",
                      boxShadow: "md"
                    }}
                  >
                    <Image src={signature} alt="Signature" width={150} height={100} style={{ objectFit: 'contain' }}/>
                  </Box>
                  <Text borderTop="2px solid" borderColor="gray.300" pt={2} width="200px" textAlign="center"/>
                  <Text fontWeight="bold" color="gray.700" mt={2}>Customer Signature</Text>
                </Flex>
              </Flex>
            </Box>
          </Box>

          {/* Approval Buttons */}
          {!isApproved && (
            <Flex 
              justify="center" 
              gap={4} 
              mt={8}
              mb={8}
              sx={{
                animation: fadeInAnimation,
                animationDelay: '0.7s',
                animationFillMode: 'both'
              }}
            >
              <Button
                leftIcon={<CheckIcon />}
                colorScheme="green"
                size="lg"
                onClick={() => handleApproval(1)}
                _hover={{
                  transform: 'translateY(-2px)',
                  boxShadow: 'lg'
                }}
                transition="all 0.2s"
              >
                Approve Quotation
              </Button>
              <Button
                leftIcon={<CloseIcon />}
                colorScheme="red"
                variant="outline"
                size="lg"
                onClick={() => handleApproval(0)}
                _hover={{
                  transform: 'translateY(-2px)',
                  boxShadow: 'lg',
                  bg: 'red.50'
                }}
                transition="all 0.2s"
              >
                Reject Quotation
              </Button>
            </Flex>
          )}

          {/* Image Viewer Modal */}
          <Modal 
            isOpen={isImageOpen} 
            onClose={onImageClose} 
            size="6xl"
            motionPreset="scale"
          >
            <ModalOverlay 
              bg="blackAlpha.800"
              backdropFilter="blur(10px)"
            />
            <ModalContent
              bg="transparent"
              boxShadow="none"
              mx={4}
              my="auto"
              maxW="90vw"
              maxH="90vh"
              sx={{
                animation: `${zoomInKeyframes} 0.3s ease-out`
              }}
            >
              <ModalCloseButton 
                color="white" 
                size="lg"
                top={-10}
                right={-10}
                _hover={{
                  bg: 'whiteAlpha.200'
                }}
              />
              <ModalBody p={0}>
                <Box
                  position="relative"
                  width="100%"
                  height="100%"
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                >
                  {selectedImage && (
                    <Image
                      src={`data:image/jpeg;base64,${Buffer.from(selectedImage.FileData.data).toString('base64')}`}
                      alt="Selected Image"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '85vh',
                        objectFit: 'contain',
                        borderRadius: '8px'
                      }}
                    />
                  )}
                </Box>
              </ModalBody>
            </ModalContent>
          </Modal>
        </>
      )}

      {/* Rejection Modal */}
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Rejection Reason</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Textarea
              value={rejectionCause}
              onChange={(e) => setRejectionCause(e.target.value)}
              placeholder="Please enter the reason for rejection"
              size="lg"
              minH="150px"
              _focus={{
                borderColor: "#2d6651",
                boxShadow: "0 0 0 1px #2d6651"
              }}
            />
          </ModalBody>
          <ModalFooter>
            <Button 
              colorScheme="red" 
              mr={3} 
              onClick={() => submitApproval(0, rejectionCause)}
              isDisabled={!rejectionCause.trim()}
              leftIcon={<CloseIcon />}
            >
              Submit Rejection
            </Button>
            <Button variant="ghost" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

const RecoveryFollowUpPage = () => (
  <Suspense fallback={<Loader />}>
    <RecoveryFollowUp />
  </Suspense>
);

export default RecoveryFollowUpPage;
