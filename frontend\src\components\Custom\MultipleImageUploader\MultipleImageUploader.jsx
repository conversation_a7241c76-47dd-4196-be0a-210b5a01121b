import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Image as ChakraImage, Text, useToast, Link } from '@chakra-ui/react';
import { HiUpload } from "react-icons/hi";
import { BiTrash } from "react-icons/bi";
import { EditIcon } from "@chakra-ui/icons";

const MultipleImageUploader = ({ initial = [], onChange, disabled = false }) => {
    const [images, setImages] = useState([]);
    const [newImages, setNewImages] = useState([]);
    const fileInputRef = useRef(null);
    const toast = useToast();

    useEffect(() => {
        if (initial.length > 0) {
            const formattedImages = initial.map(image => ({
                file: { name: image.FileName },
                preview: `data:image/jpeg;base64,${Buffer.from(image.FileData.data).toString('base64')}`,
                timestamp: new Date(image.Timestamp).toLocaleString(),
                location: { lat: image.Latitude, lng: image.Longitude },
                googleMapsLink: image.GoogleMapsLink,
                role: image.Role,
            }));
            setImages(formattedImages);
        }
    }, [initial]);

    useEffect(() => {
        if (newImages.length > 0) {
            setImages(prev => {
                const updatedImages = [...prev, ...newImages];
                onChange?.(updatedImages);
                return updatedImages;
            });
            setNewImages([]);
        }
    }, [newImages, onChange]);

    const handleImageChange = (e) => {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            const fileExists = images.some(image => image.file.name === file.name);
            if (fileExists) {
                toast({
                    title: "Image already uploaded",
                    status: "warning",
                    variant: "left-accent",
                    position: "top-right",
                    isClosable: true,
                });
                return;
            }

            const reader = new FileReader();
            const timestamp = new Date().toLocaleString();
            reader.onloadend = () => {
                navigator.geolocation.getCurrentPosition((position) => {
                    const location = `Lat: ${position.coords.latitude}, Lon: ${position.coords.longitude}`;
                    const googleMapsLink = `https://www.google.com/maps?q=${position.coords.latitude},${position.coords.longitude}`;
                    setNewImages(prev => [...prev, { file, preview: reader.result, timestamp, location: { lat: position.coords.latitude, lng: position.coords.longitude }, googleMapsLink }]);
                }, (error) => {
                    console.error("Error getting location:", error);
                    toast({
                        title: "Location permission denied. Image not uploaded.",
                        status: "error",
                        variant: "left-accent",
                        position: "top-right",
                        isClosable: true,
                    });
                });
            };
            reader.readAsDataURL(file);
        });
    };

    const handleRemove = (index) => {
        const updatedImages = images.filter((_, i) => i !== index);
        setImages(updatedImages);
        onChange?.(updatedImages);
    };

    const handleUploadClick = () => {
        fileInputRef.current?.click();
    };

    return (
        <Box width="100%" height="100%">
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageChange}
                accept="image/*"
                style={{ display: 'none' }}
                disabled={disabled}
                multiple
            />

            <Button
                width="100%"
                bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                onClick={handleUploadClick}
                disabled={disabled}
            >
                <HiUpload size={20} style={{ marginRight: "5px" }} />
                Upload Images
            </Button>

            <Box mt={4} sx={{
                display: "grid",
                gridTemplateColumns: {
                    base: "repeat(auto-fit,minmax(200,1fr))",
                    sm: "repeat(auto-fit,minmax(200,1fr))",
                    md: "repeat(auto-fit,minmax(300,1fr))",
                    lg: "repeat(auto-fit,minmax(500px,1fr))",
                },
            }}>
                {images.map((image, index) => (
                    <Box key={index} display="flex" alignItems="center" mb={2} width={'100%'} justifyContent={'space-between'} paddingRight={3} flexDirection={{ base: "column", md: "row" }}>
                        <ChakraImage
                            src={image.preview}
                            alt={`Preview ${index}`}
                            width={{ base: "100%", md: "200px" }}
                            height={{ base: "auto", md: "150px" }}
                            borderRadius="10%"
                            objectFit="fill"
                            mr={2}
                        />
                        <Box>
                            <Text><strong>Location: &nbsp;</strong>
                                {image.googleMapsLink && (
                                    <Link href={image.googleMapsLink} isExternal color="blue.500">
                                        View on Google Maps
                                    </Link>
                                )}
                            </Text>
                            <Text><strong>Timestamp:</strong> {image.timestamp}</Text>
                            { image.role && <Text><strong>Role:</strong> {image.role}</Text>}
                        </Box>
                        <Button
                            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                            onClick={() => handleRemove(index)}
                            disabled={disabled}
                            size="sm"
                            ml={2}
                        >
                            <BiTrash />
                        </Button>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};

export default MultipleImageUploader;
