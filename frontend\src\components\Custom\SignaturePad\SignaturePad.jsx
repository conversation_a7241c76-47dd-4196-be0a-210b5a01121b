import React, { useRef, useState, useEffect } from "react";
import SignatureCanvas from "react-signature-canvas";
import { Box, Button } from "@chakra-ui/react";

const SignaturePad = ({ onSave, onClose, initialSignature, isDisabled = false }) => {
    const sigCanvas = useRef({});
    const [signature, setSignature] = useState(initialSignature || null);

    useEffect(() => {
        if (initialSignature && sigCanvas.current) {
            // If we have an initial signature, load it into the canvas
            const ctx = sigCanvas.current.getCanvas().getContext('2d');
            
            // Clear the canvas first
            sigCanvas.current.clear();
            
            // Create an image element
            const img = new Image();
            img.onload = () => {
                // Draw the image on the canvas
                ctx.drawImage(img, 0, 0, sigCanvas.current.getCanvas().width, sigCanvas.current.getCanvas().height);
                setSignature(initialSignature);
            };
            
            // Set the source of the image
            img.src = initialSignature;
        }
    }, [initialSignature]);

    const clear = () => {
        sigCanvas.current.clear();
        onClose();
        setSignature(null);
    };

    const save = () => {
        const dataUrl = sigCanvas.current.getCanvas().toDataURL("image/png");
        onSave(dataUrl);
        setSignature(dataUrl);
    };

    return (
        <Box>
            <Box sx={{ border: '1px solid #2d6651', borderRadius: '8px' }}>
                <SignatureCanvas
                    ref={sigCanvas}
                    penColor="black"
                    canvasProps={{ width: 200, height: 200, className: "sigCanvas" }}
                />
            </Box>
            {!isDisabled && (
                <>
                    <Button onClick={clear} colorScheme="red" mt={2} disabled={!signature}>
                        Clear
                    </Button>
                    <Button onClick={save} bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} mt={2} ml={2} disabled={signature}>
                        Save
                    </Button>
                </>
            )}
        </Box>
    );
};

export default SignaturePad;
