const { body, validationResult } = require('express-validator');

const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

const employeeValidationRules = {
    create: [
        body('employeeId').notEmpty().isString().withMessage('Employee ID is required'),
        body('username').notEmpty().isString().isLength({ max: 8 }).withMessage('Username is required and cannot exceed 8 characters'),
        body('firstName').notEmpty().isString().withMessage('First name is required'),
        body('lastName').notEmpty().isString().withMessage('Last name is required'),
        body('password').notEmpty().isString().withMessage('Password is required'),
        body('gender').notEmpty().isInt({ min: 0, max: 2 }).withMessage('Gender must be 0, 1, or 2'),
        body('email').notEmpty().isEmail().withMessage('Valid email is required'),
        body('abn').optional().isString().withMessage('ABN must be a string'),
        body('licenseNo').notEmpty().isString().withMessage('License is required'),
        body('phoneNo').notEmpty().isString().withMessage('Phone number is required'),
        body('roleId').notEmpty().isInt().withMessage('Role ID must be an integer'),
        body('location').notEmpty().isString().withMessage('Location is required'),
        validate
    ],
    update: [
        body('firstName').optional().isString().withMessage('First name must be a string'),
        body('lastName').optional().isString().withMessage('Last name must be a string'),
        body('email').optional().isEmail().withMessage('Valid email is required'),
        body('phoneNo').optional().isString().withMessage('Phone number must be a string'),
        body('location').optional().isString().withMessage('Location must be a string'),
        body('roleId').optional().isInt().withMessage('Role ID must be an integer'),
        body('abn').optional().isString().withMessage('ABN must be a string'),
        body('licenseNo').optional().isString().withMessage('License must be a string'),
        validate
    ],
    bulkUpdate: [
        body('employees').isArray({ min: 1 }).withMessage('Employees array is required and must not be empty'),
        body('employees.*.id').notEmpty().isString().withMessage('Employee ID is required for each employee'),
        body('employees.*.firstName').optional().isString().withMessage('First name must be a string'),
        body('employees.*.lastName').optional().isString().withMessage('Last name must be a string'),
        body('employees.*.email').optional().isEmail().withMessage('Valid email is required'),
        body('employees.*.phoneNo').optional().isString().withMessage('Phone number must be a string'),
        body('employees.*.location').optional().isString().withMessage('Location must be a string'),
        body('employees.*.roleId').optional().isInt().withMessage('Role ID must be an integer'),
        body('employees.*.abn').optional().isString().withMessage('ABN must be a string'),
        body('employees.*.licenseNo').optional().isString().withMessage('License must be a string'),
        validate
    ]
};

module.exports = employeeValidationRules;
