"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import { useUser } from '@src/app/provider/UserContext';
import { capitalizeWords } from '@src/app/utils/functions';
import { Box, IconButton, Tooltip } from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import ReportTable from '@src/components/Custom/ReportTable';
import TimeUpdateModal from '@src/components/Custom/TimeUpdateModal/TimeUpdateModal';
import { FiClock } from 'react-icons/fi';

const InstallerDashboard = () => {
    const router = useRouter();
    const { user, userRole } = useUser();
    const [tasks, setTasks] = useState([]);
    const [isTimeUpdateModalOpen, setIsTimeUpdateModalOpen] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("client/tasks/installer");
            if (Array.isArray(data.data) && data.data.length > 0) {
                setTasks(data.data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'in-progress': return 'blue';
            case 'partial-completed': return 'orange';
            case 'completed': return 'green';
            default: return 'gray';
        }
    };

    const handleTimeUpdate = (task) => {
        setSelectedTask(task);
        setIsTimeUpdateModalOpen(true);
    };

    const handleTimeUpdateSave = () => {
        fetchTasks();
        setIsTimeUpdateModalOpen(false);
        setSelectedTask(null);
    };

    const renderTimeUpdateButton = (task) => {
        if (task.status == 'partial-completed' || task.status == 'completed') return null;
        return (
            <Tooltip label="Update Time" hasArrow>
                <IconButton
                    icon={<FiClock />}
                    size="sm"
                    bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                    variant="outline"
                    onClick={(e) => {
                        e.stopPropagation();
                        handleTimeUpdate(task);
                    }}
                    aria-label="Update Time"
                />
            </Tooltip>
        );
    };

    const columns = [
        { header: 'Quotation no.', field: 'RefVoucherNo' },
        { header: 'Client', field: 'client_name' },
        { header: 'Installation Date', field: 'installerTime', type: 'date' },
        { header: 'Status', field: 'status', type: 'badge' },
        { header: 'Actions', type: 'actions', render: renderTimeUpdateButton }
    ];

    const getRowCursor = (task) => task.status !== 'pending' ? 'pointer' : 'default';

    const handleRowClick = (task) => {
        if (task.status !== 'pending') {
            router.push(`/follow-up?voucher_No=${task.RefVoucherNo}`);
        }
    };

    return (
        <>
            <div className="wrapper">
                <div>
                    <div className="container">
                        <div className="page-inner">
                            <div className="row">
                                <div className="col-md-12">
                                    {/* Welcome Card */}
                                    <div className="card card-round">
                                        <div
                                            className="card-header p-4"
                                            style={{
                                                display: "flex",
                                                flexDirection: "column",
                                                gap: "10px",
                                            }}
                                        >
                                            <p style={{ fontWeight: "500", fontSize: "20px" }}>
                                                Welcome to NexSol&apos;s ERP, {user?.userName ? capitalizeWords(user.userName) : 'Anonymous'} as {userRole}
                                            </p>
                                            <p
                                                style={{
                                                    fontSize: "12px",
                                                    fontWeight: "300",
                                                    fontFamily: "'__Inter_d65c78', '__Inter_Fallback_d65c78'",
                                                }}
                                            >
                                                Discover your tailored ERP application! Every feature is
                                                designed to show how NexSol streamlines your daily operations,
                                                enhances business efficiency, and eliminates the manual
                                                processes that slow you down.
                                            </p>
                                        </div>
                                    </div>

                                    {/* Tasks Table */}
                                    <Box className="card card-round mt-4">
                                        <Box className="card-header p-3">
                                            <h4>Installer&apos;s Jobs</h4>
                                        </Box>
                                        <ReportTable
                                            data={tasks}
                                            columns={columns}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            dateField="installerTime"
                                            showDateFilter={false}
                                        />
                                    </Box>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <TimeUpdateModal
                isOpen={isTimeUpdateModalOpen}
                onClose={() => {
                    setIsTimeUpdateModalOpen(false);
                    setSelectedTask(null);
                }}
                onSave={handleTimeUpdateSave}
                taskData={selectedTask}
                role="installer"
            />
        </>
    )
}

export default InstallerDashboard;