const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { coColumns, coItemColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- CLIENT ORDER (CO) APIS ------------------- //

router.get('/tasks', async (req, res) => {
    const { getAll, role } = req.query;
    if (!role || (role !== 'installer' && role !== 'delivery')) {
        return res.status(400).send('Invalid role');
    }
    let query = `
        SELECT 
            co.*, 
            c.Title AS client_name, 
            c1.add1 AS client_address, 
            c1.email AS client_email, 
            c1.Mobile AS client_mobile_no, 
            c1.tel AS client_telephone,
            emp.Title AS emp_name,
            CASE 
                WHEN co.chk_id IS NOT NULL 
                    AND r.RefVoucherNo IS NOT NULL 
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM RecoveryFollowUps_det rd
                        WHERE rd.Voucher_No = r.Voucher_No 
                        AND rd.DeliveredRemainingItems > 0
                    ) 
                THEN 'completed'
                WHEN co.chk_id IS NOT NULL 
                    AND r.RefVoucherNo IS NOT NULL 
                    AND EXISTS (
                        SELECT 1 
                        FROM RecoveryFollowUps_det rd
                        WHERE rd.Voucher_No = r.Voucher_No 
                        AND rd.DeliveredRemainingItems > 0
                    ) 
                THEN 'partial-completed'
                WHEN co.chk_id IS NOT NULL THEN 'in-progress'
                WHEN co.chk_id IS NULL THEN 'pending'
            END AS status
        FROM 
            CO co
        LEFT JOIN 
            Coa32 c ON co.client_id = c.id
        LEFT JOIN 
            RecoveryFollowUps r ON r.RefVoucherNo = co.Voucher_No
        LEFT JOIN 
            Coa321 c1 ON co.client_id = c1.id`;
    if (role == 'installer') {
        query = `
        SELECT 
            co.*, 
            c.Title AS client_name, 
            c1.add1 AS client_address, 
            c1.email AS client_email, 
            c1.Mobile AS client_mobile_no, 
            c1.tel AS client_telephone,
            emp.Title AS emp_name,
            CASE 
                WHEN co.chk_id IS NOT NULL 
                    AND r.RefVoucherNo IS NOT NULL 
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM RecoveryFollowUps_det rd
                        WHERE rd.Voucher_No = r.Voucher_No
                        AND (rd.InstalledRemainingItems IS NULL OR rd.InstalledRemainingItems > 0)
                    )
                THEN 'completed'
                WHEN co.chk_id IS NOT NULL 
                    AND r.RefVoucherNo IS NOT NULL 
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM RecoveryFollowUps_det rd
                        WHERE rd.Voucher_No = r.Voucher_No
                        AND (rd.InstalledRemainingItems IS NULL OR rd.InstalledRemainingItems = 0)
                    )
                THEN 'partial-completed'
                WHEN co.chk_id IS NOT NULL 
                    AND r.RefVoucherNo IS NOT NULL 
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM RecoveryFollowUps_det rd
                        WHERE rd.Voucher_No = r.Voucher_No
                        AND rd.DeliveredRemainingItems > 0
                    ) 
                THEN 'in-progress'
                ELSE 'pending'
            END AS status
        FROM 
            CO co
        LEFT JOIN 
            Coa32 c ON co.client_id = c.id
        LEFT JOIN 
            RecoveryFollowUps r ON r.RefVoucherNo = co.Voucher_No
        LEFT JOIN 
            Coa321 c1 ON co.client_id = c1.id`;
    }
    const pool = await getPool();
    const request = new sql.Request(pool);
    if (role === 'installer') {
        query += ' LEFT JOIN EmployeeDetails emp ON emp.ID= co.Installer_Person';
        if (getAll == 'true') {
            query += ' WHERE co.Installer_Person IS NOT NULL';
        } else {
            query += ' WHERE co.Installer_Person = @EmployeeID';
            request.input('EmployeeID', sql.VarChar, req.user.Emp_ID);
        }
    } else if (role === 'delivery') {
        query += ' LEFT JOIN EmployeeDetails emp ON emp.ID= co.Delivery_Person';
        if (getAll == 'true') {
            query += ' WHERE co.Delivery_Person IS NOT NULL';
        } else {
            query += ' WHERE co.Delivery_Person = @EmployeeID';
            request.input('EmployeeID', sql.VarChar, req.user.Emp_ID);
        }
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        if (result.recordset.length === 0) { return res.status(404).send('No tasks found.'); }
        res.status(200).json({
            message: 'Tasks fetched successfully',
            data: result.recordset
        });
    });
});

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth } = req.body;
    let query = 'select MAX(vno) AS vno from CO where Mnth = @Mnth';
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);

    // Add location filter for non-admin users
    if (!req.user.isAdmin) {
        query += ' AND Location = @Location';
        request.input('Location', sql.VarChar(8), req.user.Location);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: 'CO',
            location: req.user.Location,
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: 'CO/' + Mnth + '/' + req.user.Location + '/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, goto, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);
    let query = '';

    if ((next && (prev || first || last || goto)) || (prev && (first || last || goto)) || (first && (last || goto)) || (last && goto) || (!next && !prev && !first && !last && !goto)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', 'last', or 'goto' exclusively, and provide 'voucher_no' if using 'next', 'prev', or 'goto'." });
    }

    const baseQuery = `
        SELECT TOP 1 
            CO.*, 
            C.Title AS clientTitle, 
            E.Title AS salesManName, 
            I.Title AS installerName, 
            D.Title AS deliveryManName, 
            CashDetails.title AS CashTitle
        FROM 
            CO
        LEFT JOIN 
            coa32 C ON C.id = CO.client_id
        LEFT JOIN 
            (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA') AS CashDetails ON CashDetails.id = CO.Cash_id
        LEFT JOIN 
            EmployeeDetails E ON E.ID = CO.SalesMan_ID
        LEFT JOIN 
            EmployeeDetails I ON I.ID = CO.Installer_Person
        LEFT JOIN 
            EmployeeDetails D ON D.ID = CO.Delivery_Person
    `;

    if (next) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery}
            WHERE (CO.VTP > (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) 
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth > (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no))
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth = (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no) AND CO.Location > (SELECT Location FROM CO WHERE voucher_no = @voucher_no))
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth = (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no) AND CO.Location = (SELECT Location FROM CO WHERE voucher_no = @voucher_no) AND CO.vno > (SELECT vno FROM CO WHERE voucher_no = @voucher_no)))
            ${!isAdminUser ? 'AND CO.Location = @Location' : ''}
            ORDER BY CO.VTP, CO.Mnth, CO.Location, CO.vno`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    } else if (prev) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery}
            WHERE (CO.VTP < (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) 
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth < (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no))
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth = (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no) AND CO.Location < (SELECT Location FROM CO WHERE voucher_no = @voucher_no))
                OR (CO.VTP = (SELECT VTP FROM CO WHERE voucher_no = @voucher_no) AND CO.Mnth = (SELECT Mnth FROM CO WHERE voucher_no = @voucher_no) AND CO.Location = (SELECT Location FROM CO WHERE voucher_no = @voucher_no) AND CO.vno < (SELECT vno FROM CO WHERE voucher_no = @voucher_no)))
            ${!isAdminUser ? 'AND CO.Location = @Location' : ''}
            ORDER BY CO.VTP DESC, CO.Mnth DESC, CO.Location DESC, CO.vno DESC`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    } else if (first) {
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery} ${!isAdminUser ? 'WHERE CO.Location = @Location' : ''} ORDER BY CO.VTP, CO.Mnth, CO.Location, CO.vno`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    } else if (last) {
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery} ${!isAdminUser ? 'WHERE CO.Location = @Location' : ''} ORDER BY CO.VTP DESC, CO.Mnth DESC, CO.Location DESC, CO.vno DESC`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    } else if (goto) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'goto'." });
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery} WHERE CO.Voucher_No = @voucher_no ${!isAdminUser ? 'AND CO.Location = @Location' : ''}`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    }

    if (voucher_no) {
        request.input('voucher_no', sql.VarChar, voucher_no);
    }

    request.query(query, async (err, result) => {
        if (err) return res.status(500).send(err);

        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('voucher_no', sql.VarChar, order.Voucher_No);
            detailsRequest.query(`SELECT C.*, COA.Title itemTitle,COA.Unit itemUnit FROM CO_det AS C LEFT JOIN Coa31 AS COA ON COA.id = C.Item_ID  WHERE voucher_no = @voucher_no`, (detailsErr, detailsResult) => {
                if (detailsErr) return res.status(500).send(detailsErr);
                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    let query = 'SELECT * FROM co';

    const pool = await getPool();
    const request = new sql.Request(pool);

    if (!req.user.isAdmin) {
        query += ' WHERE Location = @Location';
        request.input('Location', sql.VarChar(8), req.user.Location);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/getbyvoucher', async (req, res) => {
    const { voucherNo } = req.body;

    if (!voucherNo) {
        return res.status(400).send('Voucher_No is required.');
    }

    const query = `
        SELECT 
            CO.*, 
            C.Title AS clientTitle, 
            E.Title AS salesManName, 
            CashDetails.title AS CashTitle
        FROM 
            CO
        LEFT JOIN 
            coa32 C ON C.id = CO.client_id
        LEFT JOIN 
            (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA') AS CashDetails ON CashDetails.id = CO.Cash_id
        LEFT JOIN 
            EmployeeDetails E ON E.ID = CO.SalesMan_ID  WHERE Voucher_No = @Voucher_No${!req.user.isAdmin ? ' AND CO.Location = @Location' : ''}
    `;
    const itemQuery = 'SELECT C.*, COA.Title itemTitle,COA.Unit itemUnit FROM CO_det AS C LEFT JOIN Coa31 AS COA ON COA.id = C.Item_ID  WHERE voucher_no = @Voucher_No';

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        request.input('Voucher_No', sql.VarChar(16), voucherNo);

        if (!req.user.isAdmin) {
            request.input('Location', sql.VarChar(8), req.user.Location);
        }

        const result = await request.query(query);

        if (result.recordset.length === 0) {
            await transaction.rollback();
            return res.status(404).send('Client Order not found.');
        }

        const items = await request.query(itemQuery);

        await transaction.commit();
        const order = result.recordset[0];
        order['items'] = items.recordset;
        res.status(200).json(result.recordset[0]);
    } catch (err) {
        await transaction.rollback();
        res.status(500).send(err);
    }
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        coColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO CO (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        coItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO CO_det (${itemColumnsArray.join(', ')}) 
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'ClientOrder and items successfully created.',
            vtp: req.body['VTP'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});



router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];

        coColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const clientOrderQuery = `UPDATE CO SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No ${!req.user.isAdmin ? 'AND Location = @Location' : ''}`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);
        if (!req.user.isAdmin) request.input('Location', sql.VarChar(8), req.user.Location);

        const clientOrderResult = await request.query(clientOrderQuery);

        if (clientOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('ClientOrder not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM CO_det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);
            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);

            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();

            items.forEach(item => {
                const { VTP, Mnth, Location, vno, srno } = item;
                if (!VTP || !Mnth || !Location || !vno || !srno) {
                    return res.status(400).send(`Invalid DVoucher_No format in item ${i + 1}. Expected format: VTP/Mnth/Location/vno/srno.`);
                }

                const DVoucher_No = `${VTP}/${Mnth}/${Location}/${vno}/${srno}`;
                newDVoucherNos.add(DVoucher_No);

                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));

            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                const updateParams = [];

                coItemColumns.forEach(({ name, editable }) => {
                    if (item[name] !== undefined && editable) {
                        updateItemColumns.push(`${name} = @${name}`);
                        updateParams.push({ name, value: item[name] });
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE CO_det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    updateParams.forEach(({ name, value }) => {
                        updateRequest.input(name, value);
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM CO_det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];
                const insertParams = [];

                coItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                        insertParams.push({ name, value: item[name] });
                    }
                });

                const insertItemQuery = `
                    INSERT INTO CO_det (${insertItemColumns.join(', ')})
                    VALUES (${insertItemValues.join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);
                insertParams.forEach(({ name, value }) => {
                    insertRequest.input(name, value);
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('ClientOrder and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});



router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM CO_det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = `DELETE FROM CO WHERE Voucher_No = @Voucher_No ${!req.user.isAdmin ? 'AND Location = @Location' : ''}`;
        if (!req.user.isAdmin) request.input('Location', sql.VarChar(8), req.user.Location);
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('ClientOrder not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('ClientOrder and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.get('/queryClientOrder', async (req, res) => {
    const { i_vtp, i_Mnth, Location, i_vno, i_vnoTo } = req.query;

    if (!i_vtp || !i_Mnth || !Location || !i_vno || !i_vnoTo) {
        return res.status(400).send('All query parameters (i_vtp, i_Mnth, Location, i_vno, i_vnoTo) are required.');
    }

    const pool = await getPool();
    const request = new sql.Request(pool);

    request.input('i_vtp', sql.VarChar(20), i_vtp);
    request.input('i_Mnth', sql.VarChar(8), i_Mnth);
    request.input('Location', sql.VarChar(8), Location);
    request.input('i_vno', sql.Int, i_vno);
    request.input('i_vnoTo', sql.Int, i_vnoTo);

    request.execute('p_ClientOrder', (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

module.exports = router;
