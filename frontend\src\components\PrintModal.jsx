import React, { useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  Box,
  Text,
} from "@chakra-ui/react";
import { useReactToPrint } from "react-to-print";
import { useUser } from "@src/app/provider/UserContext";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

const PrintModal = ({ isOpen, onClose, children, formName = "Form Name", showHeader = true, buttonText = 'Print', callback }) => {
  const componentRef = useRef();
  const { user } = useUser();
  const userLocation = user?.Location || user?.location || "";
  const logoSrc = `${process.env.NEXT_PUBLIC_BASE_URL ? process.env.NEXT_PUBLIC_BASE_URL : ''}godown/${userLocation}/logo?fallback=true`;

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
  });

  const handleGeneratePDF = async () => {
    const canvas = await html2canvas(componentRef.current, { scale: 2 });
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF("p", "mm", "a4");
    pdf.addImage(imgData, "PNG", 10, 10, 190, 0);
    const pdfBlob = pdf.output("blob");
    if (callback) {
      callback(pdfBlob);
    } else {
      handlePrint();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full">
      <ModalOverlay />
      <ModalContent height="100vh" width="auto" overflow={"auto"}>
        <ModalHeader>Print Preview</ModalHeader>
        <ModalCloseButton />
        <ModalBody maxHeight={"90vh"} overflow={"auto"}>
          <Box
            ref={componentRef}
            minWidth="700px"
            className="print-container"
            paddingTop={12}
          >
            {showHeader &&
              <>
                <Text
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <img alt="logo" src={logoSrc} style={{ height: "70px", width: 'auto' }} />
                </Text>
                <Text
                  fontSize="xl"
                  fontStyle="italic"
                  textAlign="center"
                  margin={"20px 0 20px 0 !important"}
                >
                  {formName}
                </Text>
              </>
            }
            {children}
          </Box>
        </ModalBody>
        <ModalFooter gap={2}>
          <Button bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} onClick={handleGeneratePDF}>
            {buttonText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PrintModal;
