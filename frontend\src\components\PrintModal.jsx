import React, { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ModalCloseButton,
  Button,
  Box,
  Text,
  useToast,
} from "@chakra-ui/react";
import { useReactToPrint } from "react-to-print";
import { useUser } from "@src/app/provider/UserContext";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

const PrintModal = ({ isOpen, onClose, children, formName = "Form Name", showHeader = true, buttonText = 'Print', callback }) => {
  const componentRef = useRef();
  const { user } = useUser();
  const [isGenerating, setIsGenerating] = useState(false);
  const toast = useToast();
  const userLocation = user?.Location || user?.location || "";
  const logoSrc = `${process.env.NEXT_PUBLIC_BASE_URL ? process.env.NEXT_PUBLIC_BASE_URL : ''}godown/${userLocation}/logo?fallback=true`;

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
  });

  // Helper function to convert image URL to base64
  const convertImageToBase64 = async (url) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.warn('Failed to convert image to base64:', error);
      return null;
    }
  };

  // Helper function to wait for all images to load
  const waitForImages = async (element) => {
    const images = element.querySelectorAll('img');
    const imagePromises = Array.from(images).map(async (img) => {
      if (img.complete) return;

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.warn('Image load timeout:', img.src);
          resolve(); // Resolve anyway to not block the process
        }, 5000);

        img.onload = () => {
          clearTimeout(timeout);
          resolve();
        };
        img.onerror = () => {
          clearTimeout(timeout);
          console.warn('Image load error:', img.src);
          resolve(); // Resolve anyway to not block the process
        };
      });
    });

    await Promise.all(imagePromises);
  };

  const handleGeneratePDF = async () => {
    if (isGenerating) return;

    setIsGenerating(true);
    try {
      console.log('Starting PDF generation...');

      // Wait for all images to load
      await waitForImages(componentRef.current);
      console.log('All images loaded');

      // Convert external images to base64 to avoid CORS issues
      const images = componentRef.current.querySelectorAll('img');
      console.log(`Found ${images.length} images to process`);

      for (const img of images) {
        if (img.src.startsWith('http') && !img.src.includes('data:')) {
          console.log('Converting image to base64:', img.src);
          try {
            const base64 = await convertImageToBase64(img.src);
            if (base64) {
              img.src = base64;
              console.log('Successfully converted image to base64');
            }
          } catch (error) {
            console.warn('Failed to convert image:', img.src, error);
          }
        }
      }

      // Small delay to ensure DOM updates
      await new Promise(resolve => setTimeout(resolve, 200));

      console.log('Starting html2canvas...');
      const canvas = await html2canvas(componentRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        onclone: (clonedDoc) => {
          // Ensure all images in the cloned document are properly loaded
          const clonedImages = clonedDoc.querySelectorAll('img');
          console.log(`Found ${clonedImages.length} images in cloned document`);
          clonedImages.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
          });
        }
      });
      console.log('html2canvas completed successfully');

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF("p", "mm", "a4");

      // Calculate dimensions to fit the page properly
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const canvasAspectRatio = canvas.height / canvas.width;
      const pdfAspectRatio = pdfHeight / pdfWidth;

      let imgWidth, imgHeight;
      if (canvasAspectRatio > pdfAspectRatio) {
        imgHeight = pdfHeight - 20; // 10mm margin on top and bottom
        imgWidth = imgHeight / canvasAspectRatio;
      } else {
        imgWidth = pdfWidth - 20; // 10mm margin on left and right
        imgHeight = imgWidth * canvasAspectRatio;
      }

      const x = (pdfWidth - imgWidth) / 2;
      const y = (pdfHeight - imgHeight) / 2;

      pdf.addImage(imgData, "PNG", x, y, imgWidth, imgHeight);
      const pdfBlob = pdf.output("blob");

      if (callback) {
        callback(pdfBlob);
      } else {
        handlePrint();
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error generating PDF",
        description: "Please try again or contact support if the issue persists.",
        status: "error",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full">
      <ModalOverlay />
      <ModalContent height="100vh" width="auto" overflow={"auto"}>
        <ModalHeader>Print Preview</ModalHeader>
        <ModalCloseButton />
        <ModalBody maxHeight={"90vh"} overflow={"auto"}>
          <Box
            ref={componentRef}
            minWidth="700px"
            className="print-container"
            paddingTop={12}
          >
            {showHeader &&
              <>
                <Text
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <img alt="logo" src={logoSrc} style={{ height: "70px", width: 'auto' }} />
                </Text>
                <Text
                  fontSize="xl"
                  fontStyle="italic"
                  textAlign="center"
                  margin={"20px 0 20px 0 !important"}
                >
                  {formName}
                </Text>
              </>
            }
            {children}
          </Box>
        </ModalBody>
        <ModalFooter gap={2}>
          <Button
            bg="#2d6651"
            color="white"
            _hover={{ bg: "#3a866a" }}
            onClick={handleGeneratePDF}
            isLoading={isGenerating}
            loadingText="Generating PDF..."
          >
            {buttonText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PrintModal;
