const { body, validationResult } = require('express-validator');

const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

const godownValidationRules = {
    create: [
        body('Id').notEmpty().isString().isLength({ max: 8 }).withMessage('Id is required and max 8 chars'),
        body('Title').notEmpty().isString().isLength({ max: 70 }).withMessage('Title is required and max 70 chars'),
        body('PH').optional().isInt({ min: 0, max: 1 }).withMessage('PH must be 0 or 1'),
        body('CompanyAddress1').optional().isString().isLength({ max: 250 }),
        body('CompanyAddress2').optional().isString().isLength({ max: 250 }),
        body('CompanyPhone').optional().isString().isLength({ max: 250 }),
        body('CompanyFax').optional().isString().isLength({ max: 250 }),
        body('CompanyEmail').optional().isString().isLength({ max: 250 }),
        body('CompanyURL').optional().isString().isLength({ max: 250 }),
        body('CompanySTN').optional().isString().isLength({ max: 250 }),
        body('CompanyNTN').optional().isString().isLength({ max: 250 }),
        body('AllowNegativeBalances').optional().isInt({ min: 0, max: 1 }),
        body('Deactive').optional().isInt({ min: 0, max: 1 }),
        body('Prp_ID').optional().isString().isLength({ max: 8 }),
        body('chk_id').optional().isString().isLength({ max: 8 }),
        body('app_id').optional().isString().isLength({ max: 8 }),
        body('FinancialAcc_ID').optional().isString().isLength({ max: 32 }),
        body('ComputerName').optional().isString().isLength({ max: 120 }),
        validate
    ],
    update: [
        body('Title').optional().isString().isLength({ max: 70 }),
        body('PH').optional().isInt({ min: 0, max: 1 }),
        body('CompanyAddress1').optional().isString().isLength({ max: 250 }),
        body('CompanyAddress2').optional().isString().isLength({ max: 250 }),
        body('CompanyPhone').optional().isString().isLength({ max: 250 }),
        body('CompanyFax').optional().isString().isLength({ max: 250 }),
        body('CompanyEmail').optional().isString().isLength({ max: 250 }),
        body('CompanyURL').optional().isString().isLength({ max: 250 }),
        body('CompanySTN').optional().isString().isLength({ max: 250 }),
        body('CompanyNTN').optional().isString().isLength({ max: 250 }),
        body('AllowNegativeBalances').optional().isInt({ min: 0, max: 1 }),
        body('Deactive').optional().isInt({ min: 0, max: 1 }),
        body('Prp_ID').optional().isString().isLength({ max: 8 }),
        body('chk_id').optional().isString().isLength({ max: 8 }),
        body('app_id').optional().isString().isLength({ max: 8 }),
        body('FinancialAcc_ID').optional().isString().isLength({ max: 32 }),
        body('ComputerName').optional().isString().isLength({ max: 120 }),
        validate
    ],
    uploadLogo: [
        body('godownId').notEmpty().isString().isLength({ max: 8 }).withMessage('godownId is required'),
        validate
    ]
};

module.exports = godownValidationRules;


