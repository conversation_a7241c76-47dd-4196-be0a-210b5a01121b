const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { deliveryChallanColumns, deliveryChallanItemColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- <PERSON><PERSON><PERSON> (DC) APIS ------------------- //

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth } = req.body;
    let query = 'select MAX(vno) AS vno from DC where Mnth = @Mnth';
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);

    // Add location filter for non-admin users
    if (!req.user.isAdmin) {
        query += ' AND Location = @Location';
        request.input('Location', sql.Var<PERSON><PERSON>(8), req.user.Location);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: 'DC',
            location: req.user.Location,
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: 'DC/' + Mnth + '/' + req.user.Location + '/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);

    let query = '';
    const baseQuery = `
        SELECT TOP 1 
            DC.*,
            g.Title AS GodownName,
            c.Title AS ClientName,
            p.Title AS ProjectName,
            cc.Title AS CostCenterName,
            sm.Title AS SalesManName,
            gd.Title AS DCGodownName,
            up.Title AS PreparedByName,
            uc.Title AS CheckedByName,
            ua.Title AS ApprovedByName
        FROM 
            DC
            LEFT JOIN Godown g ON DC.Location = g.Id
            LEFT JOIN Coa32 c ON DC.Client_ID = c.id
            LEFT JOIN DefProjects p ON DC.Project_ID = p.ID
            LEFT JOIN CostCenters cc ON DC.CostCenter_ID = cc.ID
            LEFT JOIN EmployeeDetails sm ON DC.SalesMan_ID = sm.ID
            LEFT JOIN Godown gd ON DC.Godown_ID = gd.Id
            LEFT JOIN users up ON DC.prp_ID = up.id
            LEFT JOIN users uc ON DC.chk_ID = uc.id
            LEFT JOIN users ua ON DC.app_ID = ua.id
    `;

    if ((next && (prev || first || last)) || (prev && (first || last)) || (first && last) || (!next && !prev && !first && !last)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', or 'last' exclusively, and provide 'voucher_no' if using 'next' or 'prev'." });
    }

    if (next) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = req.user.isAdmin ? parts[2] : req.user.Location;
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                DC.vtp > @vtp 
                OR (DC.vtp = @vtp AND DC.Mnth > @Mnth)
                OR (DC.vtp = @vtp AND DC.Mnth = @Mnth AND DC.Location > @Location)
                OR (DC.vtp = @vtp AND DC.Mnth = @Mnth AND DC.Location = @Location AND DC.vno > @vno)
            ) AND DC.vtp = @vtp AND DC.Location = @Location
            ORDER BY DC.vtp, DC.Mnth, DC.Location, DC.vno;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (prev) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = req.user.isAdmin ? parts[2] : req.user.Location;
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                DC.vtp < @vtp 
                OR (DC.vtp = @vtp AND DC.Mnth < @Mnth)
                OR (DC.vtp = @vtp AND DC.Mnth = @Mnth AND DC.Location < @Location)
                OR (DC.vtp = @vtp AND DC.Mnth = @Mnth AND DC.Location = @Location AND DC.vno < @vno)
            ) AND DC.vtp = @vtp AND DC.Location = @Location
            ORDER BY DC.vtp DESC, DC.Mnth DESC, DC.Location DESC, DC.vno DESC;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (first) {
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery} ${!isAdminUser ? 'WHERE DC.Location = @Location' : ''} ORDER BY DC.VTP, DC.Mnth, DC.Location, DC.vno;`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    } else if (last) {
        const isAdminUser = req.user.isAdmin;
        query = `${baseQuery} ${!isAdminUser ? 'WHERE DC.Location = @Location' : ''} ORDER BY DC.VTP DESC, DC.Mnth DESC, DC.Location DESC, DC.vno DESC;`;
        if (!isAdminUser) request.input('Location', sql.VarChar(8), req.user.Location);
    }

    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }


        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const orderVoucherNo = order.Voucher_No;

            const detailsQuery = `
            SELECT
                DC_Det.*,
                C.Title AS item_title,
                C.Unit AS item_unit
            FROM 
                DC_Det
            LEFT JOIN 
                Coa31 C ON C.id = DC_Det.item_id
            WHERE voucher_no = @orderVoucherNo`;
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('orderVoucherNo', sql.VarChar, orderVoucherNo);

            detailsRequest.query(detailsQuery, (detailsErr, detailsResult) => {
                if (detailsErr) {
                    return res.status(500).send(detailsErr);
                }

                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    let query = 'SELECT * FROM DC';

    const pool = await getPool();
    const request = new sql.Request(pool);

    if (!req.user.isAdmin) {
        query += ' WHERE Location = @Location';
        request.input('Location', sql.VarChar(8), req.user.Location);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/getbyvoucher', async (req, res) => {
    const { voucherNo } = req.body;
    let query = 'SELECT * FROM DC WHERE Voucher_No = @voucherNo';

    if (!voucherNo) {
        return res.status(400).json({ message: "'voucherNo' is required." });
    }

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('voucherNo', sql.VarChar(50), voucherNo);

    if (!req.user.isAdmin) {
        query += ' AND Location = @Location';
        request.input('Location', sql.VarChar(8), req.user.Location);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        deliveryChallanColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO DC (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        deliveryChallanItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO DC_Det (${itemColumnsArray.join(', ')}) 
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'Voucher and items successfully created.',
            vtp: req.body['vtp'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        if (!voucherNo) {
            return res.status(400).send('Voucher_No is required.');
        }

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];
        deliveryChallanColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const updateQuery = `UPDATE DC SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No ${!req.user.isAdmin ? 'AND Location = @Location' : ''}`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);
        if (!req.user.isAdmin) request.input('Location', sql.VarChar(8), req.user.Location);

        const updateResult = await request.query(updateQuery);

        if (updateResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM DC_Det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);

            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);
            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();

            items.forEach(item => {
                const { vtp, Mnth, Location, vno, srno } = item;
                const DVoucher_No = `${vtp}/${Mnth}/${Location}/${vno}/${srno}`;
                newDVoucherNos.add(DVoucher_No);

                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));

            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                deliveryChallanItemColumns.forEach(({ name, editable }) => {
                    if (editable && name !== 'DVoucher_No' && item[name] !== undefined) {
                        updateItemColumns.push(`${name} = @${name}`);
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE DC_Det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    deliveryChallanItemColumns.forEach(({ name }) => {
                        if (item[name] !== undefined) {
                            updateRequest.input(name, item[name]);
                        }
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM DC_Det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];

                deliveryChallanItemColumns.forEach(({ name }) => {
                    if (name !== 'DVoucher_No' && item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                    }
                });

                const insertItemQuery = `
                    INSERT INTO DC_Det (${[...insertItemColumns].join(', ')})
                    VALUES (${[...insertItemValues].join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);

                deliveryChallanItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertRequest.input(name, item[name]);
                    }
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('Voucher and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM DC_Det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = `DELETE FROM DC WHERE Voucher_No = @Voucher_No ${!req.user.isAdmin ? 'AND Location = @Location' : ''}`;
        if (!req.user.isAdmin) request.input('Location', sql.VarChar(8), req.user.Location);
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Voucher and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

module.exports = router;
