import React, { useState } from 'react';
import {
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Badge,
    Box,
    Input,
    Button,
    HStack,
    Select,
    Text,
} from '@chakra-ui/react';
import { Search2Icon } from '@chakra-ui/icons';

const ServerPaginatedTable = ({
    data,
    columns,
    pagination,
    onPageChange,
    onPageSizeChange,
    onSearchChange,
    onRowClick,
    getRowCursor,
    getBadgeColor,
    loading = false,
}) => {
    const [searchText, setSearchText] = useState('');

    const filteredData = data;

    const handlePageChange = (newPage) => {
        if (onPageChange && newPage >= 1 && newPage <= pagination.totalPages) {
            onPageChange(newPage);
        }
    };

    const handlePageSizeChange = (newPageSize) => {
        if (onPageSizeChange) {
            onPageSizeChange(Number(newPageSize));
        }
    };

    const renderPaginationButtons = () => {
        const buttons = [];
        const { page, totalPages } = pagination;
        
        // Always show first page
        if (totalPages > 0) {
            buttons.push(
                <Button
                    key={1}
                    onClick={() => handlePageChange(1)}
                    bg={page === 1 ? '#2d6651' : 'white'}
                    color={page === 1 ? 'white' : '#2d6651'}
                    size="sm"
                >
                    1
                </Button>
            );
        }

        // Show ellipsis if needed
        if (page > 4) {
            buttons.push(<Text key="ellipsis1" px={2}>...</Text>);
        }

        // Show pages around current page
        const start = Math.max(2, page - 2);
        const end = Math.min(totalPages - 1, page + 2);

        for (let i = start; i <= end; i++) {
            buttons.push(
                <Button
                    key={i}
                    onClick={() => handlePageChange(i)}
                    colorScheme={page === i ? 'blue' : 'gray'}
                    size="sm"
                >
                    {i}
                </Button>
            );
        }

        // Show ellipsis if needed
        if (page < totalPages - 3) {
            buttons.push(<Text key="ellipsis2" px={2}>...</Text>);
        }

        // Always show last page if more than 1 page
        if (totalPages > 1) {
            buttons.push(
                <Button
                    key={totalPages}
                    onClick={() => handlePageChange(totalPages)}
                    colorScheme={page === totalPages ? 'blue' : 'gray'}
                    size="sm"
                >
                    {totalPages}
                </Button>
            );
        }

        return buttons;
    };

    return (
        <Box className="card card-round mt-4" boxShadow={'none !important'}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 5 }} className="p-3">
                <Box width={'100%'}>
                    <h6>Search</h6>
                    <Box display={'flex'} gap={2}>
                        <Input 
                            type='text' 
                            name='search' 
                            value={searchText} 
                            onChange={(e) => setSearchText(e.target.value)} 
                            placeholder="Search in all columns..."
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    onSearchChange && onSearchChange(searchText);
                                }
                            }}
                            />
                        <Button onClick={() => onSearchChange && onSearchChange(searchText)}>
                            <Search2Icon/>
                        </Button>
                    </Box>
                </Box>
            </Box>
            <Box className="card-body" boxShadow={'none !important'}>
                <TableContainer>
                    <Table variant='simple'>
                        <Thead style={{ background: "#2d6651" }}>
                            <Tr>
                                {columns.map((column, index) => (
                                    <Th key={index} style={{ color: "white" }}>{column.header}</Th>
                                ))}
                            </Tr>
                        </Thead>
                        <Tbody>
                            {loading ? (
                                <Tr>
                                    <Td colSpan={columns.length} textAlign={'center'}>Loading...</Td>
                                </Tr>
                            ) : filteredData.length > 0 ? filteredData.map((item, rowIndex) => (
                                <Tr
                                    key={rowIndex}
                                    onClick={() => onRowClick && onRowClick(item)}
                                    style={{ cursor: getRowCursor ? getRowCursor(item) : 'default' }}
                                    sx={{
                                        transition: 'all .3s',
                                        '&:hover': {
                                            background: '#0403292c'
                                        }
                                    }}
                                >
                                    {columns.map((column, colIndex) => (
                                        <Td key={colIndex}>
                                            {column.type === 'badge' ? (
                                                column.render ? (
                                                    (() => {
                                                        const badge = column.render(item);
                                                        return (
                                                            <Badge colorScheme={badge.color}>
                                                                {badge.label}
                                                            </Badge>
                                                        );
                                                    })()
                                                ) : (
                                                    <Badge colorScheme={getBadgeColor(item[column.field])}>
                                                        {item[column.field]}
                                                    </Badge>
                                                )
                                            ) : column.render ? (
                                                column.render(item)
                                            ) : (
                                                item[column.field] ? item[column.field] : 'N/A'
                                            )}
                                        </Td>
                                    ))}
                                </Tr>
                            )) : (
                                <Tr>
                                    <Td colSpan={columns.length} textAlign={'center'}>No Data Found</Td>
                                </Tr>
                            )}
                        </Tbody>
                    </Table>
                </TableContainer>
                
                {/* Pagination Controls */}
                <Box mt={4}>
                    <HStack spacing={2} justifyContent="space-between" alignItems="center">
                        <Box>
                            <Select
                                value={pagination.pageSize}
                                onChange={(e) => handlePageSizeChange(e.target.value)}
                                size="sm"
                                width="120px"
                            >
                                <option value={10}>10 per page</option>
                                <option value={20}>20 per page</option>
                                <option value={50}>50 per page</option>
                                <option value={100}>100 per page</option>
                            </Select>
                        </Box>
                        
                        <Box>
                            <Text fontSize="sm" color="gray.600">
                                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
                                {pagination.totalCount} entries
                            </Text>
                        </Box>
                        
                        <Box display={'flex'} gap={2} alignItems="center">
                            <Button 
                                onClick={() => handlePageChange(pagination.page - 1)} 
                                disabled={pagination.page === 1}
                                size="sm"
                            >
                                &laquo; Previous
                            </Button>
                            
                            {renderPaginationButtons()}
                            
                            <Button 
                                onClick={() => handlePageChange(pagination.page + 1)} 
                                disabled={pagination.page === pagination.totalPages}
                                size="sm"
                            >
                                Next &raquo;
                            </Button>
                        </Box>
                    </HStack>
                </Box>
            </Box>
        </Box>
    );
};

export default ServerPaginatedTable;
