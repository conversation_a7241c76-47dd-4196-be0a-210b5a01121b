"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/components/PrintModal.jsx":
/*!***************************************!*\
  !*** ./src/components/PrintModal.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-to-print */ \"(app-pages-browser)/./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PrintModal = (param)=>{\n    let { isOpen, onClose, children, formName = \"Form Name\", showHeader = true, buttonText = \"Print\", callback } = param;\n    _s();\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const userLocation = (user === null || user === void 0 ? void 0 : user.Location) || (user === null || user === void 0 ? void 0 : user.location) || \"\";\n    const logoSrc = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat(userLocation, \"/logo?fallback=true\");\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint)({\n        contentRef: componentRef\n    });\n    // Helper function to convert image URL to base64\n    const convertImageToBase64 = async (url)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = ()=>resolve(reader.result);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        } catch (error) {\n            console.warn(\"Failed to convert image to base64:\", error);\n            return null;\n        }\n    };\n    // Helper function to wait for all images to load\n    const waitForImages = async (element)=>{\n        const images = element.querySelectorAll(\"img\");\n        const imagePromises = Array.from(images).map(async (img)=>{\n            if (img.complete) return;\n            return new Promise((resolve, reject)=>{\n                const timeout = setTimeout(()=>{\n                    console.warn(\"Image load timeout:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                }, 5000);\n                img.onload = ()=>{\n                    clearTimeout(timeout);\n                    resolve();\n                };\n                img.onerror = ()=>{\n                    clearTimeout(timeout);\n                    console.warn(\"Image load error:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                };\n            });\n        });\n        await Promise.all(imagePromises);\n    };\n    const handleGeneratePDF = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            console.log(\"Starting PDF generation...\");\n            // Wait for all images to load\n            await waitForImages(componentRef.current);\n            console.log(\"All images loaded\");\n            // Convert external images to base64 to avoid CORS issues\n            const images = componentRef.current.querySelectorAll(\"img\");\n            console.log(\"Found \".concat(images.length, \" images to process\"));\n            for (const img of images){\n                if (img.src.startsWith(\"http\") && !img.src.includes(\"data:\")) {\n                    console.log(\"Converting image to base64:\", img.src);\n                    try {\n                        const base64 = await convertImageToBase64(img.src);\n                        if (base64) {\n                            img.src = base64;\n                            console.log(\"Successfully converted image to base64\");\n                        }\n                    } catch (error) {\n                        console.warn(\"Failed to convert image:\", img.src, error);\n                    }\n                }\n            }\n            // Small delay to ensure DOM updates\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            console.log(\"Starting html2canvas...\");\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                onclone: (clonedDoc)=>{\n                    // Ensure all images in the cloned document are properly loaded\n                    const clonedImages = clonedDoc.querySelectorAll(\"img\");\n                    console.log(\"Found \".concat(clonedImages.length, \" images in cloned document\"));\n                    clonedImages.forEach((img)=>{\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                    });\n                }\n            });\n            console.log(\"html2canvas completed successfully\");\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"](\"p\", \"mm\", \"a4\");\n            // Calculate dimensions to fit the page properly\n            const pdfWidth = pdf.internal.pageSize.getWidth();\n            const pdfHeight = pdf.internal.pageSize.getHeight();\n            const canvasAspectRatio = canvas.height / canvas.width;\n            const pdfAspectRatio = pdfHeight / pdfWidth;\n            let imgWidth, imgHeight;\n            if (canvasAspectRatio > pdfAspectRatio) {\n                imgHeight = pdfHeight - 20; // 10mm margin on top and bottom\n                imgWidth = imgHeight / canvasAspectRatio;\n            } else {\n                imgWidth = pdfWidth - 20; // 10mm margin on left and right\n                imgHeight = imgWidth * canvasAspectRatio;\n            }\n            const x = (pdfWidth - imgWidth) / 2;\n            const y = (pdfHeight - imgHeight) / 2;\n            pdf.addImage(imgData, \"PNG\", x, y, imgWidth, imgHeight);\n            const pdfBlob = pdf.output(\"blob\");\n            if (callback) {\n                callback(pdfBlob);\n            } else {\n                handlePrint();\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            toast({\n                title: \"Error generating PDF\",\n                description: \"Please try again or contact support if the issue persists.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalContent, {\n                height: \"100vh\",\n                width: \"auto\",\n                overflow: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalHeader, {\n                        children: \"Print Preview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalBody, {\n                        maxHeight: \"90vh\",\n                        overflow: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Box, {\n                            ref: componentRef,\n                            minWidth: \"700px\",\n                            className: \"print-container\",\n                            children: [\n                                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"center\",\n                                                alignItems: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                alt: \"logo\",\n                                                src: logoSrc,\n                                                style: {\n                                                    height: \"70px\",\n                                                    width: \"auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            fontSize: \"xl\",\n                                            fontStyle: \"italic\",\n                                            textAlign: \"center\",\n                                            margin: \"20px 0 20px 0 !important\",\n                                            children: formName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                children\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalFooter, {\n                        gap: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: handleGeneratePDF,\n                            isLoading: isGenerating,\n                            loadingText: \"Generating PDF...\",\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrintModal, \"GEIWinyei48BORxa3W6Bbzad+uA=\", false, function() {\n    return [\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint\n    ];\n});\n_c = PrintModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PrintModal);\nvar _c;\n$RefreshReg$(_c, \"PrintModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PrintModal.jsx\n"));

/***/ })

});