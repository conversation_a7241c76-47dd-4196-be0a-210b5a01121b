"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    _s();\n    const [logoSrc, setLogoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Preload the logo image to ensure it's available for PDF generation\n        const img = new next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            setLogoSrc(logoImage);\n        };\n        img.onerror = ()=>{\n            console.warn(\"Failed to load logo image:\", logoImage);\n            setLogoSrc(\"\"); // Set empty to hide broken image\n        };\n        img.src = logoImage;\n    }, [\n        logoImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        p: 8,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: logoImage,\n                            alt: \"Eco Assets Manager\",\n                            style: {\n                                height: \"100px\",\n                                width: \"auto\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponentToPrint, \"bQCJrah25Bx45JT2l5QhtQPkQ6I=\");\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});