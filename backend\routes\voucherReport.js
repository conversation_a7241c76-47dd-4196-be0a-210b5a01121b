const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');

router.use(authorization);

// Unified voucher report endpoint that combines cash and bank voucher data
router.get('/report', async (req, res) => {
    const pool = await getPool();
    const request = new sql.Request(pool);
    const { page = 1, pageSize = 20, searchText = '' } = req.query;
    const offset = (page - 1) * pageSize;

    // Add search filter safely using parameters
    if (searchText) {
        request.input('searchText', sql.VarChar, `%${searchText}%`);
    }
    
    const query = `
        WITH PaymentSummary AS (
            SELECT
                a.adj_VoucherNo,
                SUM(a.TotalAmt) AS TotalPaid
            FROM ac_det a
            WHERE a.adj_VoucherNo IS NOT NULL
                AND (a.vtp = 'CR' OR a.vtp = 'BR')
            GROUP BY a.adj_VoucherNo
        ),
        UniqueQuotations AS (
            SELECT DISTINCT
                a.adj_VoucherNo,
                o.client_id as acc_id,
                o.GrossAmount,
                COALESCE(ps.TotalPaid, 0) AS TotalAmt,
                o.GrossAmount - COALESCE(ps.TotalPaid, 0) AS RemainingAmount,
                CASE
                    WHEN COALESCE(ps.TotalPaid, 0) = 0 THEN 'pending'
                    WHEN COALESCE(ps.TotalPaid, 0) >= o.GrossAmount THEN 'completed'
                    ELSE 'partial'
                END AS status
            FROM ac_det a
            INNER JOIN Offer O ON O.Voucher_No = a.adj_VoucherNo
            LEFT JOIN PaymentSummary ps ON ps.adj_VoucherNo = a.adj_VoucherNo
            WHERE a.adj_VoucherNo IS NOT NULL
                AND (a.vtp = 'CR' OR a.vtp = 'BR')
        ),
        FilteredData AS (
            SELECT
                uq.adj_VoucherNo,
                uq.acc_id,
                Client.title AS acc_name,
                uq.GrossAmount,
                uq.TotalAmt,
                uq.RemainingAmount,
                uq.status
            FROM UniqueQuotations uq
            LEFT JOIN (
                SELECT C.id, C.title
                FROM coa32 C
                LEFT JOIN Coa3 C3 ON C3.id = C.id
                WHERE C3.atp2_ID = 'CT'
            ) AS Client ON Client.id = uq.acc_id
            WHERE 1=1 ${searchText ? `
                AND (
                    uq.adj_VoucherNo LIKE @searchText
                    OR Client.title LIKE @searchText
                )` : ''}
        ),
        PagedData AS (
            SELECT
                adj_VoucherNo,
                acc_id,
                acc_name,
                GrossAmount,
                TotalAmt,
                RemainingAmount,
                status,
                ROW_NUMBER() OVER (ORDER BY adj_VoucherNo) AS RowNum,
                COUNT(*) OVER() AS TotalCount
            FROM FilteredData
        )
        SELECT
            adj_VoucherNo as quotation_no,
            acc_id as client_id,
            acc_name as client_name,
            GrossAmount as gross_amount,
            TotalAmt as paid_amount,
            RemainingAmount as remaining_amount,
            status,
            TotalCount
        FROM PagedData
        WHERE RowNum BETWEEN ${offset + 1} AND ${offset + parseInt(pageSize)}
        ORDER BY adj_VoucherNo;
    `;

    try {
        const result = await request.query(query);
        const totalCount = result.recordset.length > 0 ? result.recordset[0].TotalCount : 0;
        const totalPages = Math.ceil(totalCount / pageSize);

        res.status(200).json({
            data: result.recordset.map(row => ({
                quotation_no: row.quotation_no,
                client_id: row.client_id,
                client_name: row.client_name || 'N/A',
                gross_amount: row.gross_amount,
                paid_amount: row.paid_amount,
                remaining_amount: row.remaining_amount,
                status: row.status
            })),
            pagination: {
                current_page: parseInt(page),
                total_pages: totalPages,
                total_records: totalCount,
                page_size: parseInt(pageSize)
            }
        });
    } catch (error) {
        console.error('Error fetching unified voucher report:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Unified voucher details endpoint with payment history from both cash and bank
router.get('/voucher-details', async (req, res) => {
    const { quotationNo } = req.query;
    const pool = await getPool();

    try {
        // Get voucher basic details with combined payment summary
        const voucherRequest = new sql.Request(pool);
        voucherRequest.input('QuotationNo', sql.VarChar(50), quotationNo);

        const voucherQuery = `
            WITH PaymentSummary AS (
                SELECT
                    a.adj_VoucherNo,
                    SUM(a.TotalAmt) AS TotalPaid
                FROM ac_det a
                WHERE a.adj_VoucherNo = @QuotationNo
                    AND (a.vtp = 'CR' OR a.vtp = 'BR')
                GROUP BY a.adj_VoucherNo
            )
            SELECT TOP 1
                o.Voucher_No,
                o.Voucher_No as adj_VoucherNo,
                o.client_id as acc_id,
                Client.title AS acc_name,
                o.GrossAmount,
                COALESCE(ps.TotalPaid, 0) AS TotalAmt,
                o.GrossAmount - COALESCE(ps.TotalPaid, 0) AS RemainingAmount,
                CASE
                    WHEN COALESCE(ps.TotalPaid, 0) = 0 THEN 'pending'
                    WHEN COALESCE(ps.TotalPaid, 0) >= o.GrossAmount THEN 'completed'
                    ELSE 'partial'
                END AS status,
                o.Dated,
                o.StartingComments
            FROM Offer O
            LEFT JOIN PaymentSummary ps ON ps.adj_VoucherNo = o.Voucher_No
            LEFT JOIN (
                SELECT C.id, C.title
                FROM coa32 C
                LEFT JOIN Coa3 C3 ON C3.id = C.id
                WHERE C3.atp2_ID = 'CT'
            ) AS Client ON Client.id = o.client_id
            WHERE o.Voucher_No = @QuotationNo
        `;

        const voucherResult = await voucherRequest.query(voucherQuery);
        
        if (voucherResult.recordset.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }

        const voucherDetails = voucherResult.recordset[0];

        // Get payment history from both cash and bank transactions
        const historyRequest = new sql.Request(pool);
        historyRequest.input('QuotationNo', sql.VarChar(50), quotationNo);

        const historyQuery = `
            SELECT 
                a.Voucher_No as payment_voucher_no,
                a.TotalAmt as payment_amount,
                ac.Dated as payment_date,
                a.ln_rem as payment_description,
                a.vtp as payment_type
            FROM ac_det a
            LEFT JOIN AC ac ON ac.Voucher_No = a.Voucher_No
            WHERE a.adj_VoucherNo = @QuotationNo
                AND (a.vtp = 'CR' OR a.vtp = 'BR')
            ORDER BY ac.Dated DESC
        `;

        const historyResult = await historyRequest.query(historyQuery);
        const paymentHistory = historyResult.recordset;

        res.status(200).json({
            voucher_details: {
                voucher_no: voucherDetails.Voucher_No,
                quotation_no: voucherDetails.adj_VoucherNo,
                client_id: voucherDetails.acc_id,
                client_name: voucherDetails.acc_name || 'N/A',
                gross_amount: voucherDetails.GrossAmount,
                paid_amount: voucherDetails.TotalAmt,
                remaining_amount: voucherDetails.RemainingAmount,
                status: voucherDetails.status,
                created_at: voucherDetails.Dated,
                description: voucherDetails.StartingComments
            },
            payment_history: paymentHistory.map(payment => ({
                payment_voucher_no: payment.payment_voucher_no,
                payment_amount: payment.payment_amount,
                payment_date: payment.payment_date,
                payment_description: payment.payment_description,
                payment_type: payment.payment_type
            }))
        });

    } catch (error) {
        console.error('Error fetching unified voucher details:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
