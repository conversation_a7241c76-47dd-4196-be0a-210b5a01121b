
// Cash Receipt & Cash Payment Voucher Starts Here----------------------------------------------------------------

export const CashReceiptVoucherItemTableHeader = ["ID", "Title"];

export const CashReceiptVoucherHeaders = [
    {
        label: "A/C ID",
        key: "AC_ID",
        width: "110px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Title",
        key: "Title",
        width: "270px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Adj. Voucher No",
        key: "AdjVoucherNo",
        width: "140px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Total Amount",
        key: "TotalAmount",
        width: "120px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Paid Amount",
        key: "PaidAmount",
        width: "120px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Remaining Amount",
        key: "RemainingAmount",
        width: "130px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Received Amt",
        key: "ReceivedAmt",
        width: "120px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Narration",
        key: "Narration",
        width: "320px",
        isReadOnly: false,
        type: "text",
    },
];

export const CashReceiptVoucherSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "Date",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`,
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Voucher No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Cash Code",
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "cash",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Title"],
                    keys: ["id", "title"],
                    nameFields: ["cashId", "cashTitle"],
                },
            ],
        }
    ], [
        {
            label: "Total Amt",
            name: "totalAmt",
            style: { width: { base: "100%", sm: "100%", lg: "100%" } },
            value: "totalAmt",
            isReadOnly: true,
        }
    ]
];


export const CashReceiptVoucherCalculationFields = [
    "totalTaxAmt", "totalAmt", "totalAmtReceived"
];

export const CreateCashReceiptVoucherEmptyTableRow = () => ([{
    AC_ID: "", Title: "", Emp_ID: "", Emp_Title: "",
    AdjVoucherNo: "", TotalAmount: "", PaidAmount: "", RemainingAmount: "",
    ReceivedAmt: "", Narration: "",
}]);

export const CreateCashReceiptVoucherInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "",
    mnth: "", cashId: "", cashTitle: "", cCenterId: "",
    cCenterName: "", narration: "", totalAmt: 0,
    totalAmtReceived: 0
});

export const CashReceiptVoucherRequiredFields = [
    "Dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
    "amt",
    "bc_id"
];

export const CashReceiptVoucherItemsRequiredFields = [
    "Dated",
    "VTP",
    "Mnth",
    "Location",
    "vno",
    "srno",
    "acc_id",
    "Currency_ID",
    "ln_rem",
    "Amt",
    "TotalAmt",
];

// Cash Receipt & Cash Payment Voucher Ends Here----------------------------------------------------------------
