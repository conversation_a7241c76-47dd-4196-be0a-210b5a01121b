import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  useToast,
  FormErrorMessage,
} from "@chakra-ui/react";
import axiosInstance from "@src/app/axios";
import React, { useState, useEffect } from 'react';
import TimePickerInput from '../TimePickerInput';

const TimeUpdateModal = ({ isOpen, onClose, onSave, taskData, role }) => {
  const toast = useToast();
  const [formData, setFormData] = useState({
    newTime: '',
    reason: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({ ...prevState, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.newTime) {
      newErrors.newTime = 'New time is required';
    }
    if (!formData.reason.trim()) {
      newErrors.reason = 'Reason is required';
    } else if (formData.reason.trim().length < 10) {
      newErrors.reason = 'Reason must be at least 10 characters long';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await axiosInstance.put(
        `client/update-time/${role}/${taskData.ID}`,
        {
          newTime: formData.newTime,
          reason: formData.reason.trim()
        }
      );
      toast({
        title: "Time updated successfully",
        description: `${role.charAt(0).toUpperCase() + role.slice(1)} time has been updated.`,
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top-right"
      });
      if (onSave) {
        onSave(response.data);
      }
      handleClose();
    } catch (error) {
      console.error('Error updating time:', error);
      toast({
        title: "Error updating time",
        description: error.response?.data?.message || "An error occurred while updating the time.",
        status: "error",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({ newTime: '', reason: '' });
    setErrors({});
    onClose();
  };

  useEffect(() => {
    if (isOpen && taskData) {
      const currentTime = taskData[role === 'delivery' ? 'deliveryPersonTime' :
        role === 'installer' ? 'installerTime' : 'assessmentTime'];
      if (currentTime) {
        const date = new Date(currentTime);
        const formattedTime = date.toISOString().slice(0, 16);
        setFormData(prev => ({ ...prev, newTime: formattedTime }));
      }
    }
  }, [isOpen, taskData, role]);

  const getModalTitle = () => {
    return `Update ${role.charAt(0).toUpperCase() + role.slice(1)} Time`;
  };

  const getCurrentTimeFormatted = () => {
    if (!taskData) return 'N/A';
    const currentTime = taskData[role === 'delivery' ? 'deliveryPersonTime' :
      role === 'installer' ? 'installerTime' : 'assessmentTime'];
    if (!currentTime) return 'N/A';
    return new Date(currentTime).toLocaleString();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{getModalTitle()}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4}>
            <FormControl>
              <FormLabel>Current Time</FormLabel>
              <Input
                value={getCurrentTimeFormatted()}
                isReadOnly
                bg="gray.100"
              />
            </FormControl>
            <TimePickerInput
              label="New Time"
              name="newTime"
              value={formData.newTime}
              onChange={handleChange}
              isRequired={true}
              isInvalid={!!errors.newTime}
              errorMessage={errors.newTime}
              placeholder="Select new date and time"
            />
            <FormControl isRequired isInvalid={!!errors.reason}>
              <FormLabel>Reason for Change</FormLabel>
              <Textarea
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                placeholder="Please provide a detailed reason for changing the time..."
                rows={4}
                resize="vertical"
              />
              <FormErrorMessage>{errors.reason}</FormErrorMessage>
            </FormControl>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
            onClick={handleSave}
            isLoading={isLoading}
            loadingText="Updating..."
          >
            Update Time
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default TimeUpdateModal;
