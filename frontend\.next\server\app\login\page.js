/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.jsx&appDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.jsx&appDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.jsx */ \"(rsc)/./src/app/login/page.jsx\")), \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.jsx&appDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cbootstrap.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cplugins.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Ckaiadmin.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cprovider%5C%5CUserContext.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cbootstrap.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cplugins.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Ckaiadmin.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cprovider%5C%5CUserContext.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/provider/UserContext.js */ \"(ssr)/./src/app/provider/UserContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cbootstrap.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Cplugins.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Cpublic%5C%5Cassets%5C%5Ccss%5C%5Ckaiadmin.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cprovider%5C%5CUserContext.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.jsx */ \"(ssr)/./src/app/login/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25lbCU1QyU1Q05leFNvbCUyMFRlY2glNUMlNUNFUlAlNUMlNUNJbXBleEdyYWNlJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBb0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvP2Y1YjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQZXJzb25lbFxcXFxOZXhTb2wgVGVjaFxcXFxFUlBcXFxcSW1wZXhHcmFjZVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonel%5C%5CNexSol%20Tech%5C%5CERP%5C%5CImpexGrace%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/axios.js":
/*!**************************!*\
  !*** ./src/app/axios.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"http://localhost:5002/api/\"\n});\naxiosInstance.interceptors.request.use((config)=>{\n    if (config.url.includes(\"login\")) {\n        delete config.headers[\"authorization\"];\n    } else {\n        config.headers[\"authorization\"] = \"Basic \" + js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth-token\");\n    }\n    // console.log('Request:', config);\n    return config;\n}, (error)=>{\n    // console.error('Request Error:', error);\n    return Promise.reject(error);\n});\naxiosInstance.interceptors.response.use((response)=>{\n    // console.log('Response:', response);\n    return response;\n}, (error)=>{\n    // console.error('Response Error:', error);\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/axios.js\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.jsx":
/*!********************************!*\
  !*** ./src/app/login/page.jsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _login_login_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @src/app/login/login.css */ \"(ssr)/./src/app/login/login.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/input/input-group.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/input/input-element.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaEye_FaEyeSlash_FaLock_FaUserAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaEye,FaEyeSlash,FaLock,FaUserAlt!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader_Loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Loader/Loader */ \"(ssr)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var _provider_UserContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../provider/UserContext */ \"(ssr)/./src/app/provider/UserContext.js\");\n/* harmony import */ var _src_app_login_log_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/app/login/log.svg */ \"(ssr)/./src/app/login/log.svg\");\n/* harmony import */ var _src_app_login_register_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @src/app/login/register.svg */ \"(ssr)/./src/app/login/register.svg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nconst Login = ()=>{\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleShowClick = ()=>setShowPassword(!showPassword);\n    const [ID, setID] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { login, loading } = (0,_provider_UserContext__WEBPACK_IMPORTED_MODULE_5__.useUser)();\n    // const toast = useToast();\n    // const router = useRouter();\n    const Authentication = (e)=>{\n        e.preventDefault();\n        login(ID, password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n            lineNumber: 45,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            class: \"container20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    class: \"forms-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        class: \"signin-signup\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: Authentication,\n                            class: \"sign-in-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    class: \"title\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    class: \"input-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEye_FaEyeSlash_FaLock_FaUserAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaUserAlt, {\n                                                class: \"fas fa-user\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingRight: \"60px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                                value: ID,\n                                                onChange: (e)=>setID(e.target.value),\n                                                type: \"text\",\n                                                placeholder: \"User Name\",\n                                                style: {\n                                                    height: \"55px\",\n                                                    border: \"none\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    class: \"input-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEye_FaEyeSlash_FaLock_FaUserAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaLock, {\n                                                class: \"fas fa-user\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.InputGroup, {\n                                            style: {\n                                                paddingRight: \"60px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    placeholder: \"Password\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    style: {\n                                                        height: \"55px\",\n                                                        border: \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.InputRightElement, {\n                                                    width: \"4.5rem\",\n                                                    style: {\n                                                        height: \"55px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        colorScheme: \"blackAlpha\",\n                                                        color: \"white\",\n                                                        style: {\n                                                            fontSize: \"1.5rem\"\n                                                        },\n                                                        onClick: handleShowClick,\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEye_FaEyeSlash_FaLock_FaUserAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaEyeSlash, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 41\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEye_FaEyeSlash_FaLock_FaUserAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaEye, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 58\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                    style: {\n                                        width: \"150px\",\n                                        marginTop: \"5px\"\n                                    },\n                                    bg: \"#2d6651\",\n                                    color: \"white\",\n                                    _hover: {\n                                        bg: \"#3a866a\"\n                                    },\n                                    type: \"submit\",\n                                    children: \"LOGIN\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                            lineNumber: 50,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    class: \"panels-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        class: \"panel left-panel\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                class: \"content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontFamily: \"'Poppins'\"\n                                        },\n                                        children: \"ECO ASSET MANAGER\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontFamily: \"'__Inter_d65c78'\"\n                                        },\n                                        children: \"Enterprise Resource Planning (ERP) software developed and maintaned by NexSol Tech.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                src: _src_app_login_register_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                className: \"image\",\n                                alt: \"LoginImage\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\login\\\\page.jsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Login);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/provider/UserContext.js":
/*!*****************************************!*\
  !*** ./src/app/provider/UserContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../axios */ \"(ssr)/./src/app/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst UserProvider = ({ children })=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const userRole = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>user && user?.roleName, [\n        user\n    ]);\n    const stripImageDataForCookie = (userData)=>{\n        if (!userData) return null;\n        const { imageUrl, ...strippedData } = userData;\n        return strippedData;\n    };\n    const storeUserData = (userData, authToken)=>{\n        setUser(userData);\n        const cookieData = stripImageDataForCookie(userData);\n        js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"user\", JSON.stringify(cookieData), {\n            expires: 7,\n            path: \"/\",\n            sameSite: \"strict\"\n        });\n        if (authToken) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].set(\"auth-token\", authToken, {\n                expires: 7,\n                path: \"/\",\n                sameSite: \"strict\"\n            });\n        }\n    };\n    const autoLogin = async (userName, password)=>{\n        try {\n            const res = await _axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"user/login\", {\n                userName: userName,\n                password: password\n            });\n            let userData = res.data.data;\n            const authToken = btoa(`${userName}:${password}`);\n            storeUserData(userData, authToken);\n            return true;\n        } catch (error) {\n            console.error(\"Auto login failed:\", error);\n            return false;\n        }\n    };\n    const clearSession = (shouldRedirect = true)=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"auth-token\", {\n            path: \"/\"\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].remove(\"user\", {\n            path: \"/\"\n        });\n        setUser(null);\n        if (shouldRedirect) {\n            // Check if already on login page before redirecting\n            const currentPath = window.location.pathname;\n            if (currentPath !== \"/login\") {\n                router.push(\"/login\");\n            }\n        }\n    };\n    const updateUserProfile = (updatedData)=>{\n        const newUserData = {\n            ...user,\n            ...updatedData\n        };\n        storeUserData(newUserData);\n    };\n    const checkSession = async ()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"user\");\n        const authToken = js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"auth-token\");\n        if (!userCookie || !authToken) {\n            clearSession(false);\n            return false;\n        }\n        try {\n            const userData = JSON.parse(userCookie);\n            if (!userData || !userData.roleName) {\n                clearSession(false);\n                return false;\n            }\n            const credentials = atob(authToken).split(\":\");\n            const loginSuccess = await autoLogin(credentials[0], credentials[1]);\n            if (!loginSuccess) {\n                clearSession(false);\n            }\n            if (!success) {\n                clearSession(false);\n                return false;\n            }\n            return true;\n        } catch (error) {\n            clearSession(false);\n            return false;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            setLoading(true);\n            const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"user\");\n            const authToken = js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"auth-token\");\n            if (!userCookie || !authToken) {\n                clearSession(false);\n                setLoading(false);\n                return;\n            }\n            try {\n                const credentials = atob(authToken).split(\":\");\n                const res = await _axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"user/login\", {\n                    userName: credentials[0],\n                    password: credentials[1]\n                });\n                const userData = res.data.data;\n                storeUserData(userData, authToken);\n                const currentPath = window.location.pathname;\n                if (currentPath === \"/login\") {\n                    router.push(\"/dashboard\");\n                }\n            } catch (error) {\n                console.error(\"Session initialization error:\", error);\n                clearSession(false);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeSession();\n    }, []);\n    const login = async (userName, password)=>{\n        setLoading(true);\n        try {\n            const res = await _axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"user/login\", {\n                userName: userName,\n                password: password\n            });\n            let userData = res.data.data;\n            const authToken = btoa(`${userName}:${password}`);\n            storeUserData(userData, authToken);\n            router.push(\"/dashboard\");\n            toast({\n                title: \"Login Successfully !\",\n                status: \"success\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } catch (err) {\n            toast({\n                title: \"Login Failed !\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        clearSession(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            user,\n            login,\n            logout,\n            loading,\n            userRole,\n            checkSession,\n            updateUserProfile\n        },\n        children: [\n            \" \",\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\provider\\\\UserContext.js\",\n        lineNumber: 193,\n        columnNumber: 9\n    }, undefined);\n};\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (!context) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/provider/UserContext.js\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_next_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/next-js */ \"(ssr)/./node_modules/@chakra-ui/next-js/dist/chunk-U7SQ5CYR.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/chakra-provider.mjs\");\n// app/providers.tsx\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_next_js__WEBPACK_IMPORTED_MODULE_1__.CacheProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ChakraProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxvQkFBb0I7O0FBRytCO0FBQ0Q7QUFFM0MsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQ2pFLHFCQUNJLDhEQUFDSCw2REFBYUE7a0JBQ1YsNEVBQUNDLDREQUFjQTtzQkFBRUU7Ozs7Ozs7Ozs7O0FBRzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vc3JjL2FwcC9wcm92aWRlcnMudHN4PzkzMjYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gYXBwL3Byb3ZpZGVycy50c3hcclxuJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgQ2FjaGVQcm92aWRlciB9IGZyb20gJ0BjaGFrcmEtdWkvbmV4dC1qcyc7XHJcbmltcG9ydCB7IENoYWtyYVByb3ZpZGVyIH0gZnJvbSAnQGNoYWtyYS11aS9yZWFjdCc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPENhY2hlUHJvdmlkZXI+XHJcbiAgICAgICAgICAgIDxDaGFrcmFQcm92aWRlcj57Y2hpbGRyZW59PC9DaGFrcmFQcm92aWRlcj5cclxuICAgICAgICA8L0NhY2hlUHJvdmlkZXI+XHJcbiAgICApO1xyXG59Il0sIm5hbWVzIjpbIkNhY2hlUHJvdmlkZXIiLCJDaGFrcmFQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Loader/Loader.jsx":
/*!******************************************!*\
  !*** ./src/components/Loader/Loader.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Loader_Loader_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Loader/Loader.css */ \"(ssr)/./src/components/Loader/Loader.css\");\n\n\n\nconst Loader = (onSetLoading)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loaderContainer\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scene\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"cube-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cube\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"cube-faces\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face shadow\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face bottom\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face top\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face back\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cube-face front\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                            lineNumber: 11,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                        lineNumber: 10,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Loader\\\\Loader.jsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Loader/Loader.jsx\n");

/***/ }),

/***/ "(rsc)/./public/assets/css/bootstrap.min.css":
/*!*********************************************!*\
  !*** ./public/assets/css/bootstrap.min.css ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5a4f4a7bdb13\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvYXNzZXRzL2Nzcy9ib290c3RyYXAubWluLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL3B1YmxpYy9hc3NldHMvY3NzL2Jvb3RzdHJhcC5taW4uY3NzPzAxMDEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1YTRmNGE3YmRiMTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./public/assets/css/bootstrap.min.css\n");

/***/ }),

/***/ "(rsc)/./public/assets/css/kaiadmin.min.css":
/*!********************************************!*\
  !*** ./public/assets/css/kaiadmin.min.css ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"510489518255\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvYXNzZXRzL2Nzcy9rYWlhZG1pbi5taW4uY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vcHVibGljL2Fzc2V0cy9jc3Mva2FpYWRtaW4ubWluLmNzcz82ZDU0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTEwNDg5NTE4MjU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./public/assets/css/kaiadmin.min.css\n");

/***/ }),

/***/ "(rsc)/./public/assets/css/plugins.min.css":
/*!*******************************************!*\
  !*** ./public/assets/css/plugins.min.css ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"979bedeb771e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvYXNzZXRzL2Nzcy9wbHVnaW5zLm1pbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9wdWJsaWMvYXNzZXRzL2Nzcy9wbHVnaW5zLm1pbi5jc3M/ZTYwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk3OWJlZGViNzcxZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./public/assets/css/plugins.min.css\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"94ff86ed8317\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81NjQ3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTRmZjg2ZWQ4MzE3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/login/login.css":
/*!*********************************!*\
  !*** ./src/app/login/login.css ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6eba53c3477a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL2xvZ2luLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL3NyYy9hcHAvbG9naW4vbG9naW4uY3NzP2Q2ZjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2ZWJhNTNjMzQ3N2FcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/login.css\n");

/***/ }),

/***/ "(ssr)/./src/components/Loader/Loader.css":
/*!******************************************!*\
  !*** ./src/components/Loader/Loader.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0e122f3f4939\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2FkZXIvTG9hZGVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL3NyYy9jb21wb25lbnRzL0xvYWRlci9Mb2FkZXIuY3NzP2U0MzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZTEyMmYzZjQ5MzlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Loader/Loader.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _assets_assets_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/css/bootstrap.min.css */ \"(rsc)/./public/assets/css/bootstrap.min.css\");\n/* harmony import */ var _assets_assets_css_plugins_min_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/css/plugins.min.css */ \"(rsc)/./public/assets/css/plugins.min.css\");\n/* harmony import */ var _assets_assets_css_kaiadmin_min_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @assets/assets/css/kaiadmin.min.css */ \"(rsc)/./public/assets/css/kaiadmin.min.css\");\n/* harmony import */ var _provider_UserContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./provider/UserContext */ \"(rsc)/./src/app/provider/UserContext.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"ECO Asset Manager\",\n    description: \"Enterprise Resource Planning (ERP) Software developed by NexSol Tech for ECO Asset Management.\",\n    manifest: \"/manifest.json\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"http://localhost:3000/assets/imgs/ECO_asset-manager.png\",\n                        type: \"image/x-icon\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/core/jquery-3.7.1.min.js\",\n                        strategy: \"beforeInteractive\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/core/popper.min.js\",\n                        strategy: \"beforeInteractive\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/core/bootstrap.min.js\",\n                        strategy: \"beforeInteractive\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js\",\n                        strategy: \"beforeInteractive\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/plugin/jquery.sparkline/jquery.sparkline.min.js\",\n                        strategy: \"beforeInteractive\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/plugin/jsvectormap/jsvectormap.min.js\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/plugin/jsvectormap/world.js\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/plugin/sweetalert/sweetalert.min.js\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"../assets/js/kaiadmin.min.js\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_7__.Providers, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_UserContext__WEBPACK_IMPORTED_MODULE_6__.UserProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.jsx":
/*!********************************!*\
  !*** ./src/app/login/page.jsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personel\NexSol Tech\ERP\ImpexGrace\frontend\src\app\login\page.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/provider/UserContext.js":
/*!*****************************************!*\
  !*** ./src/app/provider/UserContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserProvider: () => (/* binding */ e0),
/* harmony export */   useUser: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personel\NexSol Tech\ERP\ImpexGrace\frontend\src\app\provider\UserContext.js#UserProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personel\NexSol Tech\ERP\ImpexGrace\frontend\src\app\provider\UserContext.js#useUser`);


/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\Personel\\NexSol Tech\\ERP\\ImpexGrace\\frontend\\src\\app\\providers.tsx#Providers`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3g/OTMyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBhcHAvcHJvdmlkZXJzLnRzeFxyXG4ndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBDYWNoZVByb3ZpZGVyIH0gZnJvbSAnQGNoYWtyYS11aS9uZXh0LWpzJztcclxuaW1wb3J0IHsgQ2hha3JhUHJvdmlkZXIgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8Q2FjaGVQcm92aWRlcj5cclxuICAgICAgICAgICAgPENoYWtyYVByb3ZpZGVyPntjaGlsZHJlbn08L0NoYWtyYVByb3ZpZGVyPlxyXG4gICAgICAgIDwvQ2FjaGVQcm92aWRlcj5cclxuICAgICk7XHJcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/login/log.svg":
/*!*******************************!*\
  !*** ./src/app/login/log.svg ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/log.bca7a89a.svg\",\"height\":787,\"width\":1141,\"blurWidth\":0,\"blurHeight\":0});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL2xvZy5zdmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsb0dBQW9HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vc3JjL2FwcC9sb2dpbi9sb2cuc3ZnP2ZiNDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZy5iY2E3YTg5YS5zdmdcIixcImhlaWdodFwiOjc4NyxcIndpZHRoXCI6MTE0MSxcImJsdXJXaWR0aFwiOjAsXCJibHVySGVpZ2h0XCI6MH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/log.svg\n");

/***/ }),

/***/ "(ssr)/./src/app/login/register.svg":
/*!************************************!*\
  !*** ./src/app/login/register.svg ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/register.bbb359d9.svg\",\"height\":797,\"width\":999,\"blurWidth\":0,\"blurHeight\":0});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL3JlZ2lzdGVyLnN2ZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyx3R0FBd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9zcmMvYXBwL2xvZ2luL3JlZ2lzdGVyLnN2Zz82OWZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9yZWdpc3Rlci5iYmIzNTlkOS5zdmdcIixcImhlaWdodFwiOjc5NyxcIndpZHRoXCI6OTk5LFwiYmx1cldpZHRoXCI6MCxcImJsdXJIZWlnaHRcIjowfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/register.svg\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2M2MDYiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@chakra-ui","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@emotion","vendor-chunks/lodash.mergewith","vendor-chunks/stylis","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/color2k","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/react-fast-compare","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/hoist-non-react-statics","vendor-chunks/delayed-stream","vendor-chunks/has-flag","vendor-chunks/react-icons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.jsx&appDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonel%5CNexSol%20Tech%5CERP%5CImpexGrace%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();