import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  Text,
  useToast
} from '@chakra-ui/react';
import jsPDF from 'jspdf';
import dayjs from 'dayjs';

const AssessorPDFGenerator = ({ isOpen, onClose, data, onPDFGenerated }) => {
  const toast = useToast();

  const generatePDF = async () => {
    try {
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      let yPosition = margin;



      // Helper function to add a new page if needed
      const checkPageBreak = (requiredSpace) => {
        if (yPosition + requiredSpace > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
      };

      // Modern gradient-style header
      pdf.setFillColor(58, 134, 106); // Primary green color
      pdf.rect(0, 0, pageWidth, 45, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(26);
      pdf.setFont('helvetica', 'bold');
      pdf.text('SITE ASSESSMENT QUOTATION', pageWidth / 2, 30, { align: 'center' });

      yPosition = 60;
      pdf.setTextColor(0, 0, 0);

      // Company Information Section with better spacing
      pdf.setFillColor(248, 249, 250); // Light gray background
      pdf.rect(margin, yPosition, (pageWidth - 2 * margin) * 0.6, 35, 'F');

      yPosition += 8;
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(58, 134, 106); // Green color for company name
      pdf.text('Eco Assets Manager PTY LTD', margin + 5, yPosition);
      yPosition += 8;

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      pdf.text('ABN 97 ***********', margin + 5, yPosition);
      yPosition += 5;
      pdf.text('3/36 Zakwell Court, Coolaroo 3048 VIC', margin + 5, yPosition);
      yPosition += 5;
      pdf.text('Phone: 1300 5333 733', margin + 5, yPosition);

      // Quotation Number and Date Section (right side)
      const rightSectionX = margin + (pageWidth - 2 * margin) * 0.65;
      const rightSectionWidth = (pageWidth - 2 * margin) * 0.35;

      pdf.setFillColor(58, 134, 106); // Green background for quotation info
      pdf.rect(rightSectionX, 60, rightSectionWidth, 35, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.text('QUOTATION', rightSectionX + rightSectionWidth / 2, 72, { align: 'center' });

      pdf.setFontSize(16);
      pdf.text(`EAM ${data?.vno || 'N/A'}`, rightSectionX + rightSectionWidth / 2, 82, { align: 'center' });

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Date: ${dayjs().format('DD/MM/YYYY')}`, rightSectionX + rightSectionWidth / 2, 90, { align: 'center' });

      yPosition = 110;

      // Client Information with modern card design
      pdf.setTextColor(0, 0, 0);

      // Add border and shadow effect
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 40);

      // Header section for client info
      pdf.setFillColor(58, 134, 106); // Green header
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 10, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('CLIENT INFORMATION', margin + 5, yPosition + 7);

      yPosition += 15;
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');

      // Two column layout for client info
      const leftColX = margin + 5;
      const rightColX = margin + (pageWidth - 2 * margin) / 2;

      pdf.setFont('helvetica', 'bold');
      pdf.text('Name:', leftColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(data?.clientTitle || 'N/A', leftColX + 25, yPosition);

      pdf.setFont('helvetica', 'bold');
      pdf.text('Phone:', rightColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(data?.phoneNumber || 'N/A', rightColX + 25, yPosition);

      yPosition += 7;
      pdf.setFont('helvetica', 'bold');
      pdf.text('Email:', leftColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(data?.email || 'N/A', leftColX + 25, yPosition);

      yPosition += 7;
      pdf.setFont('helvetica', 'bold');
      pdf.text('Address:', leftColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      const addressLines = pdf.splitTextToSize(data?.address || 'N/A', pageWidth - 2 * margin - 35);
      pdf.text(addressLines, leftColX + 25, yPosition);

      yPosition += 20;

      // Assessment Details with modern design
      checkPageBreak(40);

      // Add border and shadow effect
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 25);

      // Header section for assessment details
      pdf.setFillColor(58, 134, 106); // Green header
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('ASSESSMENT DETAILS', margin + 5, yPosition + 6);

      yPosition += 13;
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(10);

      // Two column layout for assessment details
      const assessLeftColX = margin + 5;
      const assessRightColX = margin + (pageWidth - 2 * margin) / 2;

      pdf.setFont('helvetica', 'bold');
      pdf.text('Assessor:', assessLeftColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(data?.assignerTitle || 'N/A', assessLeftColX + 30, yPosition);

      pdf.setFont('helvetica', 'bold');
      pdf.text('Assessment Date:', assessRightColX, yPosition);
      pdf.setFont('helvetica', 'normal');
      pdf.text(data?.assessmentTime ? dayjs(data.assessmentTime).format('DD/MM/YYYY HH:mm') : 'N/A', assessRightColX + 45, yPosition);

      yPosition += 20;

      // Items Table with modern design
      checkPageBreak(60);

      // Section header with icon-like design
      pdf.setFillColor(248, 249, 250);
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 12, 'F');

      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(58, 134, 106);
      pdf.text('QUOTATION ITEMS', margin + 5, yPosition + 8);
      yPosition += 15;

      // Modern table design with better spacing
      const colWidths = [85, 25, 30, 30]; // Description, Qty, Rate, Total
      const colPositions = [margin, margin + colWidths[0], margin + colWidths[0] + colWidths[1], margin + colWidths[0] + colWidths[1] + colWidths[2]];

      // Table border
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 10);

      // Gradient-style header
      pdf.setFillColor(58, 134, 106); // Green header
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 10, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'bold');
      pdf.text('DESCRIPTION', colPositions[0] + 3, yPosition + 7);
      pdf.text('QTY', colPositions[1] + 3, yPosition + 7);
      pdf.text('RATE', colPositions[2] + 3, yPosition + 7);
      pdf.text('TOTAL', colPositions[3] + 3, yPosition + 7);

      yPosition += 10;
      pdf.setTextColor(0, 0, 0);

      // Table Rows with enhanced styling
      if (data?.items && data.items.length > 0) {
        data.items.forEach((item, index) => {
          checkPageBreak(10);

          // Alternating row colors with subtle styling
          const rowColor = index % 2 === 0 ? [255, 255, 255] : [248, 250, 252];
          pdf.setFillColor(...rowColor);
          pdf.rect(margin, yPosition, pageWidth - 2 * margin, 10, 'F');

          // Add subtle row borders
          pdf.setDrawColor(230, 230, 230);
          pdf.setLineWidth(0.2);
          pdf.line(margin, yPosition + 10, pageWidth - margin, yPosition + 10);

          pdf.setFontSize(10);
          pdf.setFont('helvetica', 'normal');
          pdf.setTextColor(60, 60, 60);

          // Description (with word wrap if needed)
          const description = item?.Item_Title || 'N/A';
          const descLines = pdf.splitTextToSize(description, colWidths[0] - 6);
          pdf.text(descLines[0], colPositions[0] + 3, yPosition + 7);

          // Center align numbers
          const qty = (parseFloat(item?.Qty) || 0).toFixed(0);
          const rate = `$${(parseFloat(item?.Rate) || 0).toFixed(2)}`;
          const total = `$${(parseFloat(item?.Total) || 0).toFixed(2)}`;

          pdf.text(qty, colPositions[1] + colWidths[1]/2, yPosition + 7, { align: 'center' });
          pdf.text(rate, colPositions[2] + colWidths[2]/2, yPosition + 7, { align: 'center' });

          // Highlight total in green
          pdf.setTextColor(58, 134, 106);
          pdf.setFont('helvetica', 'bold');
          pdf.text(total, colPositions[3] + colWidths[3]/2, yPosition + 7, { align: 'center' });

          pdf.setTextColor(60, 60, 60);
          pdf.setFont('helvetica', 'normal');

          yPosition += 10;
        });
      }

      // Side by side layout: Terms & Conditions (50%) and Totals (50%)
      checkPageBreak(60);
      yPosition += 15;

      const sectionWidth = (pageWidth - 2 * margin - 10) / 2; // 50% each with 10mm gap
      const termsStartX = margin;
      const totalsStartX = margin + sectionWidth + 10;
      const sectionStartY = yPosition;

      // Terms and Conditions Section (Left 50%)
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(termsStartX, yPosition, sectionWidth, 50);

      // Terms header
      pdf.setFillColor(58, 134, 106); // Green header
      pdf.rect(termsStartX, yPosition, sectionWidth, 8, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'bold');
      pdf.text('TERMS & CONDITIONS', termsStartX + 5, yPosition + 6);

      yPosition += 12;
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');

      const terms = [
        '• Please deposit 10% and balance must be paid on installation day.',
        '• Additional charges may apply subject to installer\'s assessment.',
        '• This quotation is valid for 30 days from the date of issue.',
        '• All prices include GST unless otherwise stated.',
        '• Work to be completed as per Australian Standards.',
        '• Customer to provide safe access to work areas.'
      ];

      terms.forEach(term => {
        const termLines = pdf.splitTextToSize(term, sectionWidth - 10);
        pdf.text(termLines, termsStartX + 5, yPosition);
        yPosition += termLines.length * 3 + 1;
      });

      // Totals Section (Right 50%)
      yPosition = sectionStartY; // Reset to same starting position

      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(totalsStartX, yPosition, sectionWidth, 50);

      // Totals header
      pdf.setFillColor(58, 134, 106); // Green header
      pdf.rect(totalsStartX, yPosition, sectionWidth, 8, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'bold');
      pdf.text('FINANCIAL SUMMARY', totalsStartX + 5, yPosition + 6);

      yPosition += 15;
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');

      // Financial details with correct calculations
      const subtotal = parseFloat(data?.totalAmount) || 0;
      const taxRate = (parseFloat(data?.salesTaxR) || 0) + (parseFloat(data?.salesTaxA) || 0);
      const taxAmount = parseFloat(data?.sTaxAmount) || 0;
      const netAmountBeforeDiscount = subtotal + taxAmount;
      const discountAmount = parseFloat(data?.discountAmount) || 0;
      const freight = parseFloat(data?.freight) || 0;

      // Calculate the correct final total: (Subtotal + Tax + Freight - Discount)
      const finalTotal = netAmountBeforeDiscount + freight - discountAmount;

      const financialItems = [
        { label: 'Subtotal:', value: `$${subtotal.toFixed(2)}` },
        { label: `Tax (${taxRate.toFixed(1)}%):`, value: `$${taxAmount.toFixed(2)}` },
        { label: 'VEEC Discount:', value: `-$${discountAmount.toFixed(2)}` },
        { label: 'Freight:', value: `$${freight.toFixed(2)}` }
      ];

      financialItems.forEach(item => {
        pdf.text(item.label, totalsStartX + 5, yPosition);
        pdf.text(item.value, totalsStartX + sectionWidth - 5, yPosition, { align: 'right' });
        yPosition += 6;
      });

      // Final total with emphasis
      yPosition += 3;
      pdf.setDrawColor(58, 134, 106);
      pdf.setLineWidth(1);
      pdf.line(totalsStartX + 5, yPosition, totalsStartX + sectionWidth - 5, yPosition);
      yPosition += 5;

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(58, 134, 106);
      pdf.text('TOTAL:', totalsStartX + 5, yPosition);
      pdf.text(`$${finalTotal.toFixed(2)}`, totalsStartX + sectionWidth - 5, yPosition, { align: 'right' });

      yPosition = sectionStartY + 60; // Move past both sections

      // Signature Section with centered layout
      checkPageBreak(50);
      yPosition += 20;

      // Create two signature areas side by side
      const signatureWidth = (pageWidth - 2 * margin - 20) / 2;
      const leftSignatureX = margin;
      const rightSignatureX = margin + signatureWidth + 20;

      // Customer Signature (Left)
      if (data?.signature) {
        try {
          // Center the signature image
          const signatureImageWidth = 50;
          const signatureImageHeight = 25;
          const signatureImageX = leftSignatureX + (signatureWidth - signatureImageWidth) / 2;
          pdf.addImage(data.signature, 'PNG', signatureImageX, yPosition, signatureImageWidth, signatureImageHeight);
        } catch (error) {
          console.warn('Could not add signature to PDF:', error);
        }
      }

      yPosition += 30;

      // Customer signature line (centered)
      const customerLineWidth = 80;
      const customerLineX = leftSignatureX + (signatureWidth - customerLineWidth) / 2;
      pdf.setDrawColor(0, 0, 0);
      pdf.setLineWidth(0.5);
      pdf.line(customerLineX, yPosition, customerLineX + customerLineWidth, yPosition);

      yPosition += 8;
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(0, 0, 0);
      // Center the "Customer Signature" text
      pdf.text('Customer Signature', leftSignatureX + signatureWidth / 2, yPosition, { align: 'center' });

      // Date section (Right)
      yPosition -= 8; // Reset to line level

      // Show the actual date above the line (centered)
      pdf.text(dayjs().format('DD/MM/YYYY'), rightSignatureX + signatureWidth / 2, yPosition - 3, { align: 'center' });

      // Date line (centered)
      const dateLineWidth = 60;
      const dateLineX = rightSignatureX + (signatureWidth - dateLineWidth) / 2;
      pdf.line(dateLineX, yPosition, dateLineX + dateLineWidth, yPosition);

      yPosition += 8;
      // Show "Date" label below the line
      pdf.text('Date', rightSignatureX + signatureWidth / 2, yPosition, { align: 'center' });

      // Add a professional footer
      yPosition += 20;
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(128, 128, 128);
      pdf.text('Thank you for choosing Eco Assets Manager PTY LTD', pageWidth / 2, yPosition, { align: 'center' });
      pdf.text('For any queries, please contact us at 1300 5333 733', pageWidth / 2, yPosition + 5, { align: 'center' });

      // Generate PDF blob with proper filename
      const pdfBlob = pdf.output('blob');

      // Add filename property to the blob for proper extension recognition
      const filename = `Assessment_Report_EAM_${data?.vno || 'N/A'}_${dayjs().format('YYYY-MM-DD')}.pdf`;

      // Create a new blob with the filename property
      const namedPdfBlob = new File([pdfBlob], filename, {
        type: 'application/pdf',
        lastModified: Date.now()
      });

      if (onPDFGenerated) {
        onPDFGenerated(namedPdfBlob);
      }

      // Also trigger download for user to see the PDF
      const downloadUrl = URL.createObjectURL(namedPdfBlob);
      const downloadLink = document.createElement('a');
      downloadLink.href = downloadUrl;
      downloadLink.download = filename;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadUrl);

      toast({
        title: "PDF Generated Successfully",
        description: "The assessment report has been generated as a PDF.",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top-right"
      });

      onClose();
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error Generating PDF",
        description: "There was an error creating the PDF report.",
        status: "error",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Generate Assessment Report</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4}>
            <Text>
              This will generate a professional PDF report for the site assessment quotation.
            </Text>
            <Text fontSize="sm" color="gray.600">
              The PDF will include all quotation details, items, pricing, and signature.
            </Text>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} onClick={generatePDF}>
            Generate PDF
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AssessorPDFGenerator;
