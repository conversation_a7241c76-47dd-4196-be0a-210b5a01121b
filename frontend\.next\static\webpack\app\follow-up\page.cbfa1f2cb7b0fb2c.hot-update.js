"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/AssesserFollowUp.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _src_components_sidebar_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @src/components/sidebar/sidebar */ \"(app-pages-browser)/./src/components/sidebar/sidebar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @components/Custom/ComboBox/ComboBox */ \"(app-pages-browser)/./src/components/Custom/ComboBox/ComboBox.jsx\");\n/* harmony import */ var _components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @components/Custom/TableComboBox/TableComboBox */ \"(app-pages-browser)/./src/components/Custom/TableComboBox/TableComboBox.jsx\");\n/* harmony import */ var _components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @components/Custom/AanzaDataTable/AanzaDataTable */ \"(app-pages-browser)/./src/components/Custom/AanzaDataTable/AanzaDataTable.jsx\");\n/* harmony import */ var _axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @components/Toolbar/Toolbar */ \"(app-pages-browser)/./src/components/Toolbar/Toolbar.jsx\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @utils/functions */ \"(app-pages-browser)/./src/app/utils/functions.js\");\n/* harmony import */ var _utils_constant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @utils/constant */ \"(app-pages-browser)/./src/app/utils/constant.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @components/Custom/SignaturePad/SignaturePad */ \"(app-pages-browser)/./src/components/Custom/SignaturePad/SignaturePad.jsx\");\n/* harmony import */ var _components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @components/Custom/MultipleImageUploader/MultipleImageUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleImageUploader/MultipleImageUploader.jsx\");\n/* harmony import */ var _components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @components/Custom/MultipleAudioUploader/MultipleAudioUploader */ \"(app-pages-browser)/./src/components/Custom/MultipleAudioUploader/MultipleAudioUploader.jsx\");\n/* harmony import */ var _components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @components/Custom/NumberInput/NumberInput */ \"(app-pages-browser)/./src/components/Custom/NumberInput/NumberInput.jsx\");\n/* harmony import */ var _components_PrintModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @components/PrintModal */ \"(app-pages-browser)/./src/components/PrintModal.jsx\");\n/* harmony import */ var _ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./ComponentToPrint */ \"(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst QuotationFollowupRequiredFields = [];\nconst QuotationFollowupItemsRequiredFields = [];\nconst QuotationFollowupHeaders = [\n    {\n        label: \"Item ID\",\n        key: \"Item_ID\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Item Description\",\n        key: \"Item_Title\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    },\n    {\n        label: \"Qty\",\n        key: \"Qty\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Rate\",\n        key: \"Rate\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Disc %\",\n        key: \"Disc\",\n        width: \"100px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Installation Charges\",\n        key: \"InstallationCharges\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"number\"\n    },\n    {\n        label: \"Total\",\n        key: \"Total\",\n        width: \"100px\",\n        isReadOnly: true,\n        type: \"number\"\n    },\n    {\n        label: \"Delivery Date\",\n        key: \"Date\",\n        width: \"200px\",\n        isReadOnly: false,\n        type: \"date\"\n    },\n    {\n        label: \"Details\",\n        key: \"Details\",\n        width: \"350px\",\n        isReadOnly: false,\n        type: \"text\"\n    }\n];\nconst createQuotationFollowupEmptyTableRow = ()=>[\n        {\n            Item_ID: \"\",\n            Item_Title: \"\",\n            Qty: 0,\n            Rate: 0,\n            InstallationCharges: 0,\n            Disc: 0,\n            Total: 0,\n            Date: \"\",\n            Details: \"\"\n        }\n    ];\nconst createQuotationFollowupInitialFormData = ()=>({\n        location: \"\",\n        vno: \"\",\n        vtp: \"\",\n        voucherNo: \"\",\n        date: \"\",\n        mnth: \"\",\n        tenderNo: \"\",\n        exchangeRate: 0,\n        partyRef: \"\",\n        attentionPerson: \"\",\n        designation: \"\",\n        contactPerson: \"\",\n        email: \"\",\n        phoneNumber: \"\",\n        landline: \"\",\n        address: \"\",\n        lead: \"\",\n        clientId: \"\",\n        clientTitle: \"\",\n        currency: \"\",\n        subject: \"Quotation for Requested Items\",\n        quotation: \"Please find below the quotation for the items you inquired about. The prices mentioned are inclusive of all applicable charges. Let us know if you need any adjustments or have further queries.\",\n        totalAmount: 0,\n        freight: 0,\n        netAmount: 0,\n        validityDays: 0,\n        paymentTerms: \"\",\n        narration: \"\",\n        salesTaxR: \"\",\n        salesTaxA: \"\",\n        discountPercent: \"\",\n        discountAmount: 0,\n        netPayableAmt: 0,\n        sTaxAmount: 0,\n        assignerId: \"\",\n        assignerTitle: \"\",\n        assessmentTime: \"\",\n        assignerLocation: \"\"\n    });\nconst QuotationFollowupCalculationFields = [\n    \"salesTaxR\",\n    \"salesTaxA\",\n    \"netAmount\",\n    \"discountPercent\",\n    \"discountAmount\",\n    \"netPayableAmt\",\n    \"sTaxAmount\",\n    \"totalAmount\",\n    \"freight\"\n];\nconst AssesserFollowUp = ()=>{\n    _s();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const [isDisabled, setIsDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tableData, setTableData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupEmptyTableRow());\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(createQuotationFollowupInitialFormData());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [printData, setPrintData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [audios, setAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedImages, setLoadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loadedAudios, setLoadedAudios] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isPrintModalOpen, setPrintModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handlePrint = ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const data = {\n            ...formData,\n            items: tableData,\n            signature\n        };\n        setPrintData(data);\n        setPrintModalOpen(true);\n    };\n    const handleGeneratePDFFromComponent = async (pdfBlob)=>{\n        try {\n            // Ensure the component is properly rendered and attached to DOM\n            if (!pdfBlob) {\n                throw new Error(\"No PDF blob provided\");\n            }\n            const filename = \"Assessment_Report_\".concat((formData === null || formData === void 0 ? void 0 : formData.location) || \"EAM\", \"_\").concat((formData === null || formData === void 0 ? void 0 : formData.vno) || \"N/A\", \"_\").concat(dayjs__WEBPACK_IMPORTED_MODULE_13___default()().format(\"YYYY-MM-DD\"), \".pdf\");\n            const namedPdfBlob = new File([\n                pdfBlob\n            ], filename, {\n                type: \"application/pdf\",\n                lastModified: Date.now()\n            });\n            // Download PDF\n            const downloadUrl = URL.createObjectURL(namedPdfBlob);\n            const downloadLink = document.createElement(\"a\");\n            downloadLink.href = downloadUrl;\n            downloadLink.download = filename;\n            document.body.appendChild(downloadLink);\n            downloadLink.click();\n            document.body.removeChild(downloadLink);\n            URL.revokeObjectURL(downloadUrl);\n            // Send PDF via email\n            await handleSendPdf(namedPdfBlob);\n            // Save the form\n            // await handleSave();\n            toast({\n                title: \"Process completed successfully\",\n                description: \"PDF generated, emailed, and form saved.\",\n                status: \"success\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n            // Close the modal after successful completion\n            setPrintModalOpen(false);\n        } catch (error) {\n            console.error(\"Error in PDF process:\", error);\n            toast({\n                title: \"Error in PDF process\",\n                description: error.message || \"There was an error during the PDF generation process.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        }\n    };\n    const handleSendPdf = async (pdfBlob)=>{\n        try {\n            if (formData.email && formData.subject && formData.clientTitle && formData.quotation) {\n                const newFormData = new FormData();\n                newFormData.append(\"pdf\", pdfBlob);\n                newFormData.append(\"to\", \"<EMAIL>\");\n                newFormData.append(\"subject\", formData.subject);\n                newFormData.append(\"clientName\", formData.clientTitle);\n                newFormData.append(\"quotation\", formData.quotation);\n                await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"email/quotation-email-with-file\", newFormData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Error sending PDF:\", error);\n            toast({\n                title: \"Error sending PDF.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleInputChange = (singleInput, bulkInput)=>{\n        if (singleInput) {\n            let { name, value } = singleInput.target || singleInput;\n            if (QuotationFollowupCalculationFields.includes(name)) {\n                setFormData((prev)=>{\n                    const salesTaxR = name === \"salesTaxR\" ? Number(value) : Number(prev.salesTaxR);\n                    const salesTaxA = name === \"salesTaxA\" ? Number(value) : Number(prev.salesTaxA);\n                    const freight = name === \"freight\" ? Number(value) : Number(prev.freight);\n                    const discountPercent = name === \"discountPercent\" ? value : prev.discountPercent;\n                    let discountAmount = name === \"discountAmount\" ? value : prev.discountAmount;\n                    const totalAmount = prev.totalAmount;\n                    let sTaxAmount = prev.sTaxAmount;\n                    let netAmount = prev.netAmount;\n                    if (salesTaxR + salesTaxA > 100) {\n                        sTaxAmount = 0;\n                    } else {\n                        const totalPercentage = (salesTaxR + salesTaxA) / 100;\n                        sTaxAmount = totalAmount * totalPercentage;\n                    }\n                    if (name !== \"netAmount\") {\n                        netAmount = totalAmount + sTaxAmount;\n                    }\n                    discountAmount = discountPercent / 100 * netAmount;\n                    const netPayableAmt = netAmount + freight - discountAmount;\n                    return {\n                        ...prev,\n                        [name]: value,\n                        salesTaxR,\n                        salesTaxA,\n                        sTaxAmount,\n                        discountAmount,\n                        totalAmount,\n                        netAmount,\n                        netPayableAmt\n                    };\n                });\n            } else {\n                setFormData((prev)=>({\n                        ...prev,\n                        [name]: value\n                    }));\n            }\n        } else if (bulkInput) {\n            setFormData((prev)=>({\n                    ...prev,\n                    ...bulkInput\n                }));\n        }\n    };\n    const transformData = function() {\n        let orderData = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, itemsArray = arguments.length > 1 ? arguments[1] : void 0, isNavigationdata = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (isNavigationdata) {\n            return itemsArray.map((item)=>{\n                return {\n                    Item_ID: item === null || item === void 0 ? void 0 : item.item_id,\n                    Item_Title: item === null || item === void 0 ? void 0 : item.itemTitle,\n                    Rate: Number(item === null || item === void 0 ? void 0 : item.Rate),\n                    InstallationCharges: Number(item === null || item === void 0 ? void 0 : item.InstallationCharges),\n                    Disc: Number(item === null || item === void 0 ? void 0 : item.Discount),\n                    Total: Number(item === null || item === void 0 ? void 0 : item.Total),\n                    Date: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(item === null || item === void 0 ? void 0 : item.VisitDate), true),\n                    Details: item === null || item === void 0 ? void 0 : item.details,\n                    Qty: Number(item === null || item === void 0 ? void 0 : item.Qty)\n                };\n            });\n        } else {\n            return itemsArray.map((item, index)=>{\n                return {\n                    Dated: orderData.Dated,\n                    VTP: orderData.VTP,\n                    Mnth: orderData.Mnth,\n                    Location: orderData.Location,\n                    vno: orderData.vno,\n                    srno: index + 1,\n                    item_id: item.Item_ID,\n                    Rate: Number(item.Rate),\n                    InstallationCharges: Number(item.InstallationCharges),\n                    Discount: Number(item.Disc),\n                    Total: Number(item.Total),\n                    VisitDate: item.Date,\n                    details: item.Details,\n                    Qty: Number(item.Qty)\n                };\n            });\n        }\n    };\n    const rowClickHandler = (data, rowIndex, colIndex)=>{\n        const isExist = tableData.find((modal)=>modal.Item_ID === data.Item_ID);\n        if (isExist) {\n            setTableData((prev)=>{\n                const updatedTableData = prev.map((item)=>{\n                    if (item.Item_ID === isExist.Item_ID) {\n                        return {\n                            ...item,\n                            qty: item.qty ? Number(item.qty) + 1 : 1\n                        };\n                    }\n                    return item;\n                });\n                return updatedTableData;\n            });\n        } else {\n            setTableData((prev)=>{\n                const updatedTableData = [\n                    ...prev\n                ];\n                updatedTableData[rowIndex] = {\n                    ...updatedTableData[rowIndex],\n                    Item_ID: data.Item_ID ? data.Item_ID : \"\",\n                    Item_Title: data.Item_Title ? data.Item_Title : \"\",\n                    Rate: data.Item_Sale_Rate ? data.Item_Sale_Rate : 0,\n                    Details: data.Item_Details ? data.Item_Details : \"\"\n                };\n                return updatedTableData;\n            });\n        }\n    };\n    const handleImagesChange = (newImages)=>{\n        setImages(newImages);\n    };\n    const handleAudiosChange = (newAudios)=>{\n        setAudios(newAudios);\n    };\n    const cellRender = (value, key, rowIndex, colIndex, cellData, handleInputChange)=>{\n        if ([\n            \"Item_ID\",\n            \"Item_Title\"\n        ].includes(key)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_TableComboBox_TableComboBox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                rowIndex: rowIndex,\n                colIndex: colIndex,\n                inputWidth: cellData.width,\n                value: value,\n                onChange: (val)=>handleInputChange(val),\n                modalData: items,\n                modalHeaders: [\n                    \"ID\",\n                    \"Title\",\n                    \"Details\",\n                    \"Sale_Rate\"\n                ],\n                isDisabled: isDisabled,\n                rowClickHandler: rowClickHandler\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 470,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (cellData.type === \"number\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                width: cellData.width,\n                value: value,\n                onChange: (e)=>handleInputChange(e.target.value),\n                size: \"sm\",\n                isReadOnly: cellData.isReadOnly,\n                isDisabled: isDisabled\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n            width: cellData.width,\n            value: value,\n            onChange: (e)=>handleInputChange(e.target.value),\n            size: \"sm\",\n            type: cellData.type,\n            isReadOnly: cellData.isReadOnly,\n            isDisabled: isDisabled\n        }, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, undefined);\n    };\n    const calculation = (header, value, rowIndex)=>{\n        setTableData((prevData)=>{\n            return prevData.map((r, i)=>{\n                if (i === rowIndex) {\n                    const updatedRow = {\n                        ...r,\n                        [header.key]: value\n                    };\n                    const qty = header.key === \"Qty\" ? value : r.Qty;\n                    const rate = header.key === \"Rate\" ? value : r.Rate;\n                    const installationCharges = header.key === \"InstallationCharges\" ? value : r.InstallationCharges;\n                    const discountPercent = header.key === \"Disc\" ? value : r.Disc;\n                    const total = (Number(qty) || 0) * (Number(rate) || 0);\n                    const discountAmount = discountPercent / 100 * total;\n                    updatedRow.Total = total - discountAmount + (Number(installationCharges) || 0);\n                    return updatedRow;\n                }\n                return r;\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let totalQty = 0;\n        let totalAmount = 0;\n        tableData.forEach((data)=>{\n            totalAmount += Number(data.Total) || 0;\n        });\n        const salesTaxR = formData.salesTaxR;\n        const salesTaxA = formData.salesTaxA;\n        const freight = formData.freight;\n        const discountPercent = formData.discountPercent;\n        let discountAmount = formData.discountAmount;\n        let sTaxAmount = formData.sTaxAmount;\n        let netAmount = formData.netAmount;\n        if (salesTaxR + salesTaxA > 100) {\n            sTaxAmount = 0;\n        } else {\n            const totalPercentage = (salesTaxR + salesTaxA) / 100;\n            sTaxAmount = totalAmount * totalPercentage;\n        }\n        discountAmount = discountPercent / 100 * netAmount;\n        const netPayableAmt = netAmount + freight - discountAmount;\n        setFormData((prev)=>({\n                ...prev,\n                totalQty,\n                totalAmount,\n                netAmount,\n                netPayableAmt,\n                sTaxAmount,\n                discountAmount\n            }));\n    }, [\n        tableData\n    ]);\n    const getVoucherNo = async (date)=>{\n        if (date) {\n            setLoading(true);\n            const year = new Date(date).getFullYear();\n            try {\n                const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/getVoucherNo\", {\n                    Mnth: year.toString()\n                });\n                const { location, vno, vtp, Mnth, voucherNo } = response.data || {};\n                if (location, vno, vtp, Mnth, voucherNo) {\n                    setFormData((prevFormData)=>({\n                            ...prevFormData,\n                            location,\n                            vno,\n                            vtp,\n                            Mnth,\n                            voucherNo,\n                            mnth: Mnth\n                        }));\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Error fetching voucher number:\", error);\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please Select a Date First.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const navigateVoucherForm = async (voucherNo)=>{\n        setLoading(true);\n        setIsDisabled(true);\n        try {\n            const response = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/navigate\", {\n                goto: true,\n                voucher_no: voucherNo\n            });\n            const fileResponse = await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"offer/get-files\", {\n                params: {\n                    refVoucherNo: voucherNo\n                }\n            });\n            // Process files by type\n            const allFiles = fileResponse.data;\n            const images = allFiles.filter((file)=>file.Type === \"image\" && file.FileName !== \"signature.png\");\n            const audios = allFiles.filter((file)=>file.Type === \"audio\");\n            // Find signature if it exists\n            const signatureFile = allFiles.find((file)=>file.FileName === \"signature.png\");\n            if (signatureFile) {\n                // Convert buffer to base64 string for signature pad\n                const buffer = Buffer.from(signatureFile.FileData.data);\n                const base64String = \"data:image/png;base64,\".concat(buffer.toString(\"base64\"));\n                setSignature(base64String);\n            }\n            setLoadedAudios(audios);\n            setLoadedImages(images);\n            const resData = response.data;\n            const { items } = response.data;\n            var _resData_client_id, _resData_ClientName, _resData_EmployeeID, _resData_EmployeeName, _resData_AssessmentLocation, _resData_SalesTaxA, _resData_SalesTaxR, _resData_Discount, _resData_DiscountPercent, _resData_GrossAmount, _resData_Lead, _resData_TenderNo, _resData_AttentionPerson, _resData_AttentionPerson_Desig, _resData_ContactPerson, _resData_Currency_ID, _resData_Subject, _resData_Quotation, _resData_GrdTotalPSTAmt, _resData_Freight, _resData_Validity, _resData_Terms, _resData_NetAmount, _resData_StartingComments;\n            const form = {\n                date: (resData === null || resData === void 0 ? void 0 : resData.Dated) ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date(resData === null || resData === void 0 ? void 0 : resData.Dated), true) : null,\n                vtp: resData === null || resData === void 0 ? void 0 : resData.VTP,\n                mnth: resData === null || resData === void 0 ? void 0 : resData.Mnth,\n                location: resData === null || resData === void 0 ? void 0 : resData.Location,\n                vno: resData === null || resData === void 0 ? void 0 : resData.vno,\n                voucherNo: resData === null || resData === void 0 ? void 0 : resData.Voucher_No,\n                clientId: (_resData_client_id = resData === null || resData === void 0 ? void 0 : resData.client_id) !== null && _resData_client_id !== void 0 ? _resData_client_id : \"\",\n                clientTitle: (_resData_ClientName = resData === null || resData === void 0 ? void 0 : resData.ClientName) !== null && _resData_ClientName !== void 0 ? _resData_ClientName : \"\",\n                assignerId: (_resData_EmployeeID = resData === null || resData === void 0 ? void 0 : resData.EmployeeID) !== null && _resData_EmployeeID !== void 0 ? _resData_EmployeeID : \"\",\n                assignerTitle: (_resData_EmployeeName = resData === null || resData === void 0 ? void 0 : resData.EmployeeName) !== null && _resData_EmployeeName !== void 0 ? _resData_EmployeeName : \"\",\n                assessmentTime: (resData === null || resData === void 0 ? void 0 : resData.AssessmentTime) ? dayjs__WEBPACK_IMPORTED_MODULE_13___default()(resData === null || resData === void 0 ? void 0 : resData.AssessmentTime).format(\"YYYY-MM-DDTHH:mm\") : \"\",\n                assignerLocation: (_resData_AssessmentLocation = resData === null || resData === void 0 ? void 0 : resData.AssessmentLocation) !== null && _resData_AssessmentLocation !== void 0 ? _resData_AssessmentLocation : \"\",\n                salesTaxA: (_resData_SalesTaxA = resData === null || resData === void 0 ? void 0 : resData.SalesTaxA) !== null && _resData_SalesTaxA !== void 0 ? _resData_SalesTaxA : 0,\n                salesTaxR: (_resData_SalesTaxR = resData === null || resData === void 0 ? void 0 : resData.SalesTaxR) !== null && _resData_SalesTaxR !== void 0 ? _resData_SalesTaxR : 0,\n                discountAmount: (_resData_Discount = resData === null || resData === void 0 ? void 0 : resData.Discount) !== null && _resData_Discount !== void 0 ? _resData_Discount : 0,\n                discountPercent: (_resData_DiscountPercent = resData === null || resData === void 0 ? void 0 : resData.DiscountPercent) !== null && _resData_DiscountPercent !== void 0 ? _resData_DiscountPercent : 0,\n                netPayableAmt: (_resData_GrossAmount = resData === null || resData === void 0 ? void 0 : resData.GrossAmount) !== null && _resData_GrossAmount !== void 0 ? _resData_GrossAmount : 0,\n                lead: (_resData_Lead = resData === null || resData === void 0 ? void 0 : resData.Lead) !== null && _resData_Lead !== void 0 ? _resData_Lead : \"\",\n                tenderNo: (_resData_TenderNo = resData === null || resData === void 0 ? void 0 : resData.TenderNo) !== null && _resData_TenderNo !== void 0 ? _resData_TenderNo : \"\",\n                // exchangeRate: resData?.ExchRate ?? 0,\n                // partyRef: resData?.Party_ref ?? \"\",\n                attentionPerson: (_resData_AttentionPerson = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson) !== null && _resData_AttentionPerson !== void 0 ? _resData_AttentionPerson : \"\",\n                designation: (_resData_AttentionPerson_Desig = resData === null || resData === void 0 ? void 0 : resData.AttentionPerson_Desig) !== null && _resData_AttentionPerson_Desig !== void 0 ? _resData_AttentionPerson_Desig : \"\",\n                contactPerson: (_resData_ContactPerson = resData === null || resData === void 0 ? void 0 : resData.ContactPerson) !== null && _resData_ContactPerson !== void 0 ? _resData_ContactPerson : \"\",\n                currency: (_resData_Currency_ID = resData === null || resData === void 0 ? void 0 : resData.Currency_ID) !== null && _resData_Currency_ID !== void 0 ? _resData_Currency_ID : \"\",\n                subject: (_resData_Subject = resData === null || resData === void 0 ? void 0 : resData.Subject) !== null && _resData_Subject !== void 0 ? _resData_Subject : \"\",\n                quotation: (_resData_Quotation = resData === null || resData === void 0 ? void 0 : resData.Quotation) !== null && _resData_Quotation !== void 0 ? _resData_Quotation : \"\",\n                totalAmount: (_resData_GrdTotalPSTAmt = resData === null || resData === void 0 ? void 0 : resData.GrdTotalPSTAmt) !== null && _resData_GrdTotalPSTAmt !== void 0 ? _resData_GrdTotalPSTAmt : 0,\n                freight: (_resData_Freight = resData === null || resData === void 0 ? void 0 : resData.Freight) !== null && _resData_Freight !== void 0 ? _resData_Freight : 0,\n                validityDays: (_resData_Validity = resData === null || resData === void 0 ? void 0 : resData.Validity) !== null && _resData_Validity !== void 0 ? _resData_Validity : 0,\n                paymentTerms: (_resData_Terms = resData === null || resData === void 0 ? void 0 : resData.Terms) !== null && _resData_Terms !== void 0 ? _resData_Terms : \"\",\n                netAmount: (_resData_NetAmount = resData === null || resData === void 0 ? void 0 : resData.NetAmount) !== null && _resData_NetAmount !== void 0 ? _resData_NetAmount : 0,\n                narration: (_resData_StartingComments = resData === null || resData === void 0 ? void 0 : resData.StartingComments) !== null && _resData_StartingComments !== void 0 ? _resData_StartingComments : \"\"\n            };\n            setTableData(()=>transformData([], items, true));\n            setFormData(form);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching goto voucher:\", error);\n            setLoading(false);\n            toast({\n                title: \"goto Voucher Fetching Failed !\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const loadInitialData = async ()=>{\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const { data: purposeData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"purpose/\" + purpose_no);\n        if (purposeData.RefVoucherNo) {\n            navigateVoucherForm(purposeData.RefVoucherNo);\n        } else {\n            const newDate = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(dayjs__WEBPACK_IMPORTED_MODULE_13___default()(), true);\n            setFormData((prev)=>({\n                    ...prev,\n                    date: newDate\n                }));\n            getVoucherNo(newDate);\n        }\n        const { data: clientData } = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"client/\" + (purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID));\n        const items = await (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.getData)(\"getRecords/items\");\n        setFormData((prev)=>({\n                ...prev,\n                assignerId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessorID,\n                assessmentTime: purposeData === null || purposeData === void 0 ? void 0 : purposeData.assessmentTime,\n                assignerLocation: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                address: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientAddress,\n                email: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientEmail,\n                phoneNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientMobileNo,\n                landlineNo: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTelephone,\n                clientId: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientID,\n                clientTitle: purposeData === null || purposeData === void 0 ? void 0 : purposeData.clientTitle,\n                contact: purposeData === null || purposeData === void 0 ? void 0 : purposeData.contactPerson,\n                comments: purposeData === null || purposeData === void 0 ? void 0 : purposeData.remarks,\n                ...clientData\n            }));\n        const itemData = [];\n        items.map((item)=>{\n            itemData.push({\n                Item_ID: item.id,\n                Item_Title: item.Title,\n                // Item_Unit: item.Unit,\n                Item_Details: item.Details,\n                Item_Sale_Rate: item.Sale_Rate\n            });\n        });\n        setItems(itemData);\n    };\n    // Toolbar funtions starts here\n    const handleSave = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"Please sign the document first.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return false;\n        }\n        const purpose_no = searchParams.get(\"purpose_no\");\n        const data = {\n            Dated: formData.date ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date) : null,\n            VTP: formData.vtp,\n            Mnth: formData.mnth,\n            Location: formData.location,\n            vno: formData.vno,\n            Voucher_No: formData.voucherNo,\n            Currency_ID: formData.currency,\n            Validity: formData.validityDays,\n            Lead: formData.lead,\n            EmployeeID: formData.assignerId,\n            AssessmentTime: formData.assessmentTime ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.assessmentTime) : null,\n            AssessmentLocation: formData.assignerLocation,\n            TenderNo: formData.tenderNo,\n            SalesTaxA: formData.salesTaxA,\n            SalesTaxR: formData.salesTaxR,\n            DiscountPercent: formData.discountPercent,\n            Discount: formData.discountAmount,\n            GrossAmount: formData.netPayableAmt,\n            ContactPerson: formData.contactPerson,\n            Subject: formData.subject,\n            Quotation: formData.quotation,\n            GrdTotalPSTAmt: formData.totalAmount,\n            prp_id: user.id,\n            Freight: formData.freight,\n            NetAmount: formData.netAmount,\n            Terms: formData.paymentTerms,\n            StartingComments: formData.narration,\n            client_id: formData.clientId,\n            CreationDate: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(new Date()),\n            items: transformData({\n                Dated: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.formatDate)(formData.date),\n                VTP: formData.vtp,\n                Mnth: formData.mnth,\n                Location: formData.location,\n                vno: formData.vno\n            }, tableData)\n        };\n        const isValidateObjectFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateObjectFields)(data, QuotationFollowupRequiredFields);\n        const isValidateArrayFields = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_8__.validateArrayFields)(data.items, QuotationFollowupItemsRequiredFields);\n        if (isValidateObjectFields.error) {\n            toast({\n                title: isValidateObjectFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateArrayFields.error) {\n            toast({\n                title: isValidateArrayFields.error,\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n        if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {\n            setLoading(true);\n            try {\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"post\",\n                    url: \"offer/create\",\n                    data: data\n                });\n                await (0,_axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n                    method: \"put\",\n                    url: \"purpose/update/\" + purpose_no,\n                    data: {\n                        RefVoucherNo: formData.voucherNo,\n                        RefVTP: formData.vtp,\n                        RefMnth: formData.mnth,\n                        RefLocation: formData.location,\n                        RefVNo: formData.vno\n                    }\n                });\n                handleStoreImages();\n                handleStoreAudios();\n                handleStoreSignature();\n                setIsDisabled(true);\n                toast({\n                    title: \"Quotation Followup Form saved successfully :)\",\n                    status: \"success\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error saving Quotation Followup Form:\", error);\n                toast({\n                    title: \"Error saving Quotation Followup Form :(\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                setLoading(false);\n            }\n        } else {\n            toast({\n                title: \"Please fill out all required fields and ensure all items have valid fields\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        }\n    };\n    const handleStoreImages = async ()=>{\n        if (!images || images.length === 0) {\n            toast({\n                title: \"No files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = images.map(async (image)=>{\n                const formData = new FormData();\n                formData.append(\"file\", image.file); // Attach the file\n                formData.append(\"timestamp\", image.timestamp);\n                formData.append(\"location\", JSON.stringify(image.location));\n                formData.append(\"googleMapsLink\", image.googleMapsLink);\n                formData.append(\"type\", \"image\"); // Adjust type if needed (e.g., 'video', 'audio')\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Image \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Image \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading files:\", error);\n            toast({\n                title: \"Error uploading files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreAudios = async ()=>{\n        if (!audios || audios.length === 0) {\n            toast({\n                title: \"No audio files to upload.\",\n                status: \"warning\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            const uploadPromises = audios.map(async (audio)=>{\n                const formData = new FormData();\n                formData.append(\"file\", audio.file); // Attach the file\n                formData.append(\"timestamp\", audio.timestamp);\n                formData.append(\"location\", JSON.stringify(audio.location));\n                formData.append(\"googleMapsLink\", audio.googleMapsLink);\n                formData.append(\"type\", \"audio\"); // Specify type as 'audio'\n                formData.append(\"refVoucherNo\", refVoucherNo);\n                formData.append(\"refVTP\", refVTP);\n                formData.append(\"refMnth\", refMnth);\n                formData.append(\"refLocation\", refLocation);\n                formData.append(\"refVNo\", refVNo);\n                return _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-file\", formData, {\n                    headers: {\n                        \"Content-Type\": \"multipart/form-data\"\n                    }\n                });\n            });\n            const results = await Promise.allSettled(uploadPromises);\n            results.forEach((result, index)=>{\n                if (result.status === \"fulfilled\") {\n                    console.log(\"Audio \".concat(index + 1, \" uploaded successfully\"));\n                } else {\n                    console.error(\"Audio \".concat(index + 1, \" upload failed:\"), result.reason);\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading audio files:\", error);\n            toast({\n                title: \"Error uploading audio files.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStoreSignature = async ()=>{\n        if (!signature) {\n            toast({\n                title: \"No signature to upload\",\n                status: \"info\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            const refVoucherNo = formData.voucherNo;\n            const refVTP = formData.vtp;\n            const refMnth = formData.mnth;\n            const refLocation = formData.location;\n            const refVNo = formData.vno;\n            if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {\n                toast({\n                    title: \"Voucher details are missing. Please generate a voucher first.\",\n                    status: \"error\",\n                    variant: \"left-accent\",\n                    position: \"top-right\",\n                    isClosable: true\n                });\n                return;\n            }\n            setLoading(true);\n            // Convert data URL to a Blob\n            const fetchResponse = await fetch(signature);\n            const signatureBlob = await fetchResponse.blob();\n            // Create a File object\n            const signatureFile = new File([\n                signatureBlob\n            ], \"signature.png\", {\n                type: \"image/png\"\n            });\n            const signatureFormData = new FormData();\n            signatureFormData.append(\"file\", signatureFile);\n            signatureFormData.append(\"timestamp\", new Date().toISOString());\n            signatureFormData.append(\"location\", JSON.stringify({}));\n            signatureFormData.append(\"googleMapsLink\", \"\");\n            signatureFormData.append(\"refVoucherNo\", refVoucherNo);\n            signatureFormData.append(\"refVTP\", refVTP);\n            signatureFormData.append(\"refMnth\", refMnth);\n            signatureFormData.append(\"refLocation\", refLocation);\n            signatureFormData.append(\"refVNo\", refVNo);\n            await _axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"offer/store-signature\", signatureFormData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n        } catch (error) {\n            console.error(\"Error uploading signature:\", error);\n            toast({\n                title: \"Error uploading signature.\",\n                status: \"error\",\n                variant: \"left-accent\",\n                position: \"top-right\",\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Toolbar funtions ends here\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 1103,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"20px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Site Assessment Follow-Up\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        style: {\n                                            gap: \"10px\",\n                                            paddingTop: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            margin: \"0\",\n                                                            textAlign: \"center\",\n                                                            color: \"#2B6CB0\",\n                                                            fontSize: \"20px\",\n                                                            fontWeight: \"bold\"\n                                                        },\n                                                        children: \"SOP's\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"1. Is the property more than 2 years old ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1155,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"2. Is the combined yearly income of the household equal to or less than 180,000 ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1164,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"question\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"3. Are you ready to decommission the ducted gas heated system ?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Select, {\n                                                                name: \"question1\",\n                                                                placeholder: \"Select\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Yes\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1174,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"No\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1173,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1168,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"bgWhite col-md-5 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        flexWrap: {\n                                                            base: \"wrap\",\n                                                            sm: \"wrap\",\n                                                            md: \"wrap\",\n                                                            lg: \"nowrap\"\n                                                        },\n                                                        gap: \"10px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"80%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                className: \"\",\n                                                                sx: {\n                                                                    display: \"grid\",\n                                                                    gridTemplateColumns: {\n                                                                        base: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        sm: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        md: \"repeat(auto-fit,minmax(200px,1fr)) !important\",\n                                                                        lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                                    },\n                                                                    gap: \"5px\"\n                                                                },\n                                                                children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[0].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            flexWrap: {\n                                                                                base: \"wrap\",\n                                                                                sm: \"wrap\",\n                                                                                md: \"wrap\",\n                                                                                lg: \"nowrap\"\n                                                                            },\n                                                                            flexDirection: {\n                                                                                base: \"column\",\n                                                                                sm: \"column\",\n                                                                                md: \"row\"\n                                                                            },\n                                                                            marginTop: \"10px\"\n                                                                        },\n                                                                        isRequired: field.isRequired,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                                htmlFor: field.id,\n                                                                                sx: {\n                                                                                    marginBottom: \"0\",\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"27%\"\n                                                                                    }\n                                                                                },\n                                                                                children: field.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1246,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            field.type === \"date\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                // min={field.minDate}\n                                                                                // max={field.maxDate}\n                                                                                disabled: isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1261,\n                                                                                columnNumber: 37\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                                id: field.id,\n                                                                                name: field.name,\n                                                                                type: field.type,\n                                                                                value: formData[field.value],\n                                                                                onChange: handleInputChange,\n                                                                                placeholder: field.placeholder,\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                readOnly: field.isReadOnly,\n                                                                                disabled: field.name === \"voucherNo\" ? isDisabled : isDisabled,\n                                                                                sx: {\n                                                                                    marginLeft: {\n                                                                                        base: \"0\",\n                                                                                        sm: \"0\",\n                                                                                        lg: \"4px\"\n                                                                                    },\n                                                                                    width: {\n                                                                                        base: \"100%\",\n                                                                                        sm: \"100%\",\n                                                                                        md: \"100%\",\n                                                                                        lg: \"80%\"\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1288,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, field.id, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 33\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1211,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: {\n                                                                    base: \"100%\",\n                                                                    sm: \"100%\",\n                                                                    md: \"100%\",\n                                                                    lg: \"20%\"\n                                                                }\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                                sx: {\n                                                                    height: \"100%\"\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                                    sx: {\n                                                                        width: \"100%\",\n                                                                        height: {\n                                                                            base: \"inherit\",\n                                                                            md: \"inherit\",\n                                                                            lg: \"calc(100% - 10px)\"\n                                                                        },\n                                                                        margin: \"10px 0\"\n                                                                    },\n                                                                    bg: \"#2d6651\",\n                                                                    color: \"white\",\n                                                                    _hover: {\n                                                                        bg: \"#3a866a\"\n                                                                    },\n                                                                    onClick: ()=>getVoucherNo(formData.date),\n                                                                    isDisabled: isDisabled,\n                                                                    children: \"Generate Voucher No\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1333,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1322,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                    className: \"pt-4 pb-4\",\n                                                    sx: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: {\n                                                            base: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            sm: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            md: \"repeat(auto-fit,minmax(200,1fr))\",\n                                                            lg: \"repeat(auto-fit,minmax(500px,1fr))\"\n                                                        },\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: _utils_constant__WEBPACK_IMPORTED_MODULE_9__.RecoveryFollowUpSectionFormFields[1].map((control, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                flexDirection: {\n                                                                    base: \"column\",\n                                                                    sm: \"column\",\n                                                                    lg: \"row\"\n                                                                },\n                                                                marginTop: \"10px\",\n                                                                flexWrap: \"nowrap\"\n                                                            },\n                                                            isRequired: control.isRequired,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                    htmlFor: control.fields[0].name,\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"20%\"\n                                                                        }\n                                                                    },\n                                                                    children: control.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1394,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                    sx: {\n                                                                        width: {\n                                                                            base: \"100%\",\n                                                                            sm: \"100%\",\n                                                                            lg: \"80%\"\n                                                                        },\n                                                                        display: \"flex\",\n                                                                        gap: control.fields.length > 1 ? \"10px\" : \"0\"\n                                                                    },\n                                                                    children: control.fields.map((field, fieldIndex)=>field.component === \"ComboBox\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_ComboBox_ComboBox__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            target: true,\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            inputWidths: field.inputWidths,\n                                                                            buttonWidth: field.buttonWidth,\n                                                                            styleButton: {\n                                                                                padding: \"3px !important\"\n                                                                            },\n                                                                            tableData: [],\n                                                                            tableHeaders: field.tableHeaders,\n                                                                            nameFields: field.nameFields,\n                                                                            placeholders: field.placeholders,\n                                                                            keys: field.keys,\n                                                                            form: formData,\n                                                                            isDisabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1419,\n                                                                            columnNumber: 37\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                            onChange: handleInputChange,\n                                                                            name: field.name,\n                                                                            placeholder: field.placeholder,\n                                                                            value: formData[field.value],\n                                                                            _placeholder: field._placeholder,\n                                                                            type: field.type,\n                                                                            style: {\n                                                                                width: field.inputWidth\n                                                                            },\n                                                                            disabled: true\n                                                                        }, fieldIndex, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 37\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                    lineNumber: 1406,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1354,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_AanzaDataTable_AanzaDataTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    tableData: tableData,\n                                                    setTableData: setTableData,\n                                                    headers: QuotationFollowupHeaders,\n                                                    tableWidth: \"100%\",\n                                                    tableHeight: \"400px\",\n                                                    fontSize: \"lg\",\n                                                    cellRender: cellRender,\n                                                    styleHead: {\n                                                        background: \"#3275bb\",\n                                                        color: \"white !important\"\n                                                    },\n                                                    styleBody: {\n                                                        background: \"white !important\"\n                                                    },\n                                                    calculation: calculation,\n                                                    isDisabled: isDisabled\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleImageUploader_MultipleImageUploader__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            initial: loadedImages,\n                                                            onChange: handleImagesChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1518,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1511,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1496,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(50% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite col-md-7 col-sm-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\",\n                                                            lg: \"row\"\n                                                        },\n                                                        marginTop: \"10px\",\n                                                        flexWrap: \"nowrap\",\n                                                        height: \"100%\"\n                                                    },\n                                                    isRequired: \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                        sx: {\n                                                            width: {\n                                                                base: \"100%\",\n                                                                sm: \"100%\",\n                                                                lg: \"100%\"\n                                                            },\n                                                            display: \"flex\",\n                                                            height: \"100%\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_MultipleAudioUploader_MultipleAudioUploader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            initial: loadedAudios,\n                                                            onChange: handleAudiosChange,\n                                                            disabled: isDisabled\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1559,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                        lineNumber: 1552,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1537,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 pb-2\",\n                                                    style: {\n                                                        display: \"grid\",\n                                                        gridTemplateColumns: \"repeat(auto-fit,minmax(300px,1fr))\",\n                                                        gap: \"5px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Total Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1601,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"totalAmount\",\n                                                                        value: formData.totalAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1612,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1589,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1588,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1643,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxR\",\n                                                                                value: formData.salesTaxR,\n                                                                                placeholder: \"R\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1665,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"salesTaxA\",\n                                                                                value: formData.salesTaxA,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1681,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1654,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1631,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1630,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"S.Tax Amt.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1713,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"sTaxAmount\",\n                                                                        value: formData.sTaxAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1724,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1701,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1700,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1755,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netAmount\",\n                                                                        value: formData.netAmount,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1766,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1743,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1742,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Discount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1797,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            },\n                                                                            display: \"flex\",\n                                                                            gap: 1.5\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountPercent\",\n                                                                                value: formData.discountPercent,\n                                                                                placeholder: \"%\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"30%\",\n                                                                                        sm: \"30%\",\n                                                                                        lg: \"30%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: false,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1819,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                onChange: handleInputChange,\n                                                                                name: \"discountAmount\",\n                                                                                value: formData.discountAmount,\n                                                                                placeholder: \"A\",\n                                                                                _placeholder: {\n                                                                                    color: \"gray.500\"\n                                                                                },\n                                                                                sx: {\n                                                                                    width: {\n                                                                                        base: \"70%\",\n                                                                                        sm: \"70%\",\n                                                                                        lg: \"70%\"\n                                                                                    }\n                                                                                },\n                                                                                isReadOnly: true,\n                                                                                disabled: isDisabled\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                                lineNumber: 1835,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1808,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1785,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1784,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Freight\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1867,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"freight\",\n                                                                        value: formData.freight,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1878,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1855,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1854,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Net Payable Amt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1909,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"netPayableAmt\",\n                                                                        value: formData.netPayableAmt,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: true,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1920,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1897,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1896,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"30%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Validity Days\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1951,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_NumberInput_NumberInput__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"validityDays\",\n                                                                        value: formData.validityDays,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"70%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1962,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1939,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1938,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                            gridColumn: \"1 / -1\",\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"flex-start\",\n                                                                    width: \"100%\",\n                                                                    flexDirection: {\n                                                                        base: \"column\",\n                                                                        sm: \"column\",\n                                                                        lg: \"row\"\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"10%\"\n                                                                            }\n                                                                        },\n                                                                        children: \"Payment Terms\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 1996,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Input, {\n                                                                        onChange: handleInputChange,\n                                                                        name: \"paymentTerms\",\n                                                                        value: formData.paymentTerms,\n                                                                        placeholder: \"\",\n                                                                        _placeholder: {\n                                                                            color: \"gray.500\"\n                                                                        },\n                                                                        type: \"text\",\n                                                                        sx: {\n                                                                            width: {\n                                                                                base: \"100%\",\n                                                                                sm: \"100%\",\n                                                                                lg: \"90%\"\n                                                                            }\n                                                                        },\n                                                                        isReadOnly: false,\n                                                                        disabled: isDisabled\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                        lineNumber: 2007,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 1984,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 1980,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 1579,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 1567,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(75% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Remarks\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Textarea, {\n                                                            _placeholder: {\n                                                                color: \"gray.500\"\n                                                            },\n                                                            resize: \"vertical\",\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                height: \"100%\"\n                                                            },\n                                                            onChange: handleInputChange,\n                                                            name: \"narration\",\n                                                            value: formData.narration,\n                                                            disabled: isDisabled,\n                                                            rows: 10\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2056,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2039,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2028,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                sx: {\n                                                    padding: \"15px\",\n                                                    width: {\n                                                        base: \"100% !important\",\n                                                        sm: \"100%\",\n                                                        lg: \"calc(25% - 5px) !important\"\n                                                    }\n                                                },\n                                                className: \"ClientDIVVV bgWhite\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.FormControl, {\n                                                    sx: {\n                                                        display: \"flex\",\n                                                        marginTop: \"10px\",\n                                                        flexDirection: {\n                                                            base: \"column\",\n                                                            sm: \"column\"\n                                                        }\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.FormLabel, {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: \"Digital Signature\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2092,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Box, {\n                                                            sx: {\n                                                                width: \"100%\",\n                                                                display: \"flex\",\n                                                                flexDirection: \"column\",\n                                                                alignItems: \"center\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Custom_SignaturePad_SignaturePad__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                onSave: (url)=>setSignature(url),\n                                                                onClose: ()=>setSignature(null),\n                                                                initialSignature: signature,\n                                                                isDisabled: isDisabled\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                                lineNumber: 2107,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                            lineNumber: 2099,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                    lineNumber: 2082,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                                lineNumber: 2071,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                                lineNumber: 1109,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                            lineNumber: 1108,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 1107,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 1106,\n                    columnNumber: 11\n                }, undefined),\n                !isDisabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toolbar_Toolbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    save: handlePrint,\n                    hide: [\n                        \"First\",\n                        \"Edit\",\n                        \"Last\",\n                        \"Previous\",\n                        \"Next\",\n                        \"Delete\",\n                        \"Goto\",\n                        \"Cancel\",\n                        \"Clear\",\n                        \"Check\",\n                        \"Print\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2122,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PrintModal__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isOpen: isPrintModalOpen,\n                    onClose: ()=>setPrintModalOpen(false),\n                    formName: \"Site Assessment Quotation\",\n                    showHeader: false,\n                    buttonText: \"Save & Generate PDF\",\n                    callback: handleGeneratePDFFromComponent,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentToPrint__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        data: printData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                        lineNumber: 2147,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n                    lineNumber: 2139,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(AssesserFollowUp, \"Y00UvtCWtYp7LkfYjThmtmHCCrE=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.useToast,\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_12__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = AssesserFollowUp;\nconst AssesserFollowUpPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2156,\n            columnNumber: 23\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AssesserFollowUp, {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n            lineNumber: 2157,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\AssesserFollowUp.jsx\",\n        lineNumber: 2156,\n        columnNumber: 3\n    }, undefined);\n_c1 = AssesserFollowUpPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AssesserFollowUpPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"AssesserFollowUp\");\n$RefreshReg$(_c1, \"AssesserFollowUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/AssesserFollowUp.jsx\n"));

/***/ })

});