"use client";
import React, { useEffect, useState, useRef } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import ConfirmDialog from "@components/Custom/ConfirmDialog/ConfirmDialog";
import GotoDialog from "@components/Custom/GotoDialog/GotoDialog";
import CopyDialog from "@components/Custom/CopyDialog/CopyDialog";
import axiosInstance from "../../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input, Textarea } from "@chakra-ui/react";
import {
  formatDate,
  validateArrayFields,
  validateObjectFields,
} from "@utils/functions";
import {
  ClientOrderHeaders,
  ClientOrderSectionFormFields,
  ClientOrderItemTableHeader,
  ClientOrderCalculationFields,
  createClientOrderInitialFormData,
  createClientOrderEmptyTableRow,
  ClientOrderItemsRequiredFields,
  ClientOrderRequiredFields,
} from "@utils/constant";
import Loader from "@components/Loader/Loader";
import ComponentToPrint from "./ComponentToPrint";
import PrintModal from "@components/PrintModal";
import { useUser } from "@src/app/provider/UserContext";

const ClientOrder = () => {
  const toast = useToast();
  const { user } = useUser();
  const [clients, setClients] = useState([]);
  const [clientLeads, setClientLeads] = useState([]);
  const [salesMan, setSalesMan] = useState([]);
  const [installers, setInstallers] = useState([]);
  const [deliveryMen, setDeliveryMen] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [cash, setCash] = useState([]);
  const [items, setItems] = useState([]);
  const [isNavigation, setIsNavigation] = useState(false);
  const [tableData, setTableData] = useState(createClientOrderEmptyTableRow());
  const [formData, setFormData] = useState(createClientOrderInitialFormData());
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [isGotoDialogOpen, setIsGotoDialogOpen] = useState(false);
  const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false);
  const [printData, setPrintData] = useState({});
  const [isPrintModalOpen, setPrintModalOpen] = useState(false);

  const handlePrint = async () => {
    try {
      const { data } = await axiosInstance.post('clientorders/getbyvoucher', { voucherNo: formData.voucherNo });
      setPrintData(data);
      setPrintModalOpen(true);
    } catch (error) {
      console.error(error);
    }
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;

      if (ClientOrderCalculationFields.includes(name)) {
        setFormData((prev) => {
          const salesTaxR =
            name === "salesTaxR" ? Number(value) : Number(prev.salesTaxR);
          const salesTaxA =
            name === "salesTaxA" ? Number(value) : Number(prev.salesTaxA);
          const freight =
            name === "freight" ? Number(value) : Number(prev.freight);
          const discountPercent =
            name === "discountPercent" ? value : prev.discountPercent;
          let discountAmount =
            name === "discountAmount" ? value : prev.discountAmount;
          const cashReceived =
            name === "cashReceived" ? value : prev.cashReceived;
          const creditCard = name === "creditCard" ? value : prev.creditCard;
          const grossAmt = prev.grossAmt;
          let sTaxAmount = prev.sTaxAmount;
          let netAmount = prev.netAmount;

          if (salesTaxR + salesTaxA > 100) {
            sTaxAmount = 0;
          } else {
            const totalPercentage = (salesTaxR + salesTaxA) / 100;
            sTaxAmount = grossAmt * totalPercentage;
          }
          if (name !== "netAmount") {
            netAmount = grossAmt + sTaxAmount;
          }

          discountAmount = (discountPercent / 100) * netAmount;
          const netPayableAmt = netAmount + freight - discountAmount;
          const balance = netPayableAmt - cashReceived - creditCard;

          return {
            ...prev,
            [name]: value,
            salesTaxR,
            salesTaxA,
            sTaxAmount,
            discountAmount,
            grossAmt,
            netAmount,
            netPayableAmt,
            balance,
          };
        });
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }));
      }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          qty: item?.Qty,
          rate: item?.Rate || item?.GrossRate,
          total: item.Total,
          Item_Title: item.itemTitle,
          unit: item.itemUnit,
          Installation_Charges: item.InstallationCharges,
          Item_ID: item.Item_ID || item.item_id,
          remarks: item.ItemName || item.details,
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          item_ID: item.Item_ID,
          ItemName: item.remarks,
          Qty: Number(item.qty),
          InstallationCharges: Number(item.Installation_Charges),
          GrossRate: Number(item.rate),
          Rate: Number(item.rate),
          Total: Number(item.total),
        };
      });
    }
  };
  const rowClickHandler = (data, rowIndex, colIndex) => {
    const isExist = tableData.find((modal) => modal.Item_ID === data.Item_ID);
    if (isExist) {
      setTableData((prev) => {
        const updatedTableData = prev.map((item) => {
          if (item.Item_ID === isExist.Item_ID) {
            return {
              ...item,
              qty: item.qty ? Number(item.qty) + 1 : 1,
            };
          }
          return item;
        });
        return updatedTableData;
      });
    } else {
      setTableData((prev) => {
        const updatedTableData = [...prev];
        updatedTableData[rowIndex] = {
          ...updatedTableData[rowIndex],
          Item_ID: data.Item_ID ? data.Item_ID : "",
          Item_Title: data.Item_Title ? data.Item_Title : "",
          unit: data.unit ? data.unit : "",
        };
        return updatedTableData;
      });
    }
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    if (["Item_ID", "Item_Title", "unit"].includes(key)) {
      return (
        <TableComboBox
          rowIndex={rowIndex}
          colIndex={colIndex}
          inputWidth={cellData.width}
          value={value}
          onChange={(val) => handleInputChange(val)}
          modalData={items}
          modalHeaders={ClientOrderItemTableHeader}
          isDisabled={isDisabled}
          rowClickHandler={rowClickHandler}
        />
      );
    }
    return (
      <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        disabled={isDisabled}
      />
    );
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          const updatedRow = { ...r, [header.key]: value };
          const qty = header.key === "qty" ? value : r.qty;
          const rate = header.key === "rate" ? value : r.rate;
          updatedRow.total = (Number(qty) || 0) * (Number(rate) || 0);
          return updatedRow;
        }
        return r;
      });
    });
  };

  useEffect(() => {
    let totalQty = 0;
    let totalAmount = 0;

    tableData.forEach((data) => {
      totalQty += Number(data.qty) || 0;
      totalAmount += Number(data.total) || 0;
    });

    const salesTaxR = formData.salesTaxR;
    const salesTaxA = formData.salesTaxA;
    const freight = formData.freight;
    const discountPercent = formData.discountPercent;
    let discountAmount = formData.discountAmount;
    const cashReceived = formData.cashReceived;
    const creditCard = formData.creditCard;
    const grossAmt = totalAmount;
    let sTaxAmount = formData.sTaxAmount;
    let netAmount = formData.netAmount;

    if (salesTaxR + salesTaxA > 100) {
      sTaxAmount = 0;
    } else {
      const totalPercentage = (salesTaxR + salesTaxA) / 100;
      sTaxAmount = grossAmt * totalPercentage;
    }

    if (name !== "netAmount") {
      netAmount = grossAmt + sTaxAmount;
    }

    discountAmount = (discountPercent / 100) * netAmount;
    const netPayableAmt = netAmount + freight - discountAmount;
    const balance = netPayableAmt - cashReceived - creditCard;

    setFormData((prev) => ({
      ...prev,
      totalQty,
      grossAmt: totalAmount,
      netAmount,
      netPayableAmt,
      balance,
      sTaxAmount,
      discountAmount,
    }));
  }, [tableData]);

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true);
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post("clientorders/getVoucherNo", {
          Mnth: year.toString(),
        });
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            location,
            vno,
            vtp,
            Mnth,
            voucherNo,
            mnth: Mnth,
          }));
          setLoading(false);
          toast({
            title: "Voucher No. Generated Successfully !",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false);
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const clientsData = await getData("getRecords/clients");
    const clientLeadsData = await getData("getRecords/client-leads");
    const salesManData = await getData("getRecords/salesMan");
    const deliveryMenData = await getData("getRecords/deliveryMen");
    const installersData = await getData("getRecords/installers");
    const cashData = await getData("getRecords/cashAccounts");
    const items = await getData("getRecords/items");
    const itemData = [];
    items.map((item) => {
      itemData.push({
        Item_ID: item.id,
        Item_Title: item.Title,
        unit: item.Unit,
      });
    });
    setItems(itemData);
    setClients(clientsData);
    setClientLeads(clientLeadsData);
    setSalesMan(salesManData);
    setDeliveryMen(deliveryMenData);
    setInstallers(installersData);
    setCash(cashData);
  };

  // Toolbar funtions starts here

  const handleSave = () => {
    const data = {
      Dated: formData.date ? formatDate(formData.date) : null,
      VTP: formData.vtp,
      Mnth: formData.mnth,
      Location: formData.location,
      vno: formData.vno,
      client_id: formData.clientId,
      TenderNo: formData.tenderNo,
      ContractNo: formData.contractNo,
      SalesMan_ID: formData.salesManId,
      Delivery_Person: formData.deliveryPersonId,
      Delivery_Time: formData.deliveryPersonTime,
      Installer_Person: formData.installerId,
      Installer_Time: formData.installationTime,
      GrossAmount: formData.grossAmt,
      SalesTaxPercentage: formData.salesTaxR,
      ASalesTaxPercentage: Number(formData.salesTaxA),
      SalesTaxAmount: formData.sTaxAmount,
      Discount: formData.discountAmount,
      po: formData.poNo,
      po_date: formData.po_date ? formatDate(formData.po_date) : null,
      Freight: formData.freight,
      NetAmount: formData.netAmount,
      Balance: formData.balance,
      Advance: formData.cashReceived,
      AdvanceCCard: formData.creditCard,
      Cash_id: formData.cashId,
      Narration: formData.narration,
      prp_id: user.userName,
      CreationDate: formatDate(new Date()),
      items: transformData(
        {
          Dated: formatDate(formData.date),
          VTP: formData.vtp,
          Mnth: formData.mnth,
          Location: formData.location,
          vno: formData.vno,
        },
        tableData
      ),
    };
    const isValidateObjectFields = validateObjectFields(
      data,
      ClientOrderRequiredFields
    );
    const isValidateArrayFields = validateArrayFields(
      data.items,
      ClientOrderItemsRequiredFields
    );
    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateArrayFields.error) {
      toast({
        title: isValidateArrayFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {
      setLoading(true);
      if (isEdit) {
        const voucherNo = formData.voucherNo;
        axiosInstance({
          method: "put",
          url: `clientorders/update?voucherNo=${voucherNo}`,
          data: data
        })
          .then((response) => {
            editForm();
            toast({
              title: "Order modified successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error modifing order:", error);
            toast({
              title: "Error modifing order :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      } else {
        axiosInstance({
          method: "post",
          url: "clientorders/create",
          data: data,
        })
          .then((response) => {
            clearForm();
            toast({
              title: "Order saved successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error saving order:", error);
            toast({
              title: "Error saving order :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      }
    } else {
      toast({
        title:
          "Please fill out all required fields and ensure all items have valid fields",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleDelete = async () => {
    const voucherNo = formData.voucherNo;
    if (!voucherNo) return
    setLoading(true);
    try {
      await axiosInstance.delete(`clientorders/delete?voucherNo=${voucherNo}`);
      setLoading(false);
      clearForm();
      toast({
        title: formData.voucherNo + ' voucher deleted successfully',
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: error.response.data || "Client Order Error",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      console.error("Error navigating to voucher form:", error);
      setLoading(false);
    }
  };

  const clearForm = () => {
    setIsNavigation(false);
    setIsDisabled(false);
    setIsEdit(false);
    setFormData(createClientOrderInitialFormData);
    setTableData(createClientOrderEmptyTableRow);
    toast({
      title: `Form Cleared`,
      status: "success",
      variant: "left-accent",
      position: "top-right",
      isClosable: true,
    });
  };

  const editForm = () => {
    setIsDisabled((p) => !p);
    setIsEdit((p) => !p);
  };

  const navigateVoucherForm = async (navigate, voucherNo) => {
    setLoading(true);
    setIsNavigation(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("clientorders/navigate", {
        [navigate]: true,
        voucher_no: voucherNo,
      });
      const resData = response.data;
      const { items } = response.data;
      const form = {
        date: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.VTP,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        clientId: resData?.client_id ?? "",
        clientTitle: resData?.clientTitle ?? "",
        tenderNo: resData?.TenderNo ?? "",
        contractNo: resData?.ContractNo ?? "",
        salesManId: resData?.SalesMan_ID ?? "",
        deliveryPersonId: resData?.Delivery_Person ?? "",
        deliveryPersonName: resData?.deliveryManName ?? "",
        deliveryPersonTime: resData?.Delivery_Time
          ? formatDate(new Date(resData?.Delivery_Time), true)
          : null,
        installerId: resData?.Installer_Person ?? "",
        installerName: resData?.installerName ?? "",
        installationTime: resData?.Installer_Time
          ? formatDate(new Date(resData?.Installer_Time), true)
          : null,
        salesManName: resData?.salesManName ?? "",
        grossAmt: resData?.GrossAmount ?? 0,
        salesTaxR: resData?.SalesTaxPercentage ?? 0,
        salesTaxA: resData?.ASalesTaxPercentage ?? 0,
        sTaxAmount: resData?.SalesTaxAmount ?? 0,
        discountPercent: (resData?.Discount / resData?.NetAmount) * 100 ?? 0,
        discountAmount: resData?.Discount ?? 0,
        poNo: resData?.po ?? "",
        cashId: resData?.Cash_id ?? "",
        cashName: resData?.CashTitle ?? "",
        po_date: resData?.po_date
          ? formatDate(new Date(resData?.po_date), true)
          : formatDate(new Date("2014-12-21"), true),
        freight: resData?.Freight ?? 0,
        netAmount: resData?.NetAmount ?? 0,
        balance: resData?.Balance ?? 0,
        narration: resData?.Narration ?? "",
        cashReceived: resData.Advance ?? 0,
        creditCard: resData.AdvanceCCard ?? 0,
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      toast({
        title: `${navigate.toUpperCase()} Voucher Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      setIsNavigation(false);
      setIsDisabled(false);
      console.error(`Error fetching ${navigate} voucher:`, error);
      toast({
        title: `${navigate} Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const copyVoucherForm = async (voucherNo) => {
    setLoading(true);
    try {
      const response = await axiosInstance.post("offer/navigate", {
        goto: true,
        voucher_no: voucherNo,
      });
      const resData = response.data;
      const { items } = response.data;
      const form = {
        clientId: resData?.client_id ?? "",
        clientTitle: resData?.ClientName ?? "",
        tenderNo: resData?.TenderNo ?? "",
        contractNo: resData?.ContractNo ?? "",
        salesManId: resData?.SalesMan_ID ?? "",
        deliveryPersonId: resData?.Delivery_Person ?? "",
        deliveryPersonName: resData?.deliveryManName ?? "",
        deliveryPersonTime: resData?.Delivery_Time
          ? formatDate(new Date(resData?.Delivery_Time), true)
          : null,
        installerId: resData?.Installer_Person ?? "",
        installerName: resData?.installerName ?? "",
        installationTime: resData?.Installer_Time
          ? formatDate(new Date(resData?.Installer_Time), true)
          : null,
        salesManName: resData?.salesManName ?? "",
        grossAmt: resData?.GrossAmount ?? 0,
        salesTaxR: resData?.SalesTaxR ?? 0,
        salesTaxA: resData?.SalesTaxA ?? 0,
        sTaxAmount: resData?.SalesTaxAmount ?? 0,
        discountPercent: (resData?.Discount / resData?.NetAmount) * 100 ?? 0,
        discountAmount: resData?.Discount ?? 0,
        poNo: resData?.po ?? "",
        cashId: resData?.Cash_id ?? "",
        cashName: resData?.CashTitle ?? "",
        po_date: resData?.po_date
          ? formatDate(new Date(resData?.po_date), true)
          : formatDate(new Date("2014-12-21"), true),
        freight: resData?.Freight ?? 0,
        netAmount: resData?.NetAmount ?? 0,
        balance: resData?.Balance ?? 0,
        narration: resData?.StartingComments ?? "",
        cashReceived: resData.Advance ?? 0,
        creditCard: resData.AdvanceCCard ?? 0,
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      setLoading(false);
      toast({
        title: `Copy Voucher Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error(`Error fetching Copy voucher:`, error);
      setLoading(false);
      toast({
        title: `Copy Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  // Toolbar funtions ends here

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "24px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Client Order
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(40% - 5px) !important",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <form>
                        {ClientOrderSectionFormFields[0].map((field) => (
                          <FormControl
                            key={field.id}
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                md: "row",
                              },
                              marginTop: "10px",
                            }}
                            isRequired={field.isRequired}
                          >
                            <FormLabel
                              htmlFor={field.id}
                              sx={{
                                marginBottom: "0",
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  md: "20%",
                                  lg: "35%",
                                },
                              }}
                            >
                              {field.label}
                            </FormLabel>
                            {field.type === "date" ? (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                // min={field.minDate}
                                // max={field.maxDate}
                                disabled={isEdit || isDisabled}
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            ) : (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                disabled={field.name === 'voucherNo' ? (isEdit || isDisabled) : isDisabled}
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            )}
                          </FormControl>
                        ))}

                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                            onClick={() => setIsCopyDialogOpen(true)}
                            isDisabled={isEdit || isDisabled}>
                            Copy Voucher
                          </Button>
                        </FormControl>

                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                            onClick={() => getVoucherNo(formData.date)}
                            isDisabled={isEdit || isDisabled}>
                            Generate Voucher No
                          </Button>
                        </FormControl>
                      </form>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(60% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      {ClientOrderSectionFormFields[1].map((control, index) => (
                        <FormControl
                          key={index}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              lg: "row",
                            },
                            marginTop: "10px",
                            flexWrap: "nowrap",
                          }}
                          isRequired={control.isRequired}
                        >
                          <FormLabel
                            htmlFor={control.fields[0].name}
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "20%" },
                            }}
                          >
                            {control.label}
                          </FormLabel>
                          <Box
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "80%" },
                              display: "flex",
                              gap: control.fields.length > 1 ? "10px" : "0",
                            }}
                          >
                            {control.fields.map((field, fieldIndex) =>
                              field.component === "ComboBox" ? (
                                <ComboBox
                                  key={fieldIndex}
                                  target={true}
                                  onChange={handleInputChange}
                                  name={field.name}
                                  inputWidth={field.inputWidths}
                                  buttonWidth={field.buttonWidth}
                                  styleButton={{ padding: "3px !important" }}
                                  tableData={field.tableData === "clients"
                                    ? clients
                                    : field.tableData === "installers"
                                      ? installers
                                      : field.tableData === "deliveryMen"
                                        ? deliveryMen
                                        : salesMan
                                  }
                                  tableHeaders={field.tableHeaders}
                                  nameFields={field.nameFields}
                                  placeholders={field.placeholders}
                                  keys={field.keys}
                                  form={formData}
                                  isDisabled={isDisabled}
                                />
                              ) : (
                                <Input
                                  key={fieldIndex}
                                  onChange={handleInputChange}
                                  name={field.name}
                                  placeholder={field.placeholder}
                                  value={formData[field.value]}
                                  _placeholder={field._placeholder}
                                  type={field.type}
                                  style={{ width: field.inputWidth }}
                                  disabled={isDisabled}
                                />
                              )
                            )}
                          </Box>
                        </FormControl>
                      ))}
                    </Box>
                  </div>
                  <div className="row">
                    <div
                      style={{ padding: "0" }}
                      className="bgWhite col-md-12 mt-2"
                    >
                      <AanzaDataTable
                        tableData={tableData}
                        setTableData={setTableData}
                        headers={ClientOrderHeaders}
                        tableWidth="100%"
                        tableHeight="400px"
                        fontSize="lg"
                        cellRender={cellRender}
                        onSave={handleSave}
                        styleHead={{
                          background: "#3275bb",
                          color: "white !important",
                        }}
                        styleBody={{ background: "white !important" }}
                        calculation={calculation}
                        isDisabled={isDisabled}
                      />
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2">
                      <div
                        className="pt-4 pb-4"
                        style={{
                          display: "grid",
                          gridTemplateColumns:
                            "repeat(auto-fit,minmax(300px,1fr))",
                          gap: "5px",
                        }}
                      >
                        {ClientOrderSectionFormFields[2].map((group, index) => (
                          <FormControl
                            key={index}
                            style={{ display: "flex", gap: "4px" }}
                          >
                            {group.fields ? (
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "flex-start",
                                  width: "100%",
                                  flexDirection: {
                                    base: "column",
                                    sm: "column",
                                    lg: "row",
                                  },
                                }}
                              >
                                <FormLabel
                                  sx={{
                                    width: {
                                      base: "100%",
                                      sm: "100%",
                                      lg: "30%",
                                    },
                                  }}
                                >
                                  {group.label}
                                </FormLabel>
                                <Box
                                  sx={{
                                    width: {
                                      base: "100%",
                                      sm: "100%",
                                      lg: "70%",
                                    },
                                    display: "flex",
                                  }}
                                >
                                  {group.fields.map((field, fieldIndex) =>
                                    field.isPercentage ? (
                                      <Input
                                        key={fieldIndex}
                                        onChange={(e) => {
                                          handleInputChange({
                                            target: {
                                              name: field.name,
                                              value: e.target.value.toString(),
                                            },
                                          });
                                        }}
                                        name={field.name}
                                        placeholder={field.label}
                                        value={formData[field.value]}
                                        _placeholder={{ color: "gray.500" }}
                                        type="number"
                                        sx={field.style}
                                        isReadOnly={field.isReadOnly}
                                        min={0}
                                        max={100}
                                        disabled={isDisabled}
                                      />
                                    ) : (
                                      <Input
                                        key={fieldIndex}
                                        onChange={handleInputChange}
                                        name={field.name}
                                        placeholder={field.label}
                                        value={formData[field.value]}
                                        _placeholder={{ color: "gray.500" }}
                                        type="number"
                                        sx={field.style}
                                        isReadOnly={field.isReadOnly}
                                        disabled={isDisabled}
                                      />
                                    )
                                  )}
                                </Box>
                              </Box>
                            ) : (
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "flex-start",
                                  width: "100%",
                                  flexDirection: {
                                    base: "column",
                                    sm: "column",
                                    lg: "row",
                                  },
                                }}
                              >
                                <FormLabel
                                  sx={{
                                    width: {
                                      base: "100%",
                                      sm: "100%",
                                      lg: "30%",
                                    },
                                  }}
                                >
                                  {group.label}
                                </FormLabel>
                                <Input
                                  onChange={handleInputChange}
                                  name={group.name}
                                  value={formData[group.value]}
                                  placeholder=""
                                  _placeholder={{ color: "gray.500" }}
                                  type="number"
                                  sx={group.style}
                                  isReadOnly={group.isReadOnly}
                                  disabled={isDisabled}
                                />
                              </Box>
                            )}
                          </FormControl>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      {ClientOrderSectionFormFields[3].map((section, index) => (
                        <div key={index}>
                          <FormControl
                            sx={{
                              display: "flex",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                            isRequired
                          >
                            <FormLabel
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "15%" },
                              }}
                            >
                              {section.label}
                            </FormLabel>
                            <Box
                              sx={{
                                display: "flex",
                                gap: "5px",
                                width: { base: "100%", sm: "100%", lg: "85%" },
                              }}
                            >
                              {section.fields.map((field, fieldIndex) => (
                                <ComboBox
                                  key={fieldIndex}
                                  target={true}
                                  onChange={handleInputChange}
                                  nameFields={field.nameFields}
                                  inputWidth={field.inputWidth}
                                  buttonWidth="20px"
                                  styleButton={{ padding: "3px !important" }}
                                  tableData={cash}
                                  tableHeaders={field.tableHeaders}
                                  placeholders={field.placeholders}
                                  keys={field.keys}
                                  form={formData}
                                  isDisabled={isDisabled}
                                />
                              ))}
                            </Box>
                          </FormControl>
                        </div>
                      ))}

                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "15%" },
                          }}
                        >
                          Narration
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "85%" },
                          }}
                          onChange={handleInputChange}
                          name={"narration"}
                          value={formData.narration}
                          disabled={isDisabled}
                        />
                      </FormControl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Toolbar
            save={handleSave}
            clear={clearForm}
            edit={editForm}
            first={() => navigateVoucherForm("first")}
            last={() => navigateVoucherForm("last")}
            previous={() => navigateVoucherForm("prev", formData.voucherNo)}
            next={() => navigateVoucherForm("next", formData.voucherNo)}
            goto={() => setIsGotoDialogOpen(true)}
            remove={() => setDialogOpen(true)}
            isNavigation={isNavigation}
            isEdit={isEdit}
            print={handlePrint}
            hide={["Cancel"]}
          />
        </>
      )}
      <ConfirmDialog
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Voucher"
        message={
          <>Are you sure you want to delete voucher no <b>{formData.voucherNo}</b> ?</>
        }
        confirmText="Delete"
        cancelText="Cancel"
        confirmColorScheme="red"
      />
      <CopyDialog
        isOpen={isCopyDialogOpen}
        onClose={() => setIsCopyDialogOpen(false)}
        onConfirm={() => copyVoucherForm(formData.copyVoucherNo)}
        formData={formData}
        setFormData={setFormData}
        tableData={clientLeads}
      />
      <GotoDialog
        isOpen={isGotoDialogOpen}
        onClose={() => setIsGotoDialogOpen(false)}
        onConfirm={() => navigateVoucherForm("goto", formData.voucherNo)}
        formData={formData}
        setFormData={setFormData}
      />
      <PrintModal isOpen={isPrintModalOpen} onClose={() => setPrintModalOpen(false)} formName="Client Order">
        <ComponentToPrint data={printData} />
      </PrintModal>
    </>
  );
};

export default ClientOrder;
