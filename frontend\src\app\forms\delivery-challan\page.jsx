"use client";
import React, { useEffect, useState } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import ConfirmDialog from "@components/Custom/ConfirmDialog/ConfirmDialog";
import axiosInstance from "../../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input, Textarea } from "@chakra-ui/react";
import {
  formatDate,
  validateArrayFields,
  validateObjectFields,
} from "@utils/functions";
import Loader from "@components/Loader/Loader";

const DeliveryChallanRequiredFields = [
  "Dated",
  "VTP",
  "Mnth",
  "Location",
  "vno",
  "client_id",
  "PONo",
  // "Currency_ID",
  // "ExchRate",
  // "Party_ref",
  // "poNo_Desig",
  // "Subject",
  // "GrdTotalPSTAmt",
  // "Freight",
  // "NetAmount",
  // "Terms",
  // "StartingComments",
];

const DeliveryChallanItemsRequiredFields = [
  "Dated",
  "VTP",
  "Mnth",
  "Location",
  "vno",
  "srno",
  "item_id",
  "Qty",
  "Rate",
  "Total",
  // "Discount",
  // "VisitDate",
  // "details",
];

const DeliveryChallanHeaders = [
  {
    label: "Item ID",
    key: "Item_ID",
    width: "100px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Item Title",
    key: "Item_Title",
    width: "300px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Unit",
    key: "unit",
    width: "60px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Remarks",
    key: "remarks",
    width: "320px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Charges",
    key: "Installation_Charges",
    width: "100px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Qty",
    key: "qty",
    width: "70px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Rate",
    key: "rate",
    width: "70px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Total",
    key: "total",
    width: "90px",
    isReadOnly: true,
    type: "number",
  },
];
const createDeliveryChallanEmptyTableRow = () => [
  {
    Item_ID: "",
    Item_Title: "",
    unit: "",
    remarks: "",
    qty: "",
    rate: "",
    total: "",
    Installation_Charges: 0,
  },
];

const createDeliveryChallanInitialFormData = () => ({
  Dated: "",
  VTP: "",
  Mnth: "",
  Location: "",
  voucherNo: "",
  VNo: 0,
  Client_ID: "",
  clientTitle: "",
  PONo: "",
  PODate: "",
  Givento: "",
  Narration: "",
  DriverName: "",
  VehicleNo: "",
  CreationDate: "",
});

const DeliveryChallan = () => {
  const toast = useToast();
  const [clients, setClients] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [salesMan, setSalesMan] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [items, setItems] = useState([]);
  const [isNavigation, setIsNavigation] = useState(false);
  const [tableData, setTableData] = useState(
    createDeliveryChallanEmptyTableRow()
  );
  const [formData, setFormData] = useState(
    createDeliveryChallanInitialFormData()
  );
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;

      // if (DeliveryChallanCalculationFields.includes(name)) {
      //   setFormData((prev) => {
      //     const salesTaxR =
      //       name === "salesTaxR" ? Number(value) : Number(prev.salesTaxR);
      //     const salesTaxA =
      //       name === "salesTaxA" ? Number(value) : Number(prev.salesTaxA);
      //     const freight =
      //       name === "freight" ? Number(value) : Number(prev.freight);
      //     const discountPercent =
      //       name === "discountPercent" ? value : prev.discountPercent;
      //     let discountAmount =
      //       name === "discountAmount" ? value : prev.discountAmount;
      //     const cashReceived =
      //       name === "cashReceived" ? value : prev.cashReceived;
      //     const creditCard = name === "creditCard" ? value : prev.creditCard;
      //     const grossAmt = prev.grossAmt;
      //     let sTaxAmount = prev.sTaxAmount;
      //     let netAmount = prev.netAmount;

      //     if (salesTaxR + salesTaxA > 100) {
      //       sTaxAmount = 0;
      //     } else {
      //       const totalPercentage = (salesTaxR + salesTaxA) / 100;
      //       sTaxAmount = grossAmt * totalPercentage;
      //     }
      //     if (name !== "netAmount") {
      //       netAmount = grossAmt + sTaxAmount;
      //     }

      //     discountAmount = (discountPercent / 100) * netAmount;
      //     const netPayableAmt = netAmount + freight - discountAmount;
      //     const balance = netPayableAmt - cashReceived - creditCard;

      //     return {
      //       ...prev,
      //       [name]: value,
      //       salesTaxR,
      //       salesTaxA,
      //       sTaxAmount,
      //       discountAmount,
      //       grossAmt,
      //       netAmount,
      //       netPayableAmt,
      //       balance,
      //     };
      //   });
      // } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
      // }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          Item_ID: item?.Item_ID,
          Item_Title: item?.item_title,
          unit: item?.item_unit,
          remarks: item?.Remarks,
          rate: Number(item?.Rate),
          total: Number(item?.Total),
          qty: Number(item?.Qty),
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          item_id: item.Item_ID,
          Rate: Number(item.Rate),
          Discount: Number(item.Disc),
          Total: Number(item.Total),
          VisitDate: item.Date,
          details: item.Details,
          Qty: Number(item.Qty),
        };
      });
    }
  };
  const rowClickHandler = (data, rowIndex, colIndex) => {
    const isExist = tableData.find((modal) => modal.Item_ID === data.Item_ID);
    if (isExist) {
      setTableData((prev) => {
        const updatedTableData = prev.map((item) => {
          if (item.Item_ID === isExist.Item_ID) {
            return {
              ...item,
              qty: item.qty ? Number(item.qty) + 1 : 1,
            };
          }
          return item;
        });
        return updatedTableData;
      });
    } else {
      setTableData((prev) => {
        const updatedTableData = [...prev];
        updatedTableData[rowIndex] = {
          ...updatedTableData[rowIndex],
          Item_ID: data.Item_ID ? data.Item_ID : "",
          Item_Title: data.Item_Title ? data.Item_Title : "",
          unit: data.unit ? data.unit : "",
        };
        return updatedTableData;
      });
    }
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    if (["Item_ID", "Item_Title", "unit"].includes(key)) {
      return (
        <TableComboBox
          rowIndex={rowIndex}
          colIndex={colIndex}
          inputWidth={cellData.width}
          value={value}
          onChange={(val) => handleInputChange(val)}
          modalData={items}
          modalHeaders={["ID", "Title", "unit"]}
          isDisabled={isDisabled}
          rowClickHandler={rowClickHandler}
        />
      );
    }
    return (
      <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        disabled={isDisabled}
      />
    );
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          const updatedRow = { ...r, [header.key]: value };
          const qty = header.key === "qty" ? value : r.qty;
          const rate = header.key === "rate" ? value : r.rate;
          updatedRow.total = (Number(qty) || 0) * (Number(rate) || 0);
          return updatedRow;
        }
        return r;
      });
    });
  };

  // useEffect(() => {
  //   let totalQty = 0;
  //   let totalAmount = 0;

  //   tableData.forEach((data) => {
  //     totalQty += Number(data.qty) || 0;
  //     totalAmount += Number(data.total) || 0;
  //   });

  //   const salesTaxR = formData.salesTaxR;
  //   const salesTaxA = formData.salesTaxA;
  //   const freight = formData.freight;
  //   const discountPercent = formData.discountPercent;
  //   let discountAmount = formData.discountAmount;
  //   const cashReceived = formData.cashReceived;
  //   const creditCard = formData.creditCard;
  //   const grossAmt = totalAmount;
  //   let sTaxAmount = formData.sTaxAmount;
  //   let netAmount = formData.netAmount;

  //   if (salesTaxR + salesTaxA > 100) {
  //     sTaxAmount = 0;
  //   } else {
  //     const totalPercentage = (salesTaxR + salesTaxA) / 100;
  //     sTaxAmount = grossAmt * totalPercentage;
  //   }

  //   if (name !== "netAmount") {
  //     netAmount = grossAmt + sTaxAmount;
  //   }

  //   discountAmount = (discountPercent / 100) * netAmount;
  //   const netPayableAmt = netAmount + freight - discountAmount;
  //   const balance = netPayableAmt - cashReceived - creditCard;

  //   setFormData((prev) => ({
  //     ...prev,
  //     totalQty,
  //     grossAmt: totalAmount,
  //     netAmount,
  //     netPayableAmt,
  //     balance,
  //     sTaxAmount,
  //     discountAmount,
  //   }));
  // }, [tableData]);

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true);
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post("deliveryChallan/getVoucherNo", {
          Mnth: year.toString(),
        });
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            VTP: vtp,
            Location: location,
            VNo: vno,
            Mnth,
            voucherNo,
          }));
          setLoading(false);
          toast({
            title: "Voucher No. Generated Successfully !",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false);
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const clientsData = await getData("getRecords/clients");
    const currenciesData = await getData("getRecords/currencies");
    const items = await getData("getRecords/items");
    const salesManData = await getData("getRecords/salesMan");
    const itemData = [];
    items.map((item) => {
      itemData.push({
        Item_ID: item.id,
        Item_Title: item.Title,
        unit: item.Unit,
      });
    });
    setItems(itemData);
    setClients(clientsData);
    setCurrencies(currenciesData);
    setSalesMan(salesManData);
  };

  // Toolbar funtions starts here

  const handleSave = () => {
    const data = {
      Dated: formData.Dated ? formatDate(formData.Dated) : null,
      VTP: formData.vtp,
      Mnth: formData.mnth,
      Location: formData.location,
      vno: formData.vno,
      Currency_ID: formData.currency,
      Validity: formData.validityDays,
      ExchRate: formData.exchangeRate,
      Party_ref: formData.attn,
      TenderNo: formData.tenderNo,
      freight: formData.freight,
      PONo: formData.poNo,
      poNo_Desig: formData.poDate,
      Subject: formData.subject,
      GrdTotalPSTAmt: formData.totalAmount,
      Freight: formData.freight,
      NetAmount: formData.netAmount,
      Terms: formData.paymentTerms,
      StartingComments: formData.narration,
      client_id: formData.clientId,
      CreationDate: formatDate(new Date()),
      items: transformData(
        {
          Dated: formatDate(formData.Dated),
          VTP: formData.vtp,
          Mnth: formData.mnth,
          Location: formData.location,
          vno: formData.vno,
        },
        tableData
      ),
    };
    const isValidateObjectFields = validateObjectFields(
      data,
      DeliveryChallanRequiredFields
    );
    const isValidateArrayFields = validateArrayFields(
      data.items,
      DeliveryChallanItemsRequiredFields
    );
    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateArrayFields.error) {
      toast({
        title: isValidateArrayFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {
      setLoading(true);
      if (isEdit) {
        const voucherNo = formData.voucherNo;
        axiosInstance({
          method: "put",
          url: `deliveryChallan/update?voucherNo=${voucherNo}`,
          data: data,
        })
          .then((response) => {
            editForm();
            toast({
              title: "Delivery Challan Form modified successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error modifing Delivery Challan Form:", error);
            toast({
              title: "Error modifing Delivery Challan Form :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      } else {
        axiosInstance({
          method: "post",
          url: "deliveryChallan/create",
          data: data,
        })
          .then((response) => {
            clearForm();
            toast({
              title: "Delivery Challan Form saved successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error saving Delivery Challan Form:", error);
            toast({
              title: "Error saving Delivery Challan Form :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      }
    } else {
      toast({
        title:
          "Please fill out all required fields and ensure all items have valid fields",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleDelete = async () => {
    const voucherNo = formData.voucherNo;
    if (!voucherNo) return;
    setLoading(true);
    try {
      await axiosInstance.delete(`deliveryChallan/delete?voucherNo=${voucherNo}`);
      setLoading(false);
      clearForm();
      toast({
        title: formData.voucherNo + " voucher deleted successfully",
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: error.response.data || "Delivery Challan Error",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      console.error("Error navigating to voucher form:", error);
      setLoading(false);
    }
  };

  const clearForm = () => {
    setIsNavigation(false);
    setIsDisabled(false);
    setIsEdit(false);
    setFormData(createDeliveryChallanInitialFormData);
    setTableData(createDeliveryChallanEmptyTableRow);
    toast({
      title: `Form Cleared`,
      status: "success",
      variant: "left-accent",
      position: "top-right",
      isClosable: true,
    });
  };

  const editForm = () => {
    setIsDisabled((p) => !p);
    setIsEdit((p) => !p);
  };

  const navigateVoucherForm = async (navigate, voucherNo) => {
    setLoading(true);
    setIsNavigation(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("deliveryChallan/navigate", {
        [navigate]: true,
        voucher_no: voucherNo,
      });
      const resData = response.data;
      const { items } = response.data;
      const form = {
        Dated: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.VTP,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        Client_ID: resData?.Client_ID ?? "",
        clientTitle: resData?.ClientName ?? "",
        PONo: resData?.PONo ?? "",
        PODate: resData?.PODate
          ? formatDate(new Date(resData?.PODate), true)
          : null,
        Narration: resData?.Narration ?? "",
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      setLoading(false);
      toast({
        title: `${navigate.toUpperCase()} Voucher Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error(`Error fetching ${navigate} voucher:`, error);
      setLoading(false);
      toast({
        title: `${navigate} Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  // Toolbar funtions ends here

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "24px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Delivery Challan
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(40% - 5px) !important",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <form>
                        <FormControl
                          key={"Dated"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"Dated"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Date
                          </FormLabel>
                          <Input
                            id={"Dated"}
                            name={"Dated"}
                            type={"date"}
                            value={formData.Dated}
                            onChange={handleInputChange}
                            _placeholder={{ color: "gray.500" }}
                            disabled={isEdit || isDisabled}
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          />
                        </FormControl>

                        <FormControl
                          key={"VNo"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"VNo"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Voucher No.
                          </FormLabel>
                          <Input
                            id={"voucherNo"}
                            name={"voucherNo"}
                            type={"text"}
                            value={formData.voucherNo}
                            onChange={handleInputChange}
                            readOnly={true}
                            disabled={isEdit || isDisabled}
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          />
                        </FormControl>

                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                            onClick={() => getVoucherNo(formData.Dated)}
                            isDisabled={isEdit || isDisabled}
                          >
                            Generate Voucher No
                          </Button>
                        </FormControl>
                      </form>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(60% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        key={"Client_ID"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={true}
                      >
                        <FormLabel
                          htmlFor={"Client_ID"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Client
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <ComboBox
                            target={true}
                            onChange={handleInputChange}
                            name={"Client_ID"}
                            buttonWidth={"20px"}
                            styleButton={{ padding: "3px !important" }}
                            tableData={clients}
                            tableHeaders={["ID", "Title"]}
                            nameFields={["Client_ID", "clientTitle"]}
                            placeholders={["ID", "Title"]}
                            keys={["id", "title"]}
                            form={formData}
                            isDisabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      {/* <FormControl
                        key={"Attn"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"Attn"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Attn
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"Attn"}
                            placeholder={""}
                            value={formData.Attn}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl> */}

                      {/* <FormControl
                        key={"freight"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={'freight'}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Freight
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={'freight'}
                            placeholder={''}
                            value={formData.freight}
                            _placeholder={{ color: "gray.500" }}
                            type={'text'}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl> */}
                      <FormControl
                        key={"Givento"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={true}
                      >
                        <FormLabel
                          htmlFor={"Givento"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Given To
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <ComboBox
                            target={true}
                            onChange={handleInputChange}
                            name={"Givento"}
                            buttonWidth={"20px"}
                            styleButton={{ padding: "3px !important" }}
                            tableData={salesMan}
                            // tableData={[]}
                            tableHeaders={["ID", "Name"]}
                            nameFields={["Givento_ID", "Givento_Title"]}
                            placeholders={["ID", "Name"]}
                            keys={["Id", "Title"]}
                            // inputWidths={["100%"]}
                            form={formData}
                            isDisabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        key={"PONo"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"PONo"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Po No
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"PONo"}
                            placeholder={""}
                            value={formData.PONo}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        key={"PODate"}
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"PODate"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Po Date
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"PODate"}
                            placeholder={""}
                            value={formData.PODate}
                            _placeholder={{ color: "gray.500" }}
                            type={"date"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                  </div>
                  <div className="row">
                    <div
                      style={{ padding: "0" }}
                      className="bgWhite col-md-12 mt-2"
                    >
                      <AanzaDataTable
                        tableData={tableData}
                        setTableData={setTableData}
                        headers={DeliveryChallanHeaders}
                        tableWidth="100%"
                        tableHeight="400px"
                        fontSize="lg"
                        cellRender={cellRender}
                        onSave={handleSave}
                        styleHead={{
                          background: "#3275bb",
                          color: "white !important",
                        }}
                        styleBody={{ background: "white !important" }}
                        calculation={calculation}
                        isDisabled={isDisabled}
                      />
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2">
                      <div
                        className="pt-4 pb-2"
                        style={{
                          display: "grid",
                          gridTemplateColumns:
                            "repeat(auto-fit,minmax(300px,1fr))",
                          gap: "5px",
                        }}
                      >
                        <FormControl
                          key={"DriverName"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              lg: "row",
                            },
                            marginTop: "10px",
                            flexWrap: "nowrap",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"DriverName"}
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "20%" },
                            }}
                          >
                            Driver Name
                          </FormLabel>
                          <Box
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "80%" },
                              display: "flex",
                              gap: "0",
                            }}
                          >
                            <ComboBox
                              target={true}
                              onChange={handleInputChange}
                              name={"DriverName"}
                              buttonWidth={"20px"}
                              styleButton={{ padding: "3px !important" }}
                              tableData={salesMan}
                              // tableData={[]}
                              tableHeaders={["ID", "Name"]}
                              nameFields={["salesManName"]}
                              placeholders={["Name"]}
                              keys={["Title"]}
                              inputWidths={["100%"]}
                              form={formData}
                              isDisabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl
                          key={"VehicleNo"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              lg: "row",
                            },
                            marginTop: "10px",
                            flexWrap: "nowrap",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"VehicleNo"}
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "20%" },
                            }}
                          >
                            Vehicle No
                          </FormLabel>
                          <Box
                            sx={{
                              width: { base: "100%", sm: "100%", lg: "80%" },
                              display: "flex",
                              gap: "0",
                            }}
                          >
                            <ComboBox
                              target={true}
                              onChange={handleInputChange}
                              name={"VehicleNo"}
                              buttonWidth={"20px"}
                              styleButton={{ padding: "3px !important" }}
                              // tableData={vehicleNo}
                              tableData={[]}
                              tableHeaders={["ID"]}
                              nameFields={["VehicleNo"]}
                              placeholders={["ID"]}
                              keys={["id"]}
                              inputWidths={["100%"]}
                              form={formData}
                              isDisabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      <FormControl
                        key={"Narration"}
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "15%" },
                          }}
                        >
                          Narration
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "85%" },
                          }}
                          onChange={handleInputChange}
                          name={"Narration"}
                          value={formData.Narration}
                          disabled={isDisabled}
                        />
                      </FormControl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Toolbar
            save={handleSave}
            clear={clearForm}
            edit={editForm}
            first={() => navigateVoucherForm("first")}
            last={() => navigateVoucherForm("last")}
            previous={() => navigateVoucherForm("prev", formData.voucherNo)}
            next={() => navigateVoucherForm("next", formData.voucherNo)}
            remove={() => setDialogOpen(true)}
            isNavigation={isNavigation}
            isEdit={isEdit}
          />
        </>
      )}
      <ConfirmDialog
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Voucher"
        message={
          <>
            Are you sure you want to delete voucher no{" "}
            <b>{formData.voucherNo}</b> ?
          </>
        }
        confirmText="Delete"
        cancelText="Cancel"
        confirmColorScheme="red"
      />
    </>
  );
};

export default DeliveryChallan;
