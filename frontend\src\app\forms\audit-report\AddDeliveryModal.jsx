import {
  Modal,
  ModalOverlay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  FormErrorMessage,
} from "@chakra-ui/react";
import axiosInstance from "@src/app/axios";
import React, { useState, useEffect } from 'react';
import ComboBox from "@src/components/Custom/ComboBox/ComboBox";
import { getData } from "@src/app/utils/functions";

const AddDeliveryModal = ({ isOpen, onClose, onSave, purposeId }) => {
  const toast = useToast();
  const [formData, setFormData] = useState({
    selectedId: '',
    selectedTitle: '',
    installerId: '',
    installerName: '',
    deliveryDate: '',
    installerDate: ''
  });
  const [errors, setErrors] = useState({});
  const [deliveryPersons, setDeliveryPersons] = useState([]);
  const [installers, setInstallers] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchDeliveryPersons();
      fetchInstallers();
    } else {
      clearFormData();
    }
  }, [isOpen]);

  const clearFormData = () => {
    setFormData({
      selectedId: '',
      selectedTitle: '',
      installerId: '',
      installerName: '',
      deliveryDate: '',
      installerDate: ''
    });
    setErrors({});
  };

  const fetchDeliveryPersons = async () => {
    try {
      const deliveryMenData = await getData("getRecords/deliveryMen");
      if (Array.isArray(deliveryMenData)) {
        setDeliveryPersons(deliveryMenData);
      }
    } catch (error) {
      console.error("Error fetching delivery persons:", error);
    }
  };

  const fetchInstallers = async () => {
    try {
      const installersData = await getData("getRecords/installers");
      if (Array.isArray(installersData)) {
        setInstallers(installersData);
      }
    } catch (error) {
      console.error("Error fetching installers:", error);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.selectedId) {
      newErrors.selectedId = 'Please select a delivery person';
    }

    if (!formData.installerId) {
      newErrors.installerId = 'Please select an installer';
    }

    if (!formData.deliveryDate) {
      newErrors.deliveryDate = 'Please select delivery date and time';
    } else {
      const selectedDate = new Date(formData.deliveryDate);
      const now = new Date();
      if (selectedDate < now) {
        newErrors.deliveryDate = 'Delivery date cannot be in the past';
      }
    }
    
    if (!formData.installerDate) {
      newErrors.installerDate = 'Please select installation date and time';
    } else {
      const selectedDate = new Date(formData.installerDate);
      const now = new Date();
      if (selectedDate < now) {
        newErrors.installerDate = 'Installation date cannot be in the past';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;
      setFormData((prev) => ({ ...prev, [name]: value }));
      if (errors[name]) {
        setErrors(prev => ({ ...prev, [name]: '' }));
      }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
      const fieldsToUpdate = Object.keys(bulkInput);
      const newErrors = { ...errors };
      fieldsToUpdate.forEach(field => delete newErrors[field]);
      setErrors(newErrors);
    }
  };

  const handleClose = () => {
    clearFormData();
    onClose();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const payload = {
        deliveryPersonId: formData.selectedId,
        installerId: formData.installerId,
        deliveryPersonTime: formData.deliveryDate,
        installerTime: formData.installerDate
      };

      await axiosInstance.put("/purpose/assign-delivery-installer/" + purposeId, payload);
      onSave();
      toast({
        title: "Success",
        description: "Delivery person and installer assigned successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      clearFormData();
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to assign delivery person and installer",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.selectedId && formData.installerId && formData.deliveryDate && formData.installerDate && Object.keys(errors).length === 0;

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader style={{ background: "#2d6651", color: "white" }}>Assign Delivery Person & Installer</ModalHeader>
        <ModalCloseButton style={{ color: "white" }} />
        <form onSubmit={handleSubmit} style={{ padding: "0" }}>
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired isInvalid={!!errors.selectedId}>
                <FormLabel>Select Delivery Person</FormLabel>
                <ComboBox
                  target={true}
                  onChange={handleInputChange}
                  name={"selectedItem"}
                  buttonWidth={"20px"}
                  styleButton={{ padding: "3px !important" }}
                  tableData={deliveryPersons}
                  tableHeaders={["ID", "Name"]}
                  nameFields={["selectedId", "selectedTitle"]}
                  inputWidths={["30%", "70%" ]}
                  placeholders={["ID", "Name"]}
                  keys={["ID", "Title"]}
                  form={formData}
                />
                <FormErrorMessage>{errors.selectedId}</FormErrorMessage>
              </FormControl>
              <FormControl isRequired isInvalid={!!errors.deliveryDate}>
                <FormLabel>Delivery Date & Time</FormLabel>
                <Input
                  type="datetime-local"
                  name="deliveryDate"
                  value={formData.deliveryDate}
                  onChange={handleInputChange}
                  placeholder="Select date and time"
                  min={new Date().toISOString().slice(0, 16)}
                />
                <FormErrorMessage>{errors.deliveryDate}</FormErrorMessage>
              </FormControl>
              <FormControl isRequired isInvalid={!!errors.installerId}>
                <FormLabel>Select Installer</FormLabel>
                <ComboBox
                  target={true}
                  onChange={handleInputChange}
                  name={"installerItem"}
                  buttonWidth={"20px"}
                  styleButton={{ padding: "3px !important" }}
                  tableData={installers}
                  tableHeaders={["ID", "Name"]}
                  nameFields={["installerId", "installerName"]}
                  inputWidths={["30%", "70%" ]}
                  placeholders={["ID", "Name"]}
                  keys={["ID", "Title"]}
                  form={formData}
                />
                <FormErrorMessage>{errors.installerId}</FormErrorMessage>
              </FormControl>
              <FormControl isRequired isInvalid={!!errors.installerDate}>
                <FormLabel>Installation Date & Time</FormLabel>
                <Input
                  type="datetime-local"
                  name="installerDate"
                  value={formData.installerDate}
                  onChange={handleInputChange}
                  placeholder="Select date and time"
                  min={new Date().toISOString().slice(0, 16)}
                />
                <FormErrorMessage>{errors.installerDate}</FormErrorMessage>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button
              bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
              mr={3}
              type="submit"
              isLoading={isSubmitting}
              isDisabled={!isFormValid}
            >
              Assign
            </Button>
            <Button onClick={handleClose} colorScheme="red" isDisabled={isSubmitting}>Cancel</Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default AddDeliveryModal;
