const { sql, getPool } = require('../db');
const zlib = require('zlib');
const fs = require('fs');
const path = require('path');

const godownController = {
    createGodown: async (req, res) => {
        try {
            const {
                Id,
                Title,
                PH,
                CompanyAddress1,
                CompanyAddress2,
                CompanyPhone,
                CompanyFax,
                CompanyEmail,
                CompanyURL,
                CompanySTN,
                CompanyNTN,
                AllowNegativeBalances,
                Deactive,
                Prp_ID,
                chk_id,
                app_id,
                FinancialAcc_ID,
                ComputerName
            } = req.body;

            if (!Id || !Title) {
                return res.status(400).json({ message: 'Id and Title are required' });
            }

            const pool = await getPool();
            const request = new sql.Request(pool);

            // Normalize FinancialAcc_ID: empty string -> null
            const normalizedFinAcc = (FinancialAcc_ID && FinancialAcc_ID.toString().trim() !== '') ? FinancialAcc_ID : null;

            // If provided, verify exists in Coa32
            if (normalizedFinAcc) {
                const chkReq = new sql.Request(pool);
                chkReq.input('id', sql.VarChar(32), normalizedFinAcc);
                const chk = await chkReq.query('SELECT 1 FROM Coa32 WHERE id = @id');
                if (chk.recordset.length === 0) {
                    return res.status(400).json({ message: 'Invalid FinancialAcc_ID: not found in Coa32' });
                }
            }

            const insertQuery = `
                INSERT INTO Godown (
                    Id, Title, PH, CompanyAddress1, CompanyAddress2, CompanyPhone, CompanyFax,
                    CompanyEmail, CompanyURL, CompanySTN, CompanyNTN, AllowNegativeBalances,
                    Deactive, Prp_ID, CreationDate, chk_id, app_id, FinancialAcc_ID, ComputerName
                ) VALUES (
                    @Id, @Title, @PH, @CompanyAddress1, @CompanyAddress2, @CompanyPhone, @CompanyFax,
                    @CompanyEmail, @CompanyURL, @CompanySTN, @CompanyNTN, @AllowNegativeBalances,
                    @Deactive, @Prp_ID, GETDATE(), @chk_id, @app_id, @FinancialAcc_ID, @ComputerName
                );
            `;

            request.input('Id', sql.VarChar(8), Id);
            request.input('Title', sql.VarChar(70), Title);
            request.input('PH', sql.TinyInt, PH || 0);
            request.input('CompanyAddress1', sql.VarChar(250), CompanyAddress1 || null);
            request.input('CompanyAddress2', sql.VarChar(250), CompanyAddress2 || null);
            request.input('CompanyPhone', sql.VarChar(250), CompanyPhone || null);
            request.input('CompanyFax', sql.VarChar(250), CompanyFax || null);
            request.input('CompanyEmail', sql.VarChar(250), CompanyEmail || null);
            request.input('CompanyURL', sql.VarChar(250), CompanyURL || null);
            request.input('CompanySTN', sql.VarChar(250), CompanySTN || null);
            request.input('CompanyNTN', sql.VarChar(250), CompanyNTN || null);
            request.input('AllowNegativeBalances', sql.TinyInt, AllowNegativeBalances || 0);
            request.input('Deactive', sql.TinyInt, Deactive || 0);
            request.input('Prp_ID', sql.VarChar(8), Prp_ID || null);
            request.input('chk_id', sql.VarChar(8), chk_id || null);
            request.input('app_id', sql.VarChar(8), app_id || null);
            request.input('FinancialAcc_ID', sql.VarChar(32), normalizedFinAcc);
            request.input('ComputerName', sql.VarChar(120), ComputerName || null);

            await request.query(insertQuery);

            res.status(201).json({ message: 'Godown created successfully' });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getNextGodownId: async (req, res) => {
        try {
            const pool = await getPool();
            const request = new sql.Request(pool);
            const result = await request.query(`SELECT MAX(CAST(Id AS INT)) AS maxId FROM Godown WHERE ISNUMERIC(Id) = 1;`);
            const maxId = result.recordset[0]?.maxId || 0;
            const nextId = (maxId + 1).toString().padStart(3, '0');
            res.status(200).json({ nextId });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getGodownById: async (req, res) => {
        try {
            const { id } = req.params;
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('Id', sql.VarChar(8), id);
            const result = await request.query(`SELECT * FROM Godown WHERE Id = @Id`);
            if (result.recordset.length === 0) {
                return res.status(404).json({ message: 'Godown not found' });
            }
            res.status(200).json({ godown: result.recordset[0] });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getAllGodowns: async (req, res) => {
        try {
            const pool = await getPool();
            const request = new sql.Request(pool);
            const result = await request.query(`SELECT * FROM Godown ORDER BY Id`);
            res.status(200).json({ godowns: result.recordset });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    updateGodown: async (req, res) => {
        try {
            const { id } = req.params;
            const body = req.body || {};

            const fields = [
                'Title','PH','CompanyAddress1','CompanyAddress2','CompanyPhone','CompanyFax','CompanyEmail','CompanyURL',
                'CompanySTN','CompanyNTN','AllowNegativeBalances','Deactive','Prp_ID','chk_id','app_id','FinancialAcc_ID','ComputerName'
            ].filter(k => body[k] !== undefined);

            if (fields.length === 0) {
                return res.status(400).json({ message: 'No fields to update' });
            }

            const setClause = fields.map(k => `${k} = @${k}`).join(', ');
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('Id', sql.VarChar(8), id);
            for (const k of fields) {
                switch (k) {
                    case 'Title': request.input(k, sql.VarChar(70), body[k]); break;
                    case 'PH': request.input(k, sql.TinyInt, body[k]); break;
                    case 'AllowNegativeBalances':
                    case 'Deactive':
                        request.input(k, sql.TinyInt, body[k]);
                        break;
                    case 'Prp_ID':
                    case 'chk_id':
                    case 'app_id':
                        request.input(k, sql.VarChar(8), body[k]);
                        break;
                    case 'FinancialAcc_ID': {
                        const raw = body[k];
                        const normalized = (raw && raw.toString().trim() !== '') ? raw : null;
                        if (normalized) {
                            const chkReq = new sql.Request(pool);
                            chkReq.input('id', sql.VarChar(32), normalized);
                            const chk = await chkReq.query('SELECT 1 FROM Coa32 WHERE id = @id');
                            if (chk.recordset.length === 0) {
                                return res.status(400).json({ message: 'Invalid FinancialAcc_ID: not found in Coa32' });
                            }
                        }
                        request.input(k, sql.VarChar(32), normalized);
                        break;
                    }
                    case 'ComputerName':
                        request.input(k, sql.VarChar(120), body[k]);
                        break;
                    default:
                        request.input(k, sql.VarChar(250), body[k]);
                }
            }

            await request.query(`UPDATE Godown SET ${setClause} WHERE Id = @Id`);
            res.status(200).json({ message: 'Godown updated successfully' });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    uploadLogo: async (req, res) => {
        try {
            if (!req.file) {
                return res.status(400).json({ message: 'No image file provided' });
            }
            const { godownId } = req.body;
            if (!godownId) {
                return res.status(400).json({ message: 'godownId is required' });
            }

            const compressed = zlib.gzipSync(req.file.buffer);
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('Godown_ID', sql.VarChar(8), godownId);
            request.input('Logo', sql.VarBinary(sql.MAX), compressed);
            request.input('LogoSize', sql.Int, req.file.buffer.length);
            request.input('MimeType', sql.VarChar(50), req.file.mimetype);

            await request.query(`
                MERGE GodownLogo AS target
                USING (SELECT @Godown_ID AS Godown_ID) AS source
                ON (target.Godown_ID = source.Godown_ID)
                WHEN MATCHED THEN 
                    UPDATE SET Logo = @Logo, LogoSize = @LogoSize, MimeType = @MimeType
                WHEN NOT MATCHED THEN 
                    INSERT (Godown_ID, Logo, LogoSize, MimeType) VALUES (@Godown_ID, @Logo, @LogoSize, @MimeType);
            `);

            res.status(200).json({ message: 'Logo uploaded successfully' });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getLogo: async (req, res) => {
        try {
            const { id } = req.params;
            const fallback = (req.query.fallback === 'true');
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('Godown_ID', sql.VarChar(8), id);
            const result = await request.query(`SELECT TOP 1 Logo, MimeType FROM GodownLogo WHERE Godown_ID = @Godown_ID`);
            if (result.recordset.length === 0 || !result.recordset[0].Logo) {
                if (fallback) {
                    // Try to read frontend public icon and return it
                    const fallbackPath = path.resolve(__dirname, '../../frontend/public/icon512_rounded.png');
                    try {
                        const data = fs.readFileSync(fallbackPath);
                        res.setHeader('Content-Type', 'image/png');
                        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
                        return res.status(200).send(data);
                    } catch (e) {
                        // If file not found, return 404
                        return res.status(404).json({ message: 'Logo not found' });
                    }
                }
                return res.status(404).json({ message: 'Logo not found' });
            }
            const row = result.recordset[0];
            const decompressed = zlib.gunzipSync(row.Logo);
            res.setHeader('Content-Type', row.MimeType || 'image/png');
            res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
            return res.status(200).send(decompressed);
        } catch (error) {
            if (req.query.fallback === 'true') {
                try {
                    const fallbackPath = path.resolve(__dirname, '../../frontend/public/icon512_rounded.png');
                    const data = fs.readFileSync(fallbackPath);
                    res.setHeader('Content-Type', 'image/png');
                    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
                    return res.status(200).send(data);
                } catch (e) {}
            }
            return res.status(500).json({ message: error.message });
        }
    }
};

module.exports = godownController;


