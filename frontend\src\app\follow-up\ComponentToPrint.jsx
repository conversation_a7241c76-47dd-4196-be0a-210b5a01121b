import React, { useState, useEffect } from 'react';
import { Box, Text, Table, Thead, Tbody, Tr, Th, Td, Flex, Spacer, Grid } from "@chakra-ui/react";
import dayjs from "dayjs";
import zip from '@assets/assets/imgs/zipPayLogo.jpg';
import afterpay from '@assets/assets/imgs/afterPayLogo.jpg';
import Image from "next/image";

const ComponentToPrint = ({ data }) => {
  const [logoSrc, setLogoSrc] = useState('');
  const logoImage = `${process.env.NEXT_PUBLIC_BASE_URL ? process.env.NEXT_PUBLIC_BASE_URL : ''}godown/${data?.location || 'EAM'}/logo?fallback=true`;

  useEffect(() => {
    // Preload the logo image to ensure it's available for PDF generation
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      setLogoSrc(logoImage);
    };
    img.onerror = () => {
      console.warn('Failed to load logo image:', logoImage);
      setLogoSrc(''); // Set empty to hide broken image
    };
    img.src = logoImage;
  }, [logoImage]);
  return (
    <Box p={8} maxW="800px" mx="auto" bg="white">
      {/* Header */}
      <Flex justify="space-between" align="center" mb={6} borderBottom="2px solid" borderColor="gray.300" pb={4}>
        <Box>
          {logoSrc && (
            <img
              src={logoSrc}
              alt="Eco Assets Manager"
              style={{ height: "100px", width: "auto" }}
              crossOrigin="anonymous"
            />
          )}
        </Box>
        <Box textAlign="right">
          <Text fontSize="3xl" fontWeight="bold" color="black">QUOTATION</Text>
          <Text color="red.500" fontWeight="bold" fontSize="lg">{data?.location || 'EAM'} {data?.vno || 'N/A'}</Text>
        </Box>
      </Flex>

      {/* Company and Client Info */}
      <Grid templateColumns="1fr 1fr" gap={8} mb={6}>
        <Box>
          <Text fontSize="sm" color="gray.600" mb={2} textTransform="uppercase">INVOICE TO</Text>
          <Text fontWeight="bold" fontSize="lg">{data?.clientTitle || 'No Client'}</Text>
          <Text>{data?.email || 'No Email'}</Text>
          <Text>{data?.phoneNumber || 'No Phone'}</Text>
          <Text>{data?.address || 'No Address'}</Text>
        </Box>
        <Box textAlign="right">
          <Text fontWeight="bold" fontSize="lg">{data?.locationTitle || 'Eco Assets Manager PTY LTD'}</Text>
          <Text fontSize="sm" color="gray.600">{data?.locationABN || 'No ABN'}</Text>
          <Text fontSize="sm" color="gray.600">{data?.locationAddress || 'No Address'}</Text>
          <Text fontSize="sm" color="gray.600">{data?.locationCity || 'No City'}</Text>
          <Text fontSize="sm" color="gray.600">{data?.locationPhone || 'No Phone'}</Text>
        </Box>
      </Grid>

      {/* Products Table */}
      <Table variant="simple" mb={6}>
        <Thead>
          <Tr bg="#2d6651 !important">
            <Th color="white" borderColor="white">Products</Th>
            <Th color="white" borderColor="white" textAlign="center">Quantity</Th>
            <Th color="white" borderColor="white" textAlign="right">Price</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data?.items?.length > 0 ? data.items.map((item, index) => (
            <Tr key={index}>
              <Td borderColor="gray.300">{item.Item_Title || item.details || ''}</Td>
              <Td borderColor="gray.300" textAlign="center">{item.Qty || ''}</Td>
              <Td borderColor="gray.300" textAlign="right">{item.Rate || item.Total ? `$${parseFloat(item.Rate || item.Total).toFixed(2)}` : ''}</Td>
            </Tr>
          )) : (
            <Tr>
              <Td borderColor="gray.300" colSpan={3} textAlign="center" color="gray.500">No items available</Td>
            </Tr>
          )}
        </Tbody>
      </Table>

      <Text fontSize="sm" color="gray.600" mb={6}>* Additional charges may apply subject to installer's assessment *</Text>

      {/* Bottom Section */}
      <Grid templateColumns="1fr 1fr" gap={8}>
        {/* Left Side - Payment Info */}
        <Box display={'flex'} flexDirection={'column'} alignItems={'center'} justifyContent={'center'}>
          <Text fontSize="lg" fontWeight="bold" color="gray.700" mb={4}>FINANCING AVAILABLE</Text>

          <Box>
            <Flex align="center" mb={4} mt={2} gap={4}>
              <Image src={zip} alt="Zip" style={{ width: "100px", objectFit: "contain" }} />
              <Image src={afterpay} alt="Afterpay" style={{ width: "100px", objectFit: "contain" }} />
            </Flex>
            <Text fontSize="lg" fontWeight="bold" color="gray.700" mb={2}>PAYMENT METHOD</Text>
            <Text fontSize="sm" color="gray.600">Bank Name: Bank of Melbourne</Text>
            <Text fontSize="sm" color="gray.600">BSB: 193879</Text>
            <Text fontSize="sm" color="gray.600">Account #: *********</Text>
            <Text fontSize="sm" color="gray.600" mt={2}>Please deposit 10% and balance must be paid on installation day.</Text>
          </Box>
        </Box>

        {/* Right Side - Totals */}
        <Box>
          <Flex justify="space-between" mb={2}>
            <Text>Subtotal Incl. GST</Text>
            <Text fontWeight="bold">{data?.totalAmount ? `$${parseFloat(data.totalAmount).toFixed(2)}` : 'N/A'}</Text>
          </Flex>
          <Flex justify="space-between" mb={2}>
            <Text>EAM discount</Text>
            <Text fontWeight="bold">{data?.discountAmount ? `$${parseFloat(data.discountAmount).toFixed(2)}` : 'N/A'}</Text>
          </Flex>
          <Flex justify="space-between" mb={2}>
            <Text>Tax Included ({((data?.salesTaxR || 0) + (data?.salesTaxA || 0))}%)</Text>
            <Text fontWeight="bold">{data?.sTaxAmount ? `$${parseFloat(data.sTaxAmount).toFixed(2)}` : 'N/A'}</Text>
          </Flex>

          <Box borderTop="1px solid" borderColor="gray.300" pt={2} mt={4}>
            <Flex justify="space-between" mb={2}>
              <Text color="#2d6651" fontWeight="bold">VEEC DISCOUNT</Text>
              <Text color="#2d6651" fontWeight="bold">{data?.veecDiscount ? `$${parseFloat(data.veecDiscount).toFixed(2)}` : 'N/A'}</Text>
            </Flex>
            <Flex justify="space-between" mb={2}>
              <Text color="#2d6651" fontWeight="bold">STC DISCOUNT</Text>
              <Text color="#2d6651" fontWeight="bold">N/A</Text>
            </Flex>
            <Flex justify="space-between" mb={4}>
              <Text color="#2d6651" fontWeight="bold">SOLARVIC REBATE</Text>
              <Text color="#2d6651" fontWeight="bold">N/A</Text>
            </Flex>

            <Flex justify="space-between" align="center" bg="gray.100" p={3} borderRadius="md">
              <Box>
                <Text textAlign="left" fontSize="lg" fontWeight="bold">Total out of pocket</Text>
                <Text textAlign="right" fontSize="sm" color="gray.600">incl. GST</Text>
              </Box>
              <Box textAlign="right">
                <Text fontSize="xl" fontWeight="bold">{data?.netPayableAmt ? `$${parseFloat(data.netPayableAmt).toFixed(2)}` : data?.netAmount ? `$${parseFloat(data.netAmount).toFixed(2)}` : 'N/A'}</Text>
              </Box>
            </Flex>
          </Box>
        </Box>
      </Grid>

      {/* Signature Section */}
      <Flex justify="space-between" mt={16} pt={8} borderColor="gray.300">
        <Box textAlign="center" width="200px">
          <Box borderBottom="1px solid" borderColor="gray.400" height="60px" mb={2} display="flex" alignItems="center" justifyContent="center">
            {data?.signature && (
              <img src={data.signature} alt="Customer Signature" style={{ maxHeight: '60px', maxWidth: '100%', objectFit: 'contain' }} />
            )}
          </Box>
          <Text fontWeight="bold">Customer Signature</Text>
        </Box>
        <Box textAlign="center" width="200px">
          <Box borderBottom="1px solid" borderColor="gray.400" height="60px" mb={2} display="flex" alignItems="center" justifyContent="center">
            <Text>{dayjs().format("DD-MMM-YYYY")}</Text>
          </Box>
          <Text fontWeight="bold">Date</Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default ComponentToPrint;