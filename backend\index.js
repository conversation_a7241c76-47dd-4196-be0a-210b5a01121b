const fs = require("fs");
const path = require("path");
const https = require("https");
const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");
const helmet = require("helmet");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
const socketIo = require("socket.io");
const ioEmitter = require("./modules/ioEmitter");

require("dotenv").config();

const app = express();
const PORT = process.env.PORT || 3000;

let server;
let isHttps = false;
const isProduction = process.env.NODE_ENV === "production";

if (isProduction) {
    try {
        const certPath = "C:/ssl/ecoassetmanager.nexsoltech.biz";

        const privateKey = fs.readFileSync(path.join(certPath, "private.key"), "utf8");
        const certificate = fs.readFileSync(path.join(certPath, "certificate.crt"), "utf8");

        const caPath = path.join(certPath, "ca_bundle.crt");
        let credentials;

        if (fs.existsSync(caPath)) {
            const ca = fs.readFileSync(caPath, "utf8");
            credentials = { key: privateKey, cert: certificate, ca };
            console.log("🔐 Using private.key, certificate.crt, and ca_bundle.crt for HTTPS");
        } else {
            credentials = { key: privateKey, cert: certificate };
            console.log("⚠️ CA bundle missing — using only certificate.crt and private.key");
        }

        server = https.createServer(credentials, app);
        isHttps = true;
    } catch (error) {
        console.error("❌ SSL setup failed. Falling back to HTTP:", error.message);
        server = require("http").createServer(app);
    }
} else {
    server = require("http").createServer(app);
    console.log("🔧 Development: Running HTTP server");
}

const io = new socketIo.Server(server, { cors: { origin: "*" } });

const corsOptions = {
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept"],
    credentials: true,
    preflightContinue: false,
};

app.use(cors(corsOptions));
app.use(helmet({ contentSecurityPolicy: false, crossOriginEmbedderPolicy: false }));
app.use(compression());

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: "Too many requests from this IP, please try again later.",
});
app.use(limiter);
app.use(bodyParser.json());

// Swagger
const swaggerUi = require("swagger-ui-express");
const specs = require("./config/swagger");
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    customSiteTitle: "API Documentation",
}));

// Routes
app.use("/api/user", require("./routes/user"));
app.use("/api/clientorders", require("./routes/clientOrder"));
app.use("/api/menus", require("./routes/menu"));
app.use("/api/cashVoucher", require("./routes/cashVoucher"));
app.use("/api/bankVoucher", require("./routes/bankVoucher"));
app.use("/api/goodReceiptNotes", require("./routes/goodReceiptNote"));
app.use("/api/residentRegistrationForm", require("./routes/residentRegistrationForm"));
app.use("/api/storeIssuanceNote", require("./routes/storeIssuanceNote"));
app.use("/api/getRecords", require("./routes/getRecords"));
app.use("/api/offer", require("./routes/offer"));
app.use("/api/recoveryFollowUps", require("./routes/recoveryFollowUps"));
app.use("/api/deliveryChallan", require("./routes/deliveryChallan"));
app.use("/api/salesTaxInvoice", require("./routes/salesTaxInvoice"));
app.use("/api/generalLedger", require("./routes/generalLedger"));
app.use("/api/client", require("./routes/client"));
app.use("/api/email", require("./routes/email"));
app.use("/api/purpose", require("./routes/purpose"));
app.use("/api/notifications", require("./routes/notifications"));
app.use("/api/product", require("./routes/product"));
app.use("/api/employee", require("./routes/employee"));
app.use('/api/godown', require('./routes/godown'));

// Error handling
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: "Something went wrong, please try again later." });
});

// Socket.IO
io.on("connection", (socket) => {
    console.log("Socket.IO client connected:", socket.id);
    socket.on("disconnect", (reason) => {
        console.log("Socket.IO client disconnected:", socket.id, "Reason:", reason);
    });
});

ioEmitter.on("notifyAll", (event) => {
    console.log("Emitting event:", event.name, "to all clients");
    io.emit(event.name, event.data);
});

// Start server
server.listen(PORT, "0.0.0.0", () => {
    const protocol = isHttps ? "HTTPS" : "HTTP";
    const baseUrl = isProduction
        ? `${protocol === "HTTPS" ? "https" : "http"}://ecoassetmanager.nexsoltech.biz:${PORT}`
        : `http://localhost:${PORT}`;

    console.log(`✅ ${protocol} server running at ${baseUrl}`);
    console.log(`📘 Swagger docs at ${baseUrl}/api-docs`);
    console.log(`📡 Socket.IO listening on ${protocol}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
});
