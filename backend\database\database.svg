<svg aria-roledescription="er" role="graphics-document document" viewBox="0 0 5825.1162109375 2480" style="max-width: 5825.1162109375px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75">
	<style>
		#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .error-icon{fill:#a44141;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-thickness-normal{stroke-width:1px;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .marker.cross{stroke:lightgrey;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 p{margin:0;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .entityBox{fill:#1f2020;stroke:#ccc;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .attributeBoxOdd{fill:hsl(0, 0%, 32%);stroke:#ccc;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .attributeBoxEven{fill:hsl(0, 0%, 22%);stroke:#ccc;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .relationshipLabelBox{fill:hsl(20, 1.5873015873%, 12.3529411765%);opacity:0.7;background-color:hsl(20, 1.5873015873%, 12.3529411765%);}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .relationshipLabelBox rect{opacity:0.5;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .relationshipLine{stroke:lightgrey;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 .entityTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 #MD_PARENT_START{fill:#f5f5f5!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 #MD_PARENT_END{fill:#f5f5f5!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d020aed5-b257-4bb3-8da8-ed6608202e75 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}
	</style>
	<g>
	</g>
	<defs>
		<marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="0" id="MD_PARENT_START">
			<path d="M 18,7 L9,13 L1,7 L9,1 Z">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="19" id="MD_PARENT_END">
			<path d="M 18,7 L9,13 L1,7 L9,1 Z">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="0" id="ONLY_ONE_START">
			<path d="M9,0 L9,18 M15,0 L15,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="18" id="ONLY_ONE_END">
			<path d="M3,0 L3,18 M9,0 L9,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="0" id="ZERO_OR_ONE_START">
			<circle r="6" cy="9" cx="21" fill="white" stroke="gray">
			</circle>
			<path d="M9,0 L9,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="30" id="ZERO_OR_ONE_END">
			<circle r="6" cy="9" cx="9" fill="white" stroke="gray">
			</circle>
			<path d="M21,0 L21,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="18" id="ONE_OR_MORE_START">
			<path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="27" id="ONE_OR_MORE_END">
			<path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="18" id="ZERO_OR_MORE_START">
			<circle r="6" cy="18" cx="48" fill="white" stroke="gray">
			</circle>
			<path d="M0,18 Q18,0 36,18 Q18,36 0,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<defs>
		<marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="39" id="ZERO_OR_MORE_END">
			<circle r="6" cy="18" cx="9" fill="white" stroke="gray">
			</circle>
			<path d="M21,18 Q39,0 57,18 Q39,36 21,18" fill="none" stroke="gray">
			</path>
		</marker>
	</defs>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ONLY_ONE_END)" d="M965.05,1471.536L837.447,1505.613C709.845,1539.691,454.64,1607.845,315.029,1661.256C175.419,1714.667,151.402,1753.333,139.394,1772.667L127.386,1792" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M1133.821,1465.618L1311.868,1500.682C1489.915,1535.746,1846.009,1605.873,1879.494,1665.402C1912.979,1724.93,1623.854,1773.86,1479.292,1798.326L1334.73,1822.791" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M3593.627,1505L3568.557,1533.5C3543.488,1562,3493.349,1619,3340.636,1671.933C3187.922,1724.866,2932.635,1773.731,2804.991,1798.164L2677.347,1822.597" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_ONE_END)" d="M2546.823,1926L2539.37,1938C2531.917,1950,2517.01,1974,2509.556,1994.333C2502.103,2014.667,2502.103,2031.333,2502.103,2039.667L2502.103,2048" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M2677.347,1840.668L3215.308,1866.89C3753.27,1893.112,4829.193,1945.556,4983.27,1999.409C5137.347,2053.262,4369.579,2108.525,3985.695,2136.156L3601.81,2163.787" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M2362.048,204.721L2414.721,230.268C2467.394,255.814,2572.74,306.907,2625.413,340.787C2678.087,374.667,2678.087,391.333,2678.087,399.667L2678.087,408" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2362.048,182.765L2492.726,211.971C2623.405,241.177,2884.762,299.588,3015.44,361.127C3146.119,422.667,3146.119,487.333,3146.119,552C3146.119,616.667,3146.119,681.333,3146.119,753.333C3146.119,825.333,3146.119,904.667,3146.119,984C3146.119,1063.333,3146.119,1142.667,3146.119,1220.167C3146.119,1297.667,3146.119,1373.333,3146.119,1449C3146.119,1524.667,3146.119,1600.333,3067.99,1661.289C2989.861,1722.244,2833.604,1768.488,2755.475,1791.61L2677.347,1814.732" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2362.048,173.001L2649.664,203.834C2937.281,234.667,3512.513,296.334,3800.13,359.5C4087.746,422.667,4087.746,487.333,4087.746,552C4087.746,616.667,4087.746,681.333,4087.746,753.333C4087.746,825.333,4087.746,904.667,4087.746,984C4087.746,1063.333,4087.746,1142.667,4087.746,1220.167C4087.746,1297.667,4087.746,1373.333,4087.746,1449C4087.746,1524.667,4087.746,1600.333,4062.87,1659.333C4037.994,1718.333,3988.242,1760.667,3963.366,1781.833L3938.49,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2194.125,207.322L2145.455,232.435C2096.784,257.548,1999.444,307.774,1950.773,365.22C1902.103,422.667,1902.103,487.333,1902.103,552C1902.103,616.667,1902.103,681.333,1902.103,753.333C1902.103,825.333,1902.103,904.667,1902.103,984C1902.103,1063.333,1902.103,1142.667,1774.056,1216.422C1646.009,1290.178,1389.915,1358.356,1261.868,1392.445L1133.821,1426.535" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2194.125,185.812L2083.657,214.51C1973.188,243.208,1752.251,300.604,1641.783,361.635C1531.314,422.667,1531.314,487.333,1531.314,552C1531.314,616.667,1531.314,681.333,1531.314,753.333C1531.314,825.333,1531.314,904.667,1531.314,984C1531.314,1063.333,1531.314,1142.667,1465.065,1213.541C1398.816,1284.416,1266.319,1346.832,1200.07,1378.04L1133.821,1409.248" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2194.125,177.82L2011.677,207.85C1829.229,237.88,1464.332,297.94,1281.884,360.303C1099.435,422.667,1099.435,487.333,1099.435,552C1099.435,616.667,1099.435,681.333,1099.435,753.333C1099.435,825.333,1099.435,904.667,1099.435,984C1099.435,1063.333,1099.435,1142.667,1093.965,1207.167C1088.495,1271.667,1077.556,1321.333,1072.086,1346.167L1066.616,1371" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2695.913,696L2696.945,704.333C2697.976,712.667,2700.04,729.333,2701.071,777.333C2702.103,825.333,2702.103,904.667,2702.103,984C2702.103,1063.333,2702.103,1142.667,2702.103,1220.167C2702.103,1297.667,2702.103,1373.333,2702.103,1449C2702.103,1524.667,2702.103,1600.333,2694.649,1650.167C2687.196,1700,2672.289,1724,2664.836,1736L2657.382,1748" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2590.055,581.65L2508.73,609.042C2427.404,636.434,2264.754,691.217,2183.428,758.275C2102.103,825.333,2102.103,904.667,2102.103,984C2102.103,1063.333,2102.103,1142.667,2102.103,1220.167C2102.103,1297.667,2102.103,1373.333,2102.103,1449C2102.103,1524.667,2102.103,1600.333,1974.207,1662.354C1846.312,1724.374,1590.521,1772.748,1462.626,1796.935L1334.73,1821.122" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2766.118,604.708L2805.449,628.256C2844.78,651.805,2923.441,698.903,2959.271,730.785C2995.1,762.667,2988.097,779.333,2984.596,787.667L2981.094,796" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M2997.852,995.76L3304.86,1033.467C3611.868,1071.173,4225.884,1146.587,4532.892,1192.627C4839.9,1238.667,4839.9,1255.333,4839.9,1263.667L4839.9,1272" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2806.354,1059.961L2772.312,1086.968C2738.27,1113.974,2670.186,1167.987,2636.145,1232.827C2602.103,1297.667,2602.103,1373.333,2602.103,1449C2602.103,1524.667,2602.103,1600.333,2602.103,1650.167C2602.103,1700,2602.103,1724,2602.103,1736L2602.103,1748" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2806.354,1009.32L2672.312,1044.767C2538.27,1080.214,2270.186,1151.107,2136.145,1224.387C2002.103,1297.667,2002.103,1373.333,2002.103,1449C2002.103,1524.667,2002.103,1600.333,1890.874,1662.001C1779.645,1723.669,1557.188,1771.339,1445.959,1795.174L1334.73,1819.008" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2997.852,1004.991L3162.834,1041.159C3327.817,1077.327,3657.781,1149.664,3822.764,1223.665C3987.746,1297.667,3987.746,1373.333,3987.746,1449C3987.746,1524.667,3987.746,1600.333,3976.017,1659.333C3964.288,1718.333,3940.83,1760.667,3929.101,1781.833L3917.372,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2997.852,999.213L3231.554,1036.344C3465.256,1073.475,3932.66,1147.738,4166.363,1222.702C4400.065,1297.667,4400.065,1373.333,4400.065,1449C4400.065,1524.667,4400.065,1600.333,4391.872,1659.333C4383.679,1718.333,4367.293,1760.667,4359.1,1781.833L4350.907,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2806.354,995.977L2505.201,1033.648C2204.047,1071.318,1601.741,1146.659,1306.058,1209.163C1010.375,1271.667,1021.315,1321.333,1026.785,1346.167L1032.255,1371" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4942.966,1473.239L5086.658,1507.033C5230.349,1540.826,5517.733,1608.413,5661.424,1669.04C5805.116,1729.667,5805.116,1783.333,5805.116,1837C5805.116,1890.667,5805.116,1944.333,5437.898,1998.753C5070.681,2053.172,4336.246,2108.344,3969.028,2135.93L3601.81,2163.516" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4736.834,1491.372L4661.986,1522.144C4587.138,1552.915,4437.442,1614.457,4369.168,1666.395C4300.893,1718.333,4314.04,1760.667,4320.614,1781.833L4327.187,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4736.834,1473.572L4595.319,1507.31C4453.804,1541.048,4170.775,1608.524,4030.679,1663.429C3890.582,1718.333,3893.418,1760.667,3894.836,1781.833L3896.254,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4736.834,1459.008L4364.378,1495.173C3991.923,1531.338,3247.013,1603.669,2882.011,1651.835C2517.01,1700,2531.917,1724,2539.37,1736L2546.823,1748" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4736.834,1557.719L4718.145,1577.432C4699.456,1597.146,4662.079,1636.573,4635.841,1677.453C4609.603,1718.333,4594.505,1760.667,4586.956,1781.833L4579.406,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4802.486,1626L4800.725,1634.333C4798.963,1642.667,4795.44,1659.333,4793.679,1688.833C4791.917,1718.333,4791.917,1760.667,4791.917,1781.833L4791.917,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4942.966,1571.324L4957.665,1588.77C4972.365,1606.216,5001.764,1641.108,5016.463,1679.721C5031.163,1718.333,5031.163,1760.667,5031.163,1781.833L5031.163,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M4942.966,1503.001L4997.997,1531.834C5053.029,1560.667,5163.091,1618.334,5218.123,1668.333C5273.154,1718.333,5273.154,1760.667,5273.154,1781.833L5273.154,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ONLY_ONE_START)" marker-end="url(#ZERO_OR_MORE_END)" d="M2489.78,1465.709L2673.69,1500.757C2857.601,1535.806,3225.422,1605.903,3409.332,1652.951C3593.243,1700,3593.243,1724,3593.243,1736L3593.243,1748" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2402.103,1593L2402.103,1606.833C2402.103,1620.667,2402.103,1648.333,2422.896,1678.905C2443.688,1709.476,2485.274,1742.952,2506.066,1759.69L2526.859,1776.429" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2314.426,1471.081L2178.816,1505.234C2043.207,1539.388,1771.988,1607.694,1608.705,1659.668C1445.423,1711.643,1390.076,1747.286,1362.403,1765.107L1334.73,1782.929" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2314.426,1468.876L2162.15,1503.397C2009.873,1537.917,1705.321,1606.959,1542.039,1653.293C1378.756,1699.627,1356.743,1723.254,1345.737,1735.068L1334.73,1746.882" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2314.426,1467.071L2145.483,1501.893C1976.54,1536.714,1638.655,1606.357,1467.124,1649.512C1295.593,1692.667,1290.417,1709.333,1287.829,1717.667L1285.241,1726" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3593.243,1926L3593.243,1938C3593.243,1950,3593.243,1974,3589.476,1994.333C3585.709,2014.667,3578.175,2031.333,3574.408,2039.667L3570.641,2048" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3304.138,1466.55L3137.132,1501.458C2970.126,1536.367,2636.115,1606.183,2506.568,1661.195C2377.022,1716.206,2451.94,1756.413,2489.4,1776.516L2526.859,1796.619" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3472.06,1472.835L3591.341,1506.695C3710.622,1540.556,3949.184,1608.278,4088.186,1663.306C4227.187,1718.333,4266.628,1760.667,4286.349,1781.833L4306.069,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3472.06,1497.177L3524.002,1526.981C3575.943,1556.785,3679.825,1616.392,3746.862,1667.363C3813.899,1718.333,3844.091,1760.667,3859.187,1781.833L3874.283,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3304.138,1457.713L2953.576,1494.095C2603.015,1530.476,1901.892,1603.238,1553.919,1647.952C1205.945,1692.667,1211.121,1709.333,1213.709,1717.667L1216.297,1726" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3472.06,1466.14L3643.394,1501.117C3814.728,1536.093,4157.397,1606.047,4337.567,1662.19C4517.738,1718.333,4535.412,1760.667,4544.249,1781.833L4553.086,1803" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3515.493,2292L3515.493,2300.333C3515.493,2308.667,3515.493,2325.333,3515.493,2342C3515.493,2358.667,3515.493,2375.333,3515.493,2383.667L3515.493,2392" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3464.222,2048L3460.72,2039.667C3457.218,2031.333,3450.214,2014.667,3319.068,1981.9C3187.922,1949.134,2932.635,1900.269,2804.991,1875.836L2677.347,1851.403" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M3429.175,2145.008L3344.553,2120.506C3259.931,2096.005,3090.688,2047.003,2965.383,2001.99C2840.078,1956.978,2758.713,1915.957,2718.03,1895.446L2677.347,1874.935" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M1431.314,566.175L1676.446,596.146C1921.577,626.117,2411.84,686.058,2656.971,724.363C2902.103,762.667,2902.103,779.333,2902.103,787.667L2902.103,796" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M1199.435,588.517L1116.102,614.764C1032.769,641.011,866.102,693.506,782.769,759.419C699.435,825.333,699.435,904.667,699.435,984C699.435,1063.333,699.435,1142.667,699.435,1220.167C699.435,1297.667,699.435,1373.333,699.435,1449C699.435,1524.667,699.435,1600.333,777.331,1660.914C855.226,1721.494,1011.017,1766.988,1088.912,1789.735L1166.808,1812.482" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M1199.435,583.416L1099.435,610.514C999.435,637.611,799.435,691.805,699.435,758.569C599.435,825.333,599.435,904.667,599.435,984C599.435,1063.333,599.435,1142.667,599.435,1220.167C599.435,1297.667,599.435,1373.333,599.435,1449C599.435,1524.667,599.435,1600.333,693.997,1661.541C788.559,1722.749,977.684,1769.497,1072.246,1792.872L1166.808,1816.246" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M1802.103,567.263L1968.77,597.053C2135.436,626.842,2468.77,686.421,2638.938,724.544C2809.106,762.667,2816.108,779.333,2819.61,787.667L2823.111,796" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M1631.314,565.61L1442.668,595.675C1254.021,625.74,876.728,685.87,688.082,755.602C499.435,825.333,499.435,904.667,499.435,984C499.435,1063.333,499.435,1142.667,499.435,1220.167C499.435,1297.667,499.435,1373.333,499.435,1449C499.435,1524.667,499.435,1600.333,610.664,1662.001C721.893,1723.669,944.35,1771.339,1055.579,1795.174L1166.808,1819.008" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M5639.441,1527L5639.441,1551.833C5639.441,1576.667,5639.441,1626.333,5638.109,1663.167C5636.776,1700,5634.111,1724,5632.779,1736L5631.446,1748" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2501.212,991.758L2991.438,1030.132C3481.663,1068.505,4462.115,1145.253,4952.34,1221.46C5442.566,1297.667,5442.566,1373.333,5442.566,1449C5442.566,1524.667,5442.566,1600.333,5458.473,1652.475C5474.38,1704.616,5506.195,1733.232,5522.102,1747.539L5538.009,1761.847" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2501.212,991.286L3024.25,1029.739C3547.289,1068.191,4593.365,1145.095,5116.403,1208.381C5639.441,1271.667,5639.441,1321.333,5639.441,1346.167L5639.441,1371" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2302.994,999.697L2069.067,1036.748C1835.141,1073.798,1367.288,1147.899,1149.771,1209.783C932.255,1271.667,965.074,1321.333,981.484,1346.167L997.893,1371" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M2302.994,995.778L1985.734,1033.482C1668.474,1071.186,1033.955,1146.593,716.695,1222.13C399.435,1297.667,399.435,1373.333,399.435,1449C399.435,1524.667,399.435,1600.333,527.331,1662.354C655.226,1724.374,911.017,1772.748,1038.912,1796.935L1166.808,1821.122" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M540.576,561.847L850.83,592.539C1161.085,623.232,1781.594,684.616,2091.848,734.641C2402.103,784.667,2402.103,823.333,2402.103,842.667L2402.103,862" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M540.576,605.882L583.719,629.235C626.862,652.588,713.149,699.294,756.292,762.314C799.435,825.333,799.435,904.667,799.435,984C799.435,1063.333,799.435,1142.667,827.038,1207.396C854.64,1272.126,909.845,1322.252,937.447,1347.315L965.05,1372.378" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M341.491,608.532L301.148,631.443C260.806,654.355,180.12,700.177,139.778,762.755C99.435,825.333,99.435,904.667,99.435,984C99.435,1063.333,99.435,1142.667,99.435,1220.167C99.435,1297.667,99.435,1373.333,99.435,1449C99.435,1524.667,99.435,1600.333,99.435,1657.5C99.435,1714.667,99.435,1753.333,99.435,1772.667L99.435,1792" class="er relationshipLine">
	</path>
	<path style="stroke: gray; fill: none;" marker-start="url(#ZERO_OR_MORE_START)" marker-end="url(#ONLY_ONE_END)" d="M376.073,641L363.3,658.5C350.527,676,324.981,711,312.208,768.167C299.435,825.333,299.435,904.667,299.435,984C299.435,1063.333,299.435,1142.667,299.435,1220.167C299.435,1297.667,299.435,1373.333,299.435,1449C299.435,1524.667,299.435,1600.333,443.997,1662.632C588.559,1724.93,877.684,1773.86,1022.246,1798.326L1166.808,1822.791" class="er relationshipLine">
	</path>
	<g transform="translate(965.0495910644531,1371 )" id="entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e">
		<rect height="156" width="168.77122497558594" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(84.38561248779297,12)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e" class="er entityLabel">
			users
		</text>
		<rect height="22" width="74.83918762207031" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="71.24435424804688" y="24" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,35)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-1-name" class="er entityLabel">
			id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="146.0835418701172" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,35)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.83918762207031" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="71.24435424804688" y="46" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,57)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-2-name" class="er entityLabel">
			user_name
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="146.0835418701172" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,57)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-3-type" class="er entityLabel">
			NVARCHAR100
		</text>
		<rect height="22" width="71.24435424804688" y="68" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,79)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-3-name" class="er entityLabel">
			EmailAddress
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="146.0835418701172" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,79)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-4-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="71.24435424804688" y="90" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,101)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-4-name" class="er entityLabel">
			RoleID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="146.0835418701172" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,101)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-4-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.83918762207031" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-5-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="71.24435424804688" y="112" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,123)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-5-name" class="er entityLabel">
			CreatedAt
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="146.0835418701172" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,123)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-6-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="71.24435424804688" y="134" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,145)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-6-name" class="er entityLabel">
			UpdatedAt
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="146.0835418701172" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(151.0835418701172,145)" y="0" x="0" id="text-entity-users-33ae1e26-be31-5c1d-bf65-e859e9d0308e-attr-6-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(20,1792 )" id="entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f">
		<rect height="90" width="158.8704071044922" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(79.4352035522461,12)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f" class="er entityLabel">
			User_Role
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="61.97959899902344" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-1-name" class="er entityLabel">
			RoleID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="136.18272399902344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.18272399902344,35)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="61.97959899902344" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-2-name" class="er entityLabel">
			RoleName
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="136.18272399902344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.18272399902344,57)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-3-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="61.97959899902344" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-3-name" class="er entityLabel">
			Description
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="136.18272399902344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.18272399902344,79)" y="0" x="0" id="text-entity-UserRole-8764e232-769c-5852-80f9-607ed47e854f-attr-3-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(1166.8078231811523,1726 )" id="entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134">
		<rect height="222" width="167.92239379882812" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(83.96119689941406,12)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134" class="er entityLabel">
			EmployeeDetails
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,35)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="75.74453735351562" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,57)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-3-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-3-name" class="er entityLabel">
			Desig_ID
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,79)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-3-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-4-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-4-name" class="er entityLabel">
			Deptt_ID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,101)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-4-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-5-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-5-name" class="er entityLabel">
			Shift_ID
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,123)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-5-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-6-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-6-name" class="er entityLabel">
			Exp_ID
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,145)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-6-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-7-name" class="er entityLabel">
			Control_ID
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,167)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-8-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="178" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,189)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-8-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,189)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-9-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="200" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,211)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-9-name" class="er entityLabel">
			Project_ID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,211)" y="0" x="0" id="text-entity-EmployeeDetails-c7a28b01-ee14-51b2-83a1-cc87cf6d1134-attr-9-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(3572.060375213623,1393 )" id="entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8">
		<rect height="112" width="141.65151977539062" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(70.82575988769531,12)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8" class="er entityLabel">
			coa3
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="49.473663330078125" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-1-name" class="er entityLabel">
			id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="118.96383666992188" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(123.96383666992188,35)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-2-type" class="er entityLabel">
			NVARCHAR2
		</text>
		<rect height="22" width="49.473663330078125" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-2-name" class="er entityLabel">
			atp2_ID
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="118.96383666992188" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(123.96383666992188,57)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-3-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="49.473663330078125" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-3-name" class="er entityLabel">
			title
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="118.96383666992188" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(123.96383666992188,79)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-4-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="49.473663330078125" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-4-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="118.96383666992188" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(123.96383666992188,101)" y="0" x="0" id="text-entity-coa3-556d0835-549e-57cd-8de3-f22845e36fd8-attr-4-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(2526.8588485717773,1748 )" id="entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69">
		<rect height="178" width="150.48797607421875" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(75.24398803710938,12)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69" class="er entityLabel">
			coa32
		</text>
		<rect height="22" width="74.83918762207031" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="52.96110534667969" y="24" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,35)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-1-name" class="er entityLabel">
			id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="127.80029296875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,35)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.83918762207031" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="52.96110534667969" y="46" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,57)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-2-name" class="er entityLabel">
			title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="127.80029296875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,57)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-3-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="52.96110534667969" y="68" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,79)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-3-name" class="er entityLabel">
			Opn_Amt
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="127.80029296875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,79)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-4-type" class="er entityLabel">
			NVARCHAR250
		</text>
		<rect height="22" width="52.96110534667969" y="90" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,101)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-4-name" class="er entityLabel">
			GroupID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="127.80029296875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,101)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-5-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="52.96110534667969" y="112" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,123)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-5-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="127.80029296875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,123)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-6-type" class="er entityLabel">
			TINYINT
		</text>
		<rect height="22" width="52.96110534667969" y="134" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,145)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-6-name" class="er entityLabel">
			Deactive
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="127.80029296875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,145)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-7-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="52.96110534667969" y="156" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,167)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-7-name" class="er entityLabel">
			chk_id
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="127.80029296875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(132.80029296875,167)" y="0" x="0" id="text-entity-coa32-7bebcc1d-51b3-512f-88ff-729289a77e69-attr-7-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(2422.5886611938477,2048 )" id="entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8">
		<rect height="244" width="159.02835083007812" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(79.51417541503906,12)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8" class="er entityLabel">
			coa321
		</text>
		<rect height="22" width="74.83918762207031" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="61.50148010253906" y="24" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,35)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-1-name" class="er entityLabel">
			id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="136.34066772460938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,35)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.83918762207031" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="61.50148010253906" y="46" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,57)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-2-name" class="er entityLabel">
			formalTitle
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="136.34066772460938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,57)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-3-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="61.50148010253906" y="68" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,79)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-3-name" class="er entityLabel">
			surName
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="136.34066772460938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,79)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-4-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="61.50148010253906" y="90" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,101)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-4-name" class="er entityLabel">
			firstName
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="136.34066772460938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,101)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-5-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="61.50148010253906" y="112" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,123)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-5-name" class="er entityLabel">
			add1
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="136.34066772460938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,123)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-6-type" class="er entityLabel">
			NVARCHAR20
		</text>
		<rect height="22" width="61.50148010253906" y="134" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,145)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-6-name" class="er entityLabel">
			tel
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="136.34066772460938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,145)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-7-type" class="er entityLabel">
			NVARCHAR20
		</text>
		<rect height="22" width="61.50148010253906" y="156" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,167)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-7-name" class="er entityLabel">
			Mobile
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="136.34066772460938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,167)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-7-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-8-type" class="er entityLabel">
			NVARCHAR100
		</text>
		<rect height="22" width="61.50148010253906" y="178" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,189)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-8-name" class="er entityLabel">
			email
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="136.34066772460938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,189)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-8-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-9-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="61.50148010253906" y="200" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,211)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-9-name" class="er entityLabel">
			url
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="136.34066772460938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,211)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-9-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-10-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="61.50148010253906" y="222" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,233)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-10-name" class="er entityLabel">
			Mode
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="136.34066772460938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(141.34066772460938,233)" y="0" x="0" id="text-entity-coa321-e94f4059-48fa-5987-bb47-81b0da5df9e8-attr-10-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(3429.1749839782715,2048 )" id="entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb">
		<rect height="244" width="172.63534545898438" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(86.31767272949219,12)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb" class="er entityLabel">
			coa31
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-1-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-1-name" class="er entityLabel">
			id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="149.94766235351562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,35)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="75.74453735351562" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="149.94766235351562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,57)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-3-type" class="er entityLabel">
			NVARCHAR10
		</text>
		<rect height="22" width="75.74453735351562" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-3-name" class="er entityLabel">
			Unit
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="149.94766235351562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,79)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-4-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="75.74453735351562" y="90" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,101)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-4-name" class="er entityLabel">
			Details
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="149.94766235351562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,101)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-5-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="75.74453735351562" y="112" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,123)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-5-name" class="er entityLabel">
			Sale_Rate
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="149.94766235351562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,123)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-6-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="75.74453735351562" y="134" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,145)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-6-name" class="er entityLabel">
			Purc_Rate
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="149.94766235351562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,145)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-7-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="156" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,167)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-7-name" class="er entityLabel">
			Category_ID
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="149.94766235351562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,167)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-8-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="178" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,189)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-8-name" class="er entityLabel">
			Purchase_ID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="149.94766235351562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,189)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-9-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="200" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,211)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-9-name" class="er entityLabel">
			Sale_ID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="149.94766235351562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,211)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-10-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="222" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,233)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-10-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="149.94766235351562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(154.94766235351562,233)" y="0" x="0" id="text-entity-coa31-b2f33b8b-7c1a-5414-a359-3987946495bb-attr-10-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(2194.1253242492676,20 )" id="entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d">
		<rect height="288" width="167.92239379882812" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(83.96119689941406,12)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d" class="er entityLabel">
			AC
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-1-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="75.74453735351562" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-1-name" class="er entityLabel">
			Voucher_No
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,35)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-2-type" class="er entityLabel">
			NVARCHAR3
		</text>
		<rect height="22" width="75.74453735351562" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-2-name" class="er entityLabel">
			vtp
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,57)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-3-type" class="er entityLabel">
			NVARCHAR4
		</text>
		<rect height="22" width="75.74453735351562" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-3-name" class="er entityLabel">
			Mnth
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,79)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-4-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-4-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,101)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-5-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-5-name" class="er entityLabel">
			vno
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,123)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-6-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="75.74453735351562" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-6-name" class="er entityLabel">
			Dated
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,145)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-7-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="75.74453735351562" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-7-name" class="er entityLabel">
			amt
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,167)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-7-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-8-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="178" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,189)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-8-name" class="er entityLabel">
			bc_id
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,189)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-9-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="200" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,211)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-9-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,211)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-10-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="222" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,233)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-10-name" class="er entityLabel">
			prp_ID
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,233)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-10-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-11-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="244" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,255)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-11-name" class="er entityLabel">
			chk_ID
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,255)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-11-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-12-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="266" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,277)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-12-name" class="er entityLabel">
			app_ID
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,277)" y="0" x="0" id="text-entity-AC-642132a5-495b-5faf-afcb-1efb68b7238d-attr-12-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(2590.055034637451,408 )" id="entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632">
		<rect height="288" width="176.06297302246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(88.03148651123047,12)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632" class="er entityLabel">
			ac_det
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="79.17216491699219" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,35)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="79.17216491699219" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-2-name" class="er entityLabel">
			Voucher_No
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,57)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-3-type" class="er entityLabel">
			NVARCHAR3
		</text>
		<rect height="22" width="79.17216491699219" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-3-name" class="er entityLabel">
			vtp
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,79)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-4-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="79.17216491699219" y="90" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,101)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-4-name" class="er entityLabel">
			srno
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,101)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-5-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="79.17216491699219" y="112" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,123)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-5-name" class="er entityLabel">
			acc_id
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,123)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-5-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-6-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="79.17216491699219" y="134" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,145)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-6-name" class="er entityLabel">
			adj_VoucherNo
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,145)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-6-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-7-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="79.17216491699219" y="156" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,167)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-7-name" class="er entityLabel">
			TotalAmt
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,167)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-7-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-8-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="79.17216491699219" y="178" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,189)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-8-name" class="er entityLabel">
			Narration
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,189)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-8-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-9-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="79.17216491699219" y="200" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,211)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-9-name" class="er entityLabel">
			ln_rem
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,211)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-9-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-10-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="79.17216491699219" y="222" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,233)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-10-name" class="er entityLabel">
			Emp_ID
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,233)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-10-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-11-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="79.17216491699219" y="244" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,255)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-11-name" class="er entityLabel">
			taxacc_id
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="153.3752899169922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,255)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-11-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-12-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="79.17216491699219" y="266" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,277)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-12-name" class="er entityLabel">
			Dated
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="153.3752899169922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(158.3752899169922,277)" y="0" x="0" id="text-entity-acdet-e02b13e3-3f45-5d36-b442-0602311a1632-attr-12-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(2806.3535766601562,796 )" id="entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde">
		<rect height="376" width="191.49851989746094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(95.74925994873047,12)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde" class="er entityLabel">
			Offer
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-1-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="94.60771179199219" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-1-name" class="er entityLabel">
			Voucher_No
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,35)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-2-type" class="er entityLabel">
			NVARCHAR3
		</text>
		<rect height="22" width="94.60771179199219" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-2-name" class="er entityLabel">
			VTP
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,57)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-3-type" class="er entityLabel">
			NVARCHAR4
		</text>
		<rect height="22" width="94.60771179199219" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-3-name" class="er entityLabel">
			Mnth
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,79)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-4-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="94.60771179199219" y="90" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,101)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-4-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,101)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-5-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="94.60771179199219" y="112" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,123)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-5-name" class="er entityLabel">
			vno
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,123)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-6-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="94.60771179199219" y="134" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,145)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-6-name" class="er entityLabel">
			Dated
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,145)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="94.60771179199219" y="156" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,167)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-7-name" class="er entityLabel">
			client_id
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,167)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-8-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="94.60771179199219" y="178" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,189)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-8-name" class="er entityLabel">
			Emp_id
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,189)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-9-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="94.60771179199219" y="200" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,211)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-9-name" class="er entityLabel">
			EmployeeID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,211)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-10-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="94.60771179199219" y="222" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,233)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-10-name" class="er entityLabel">
			GrossAmount
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,233)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-10-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-11-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="94.60771179199219" y="244" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,255)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-11-name" class="er entityLabel">
			StartingComments
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,255)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-11-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-12-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="94.60771179199219" y="266" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,277)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-12-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,277)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-12-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="288" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,299)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-13-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="94.60771179199219" y="288" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,299)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-13-name" class="er entityLabel">
			Project_ID
		</text>
		<rect height="22" width="22.68768310546875" y="288" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,299)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-13-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="310" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,321)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-14-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="94.60771179199219" y="310" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,321)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-14-name" class="er entityLabel">
			prp_ID
		</text>
		<rect height="22" width="22.68768310546875" y="310" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,321)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-14-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="332" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,343)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-15-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="94.60771179199219" y="332" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,343)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-15-name" class="er entityLabel">
			chk_ID
		</text>
		<rect height="22" width="22.68768310546875" y="332" x="168.8108367919922" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,343)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-15-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="354" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,365)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-16-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="94.60771179199219" y="354" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,365)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-16-name" class="er entityLabel">
			app_ID
		</text>
		<rect height="22" width="22.68768310546875" y="354" x="168.8108367919922" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(173.8108367919922,365)" y="0" x="0" id="text-entity-Offer-9c0a33b2-e454-50da-b6ad-eaee0635acde-attr-16-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(4736.833549499512,1272 )" id="entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc">
		<rect height="354" width="206.13250732421875" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(103.06625366210938,12)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc" class="er entityLabel">
			Offer_det
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="113.95465087890625" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,35)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="113.95465087890625" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-2-name" class="er entityLabel">
			voucher_no
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,57)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-3-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-3-name" class="er entityLabel">
			srno
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,79)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-4-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="113.95465087890625" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-4-name" class="er entityLabel">
			Item_ID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,101)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-4-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-5-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="113.95465087890625" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-5-name" class="er entityLabel">
			Site_ID
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,123)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-5-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-6-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="113.95465087890625" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-6-name" class="er entityLabel">
			Qty
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,145)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-7-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="113.95465087890625" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-7-name" class="er entityLabel">
			Rate
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,167)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-7-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-8-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="113.95465087890625" y="178" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,189)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-8-name" class="er entityLabel">
			Amount
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,189)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-8-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-9-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="200" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,211)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-9-name" class="er entityLabel">
			Project_ID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,211)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-10-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="222" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,233)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-10-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,233)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-10-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-11-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="113.95465087890625" y="244" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,255)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-11-name" class="er entityLabel">
			Vendor_ID
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,255)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-11-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-12-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="266" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,277)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-12-name" class="er entityLabel">
			Godown_ID
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,277)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-12-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="288" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,299)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-13-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="288" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,299)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-13-name" class="er entityLabel">
			Job_ID
		</text>
		<rect height="22" width="22.68768310546875" y="288" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,299)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-13-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="310" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,321)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-14-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="310" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,321)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-14-name" class="er entityLabel">
			ClientItemCategory_ID
		</text>
		<rect height="22" width="22.68768310546875" y="310" x="183.44482421875" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,321)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-14-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="332" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,343)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-15-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="113.95465087890625" y="332" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,343)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-15-name" class="er entityLabel">
			ClientItemType_ID
		</text>
		<rect height="22" width="22.68768310546875" y="332" x="183.44482421875" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(188.44482421875,343)" y="0" x="0" id="text-entity-Offerdet-3316e635-bd08-5547-9e8c-eaae021dc9dc-attr-15-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(2314.4256896972656,1305 )" id="entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e">
		<rect height="288" width="175.3542938232422" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(87.6771469116211,12)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e" class="er entityLabel">
			CO
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-1-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="83.17643737792969" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-1-name" class="er entityLabel">
			Voucher_No
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,35)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-2-type" class="er entityLabel">
			NVARCHAR3
		</text>
		<rect height="22" width="83.17643737792969" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-2-name" class="er entityLabel">
			VTP
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,57)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-3-type" class="er entityLabel">
			NVARCHAR4
		</text>
		<rect height="22" width="83.17643737792969" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-3-name" class="er entityLabel">
			Mnth
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,79)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-4-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="83.17643737792969" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-4-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,101)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-5-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="83.17643737792969" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-5-name" class="er entityLabel">
			vno
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,123)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-6-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="83.17643737792969" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-6-name" class="er entityLabel">
			Dated
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,145)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="83.17643737792969" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-7-name" class="er entityLabel">
			client_id
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,167)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-8-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="83.17643737792969" y="178" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,189)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-8-name" class="er entityLabel">
			SalesMan_ID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,189)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-9-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="83.17643737792969" y="200" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,211)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-9-name" class="er entityLabel">
			Installer_Person
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,211)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-10-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="83.17643737792969" y="222" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,233)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-10-name" class="er entityLabel">
			Delivery_Person
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,233)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-10-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-11-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="83.17643737792969" y="244" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,255)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-11-name" class="er entityLabel">
			Cash_id
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="152.66661071777344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,255)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-11-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-12-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="83.17643737792969" y="266" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,277)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-12-name" class="er entityLabel">
			GrossAmount
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="152.66661071777344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(157.66661071777344,277)" y="0" x="0" id="text-entity-CO-4fd4e483-8640-537d-b791-c9d3c7ec628e-attr-12-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(3515.0960426330566,1748 )" id="entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575">
		<rect height="178" width="156.2930145263672" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(78.1465072631836,12)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575" class="er entityLabel">
			CO_det
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="64.11515808105469" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="133.60533142089844" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,35)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="64.11515808105469" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-2-name" class="er entityLabel">
			voucher_no
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="133.60533142089844" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,57)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-3-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="64.11515808105469" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-3-name" class="er entityLabel">
			srno
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="133.60533142089844" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,79)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-4-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="64.11515808105469" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-4-name" class="er entityLabel">
			Item_ID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="133.60533142089844" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,101)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-4-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-5-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="64.11515808105469" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-5-name" class="er entityLabel">
			Qty
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="133.60533142089844" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,123)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-6-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="64.11515808105469" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-6-name" class="er entityLabel">
			Rate
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="133.60533142089844" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,145)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-7-type" class="er entityLabel">
			FLOAT
		</text>
		<rect height="22" width="64.11515808105469" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-7-name" class="er entityLabel">
			Amount
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="133.60533142089844" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(138.60533142089844,167)" y="0" x="0" id="text-entity-COdet-161f1de1-b3d1-59c3-a0ae-1d1ada0cc575-attr-7-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(3304.137981414795,1283 )" id="entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955">
		<rect height="332" width="167.92239379882812" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(83.96119689941406,12)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955" class="er entityLabel">
			DC
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-1-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="75.74453735351562" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-1-name" class="er entityLabel">
			Voucher_No
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,35)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-2-type" class="er entityLabel">
			NVARCHAR3
		</text>
		<rect height="22" width="75.74453735351562" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-2-name" class="er entityLabel">
			VTP
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,57)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-3-type" class="er entityLabel">
			NVARCHAR4
		</text>
		<rect height="22" width="75.74453735351562" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-3-name" class="er entityLabel">
			Mnth
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,79)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-4-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-4-name" class="er entityLabel">
			Location
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,101)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-5-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-5-name" class="er entityLabel">
			vno
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,123)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-6-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="75.74453735351562" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-6-name" class="er entityLabel">
			Dated
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,145)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-7-name" class="er entityLabel">
			Client_ID
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,167)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-8-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="178" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,189)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-8-name" class="er entityLabel">
			Project_ID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,189)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-8-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-9-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="200" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,211)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-9-name" class="er entityLabel">
			CostCenter_ID
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,211)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-9-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-10-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="75.74453735351562" y="222" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,233)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-10-name" class="er entityLabel">
			SalesMan_ID
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,233)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-10-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="244" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,255)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-11-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="75.74453735351562" y="244" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,255)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-11-name" class="er entityLabel">
			Godown_ID
		</text>
		<rect height="22" width="22.68768310546875" y="244" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,255)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-11-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="266" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,277)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-12-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="266" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,277)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-12-name" class="er entityLabel">
			prp_ID
		</text>
		<rect height="22" width="22.68768310546875" y="266" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,277)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-12-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="288" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,299)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-13-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="288" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,299)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-13-name" class="er entityLabel">
			chk_ID
		</text>
		<rect height="22" width="22.68768310546875" y="288" x="145.23471069335938" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,299)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-13-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="310" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,321)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-14-type" class="er entityLabel">
			NVARCHAR8
		</text>
		<rect height="22" width="75.74453735351562" y="310" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,321)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-14-name" class="er entityLabel">
			app_ID
		</text>
		<rect height="22" width="22.68768310546875" y="310" x="145.23471069335938" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(150.23471069335938,321)" y="0" x="0" id="text-entity-DC-22cefdde-cd90-534b-b4b4-2569aa1fc955-attr-14-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(3453.174060821533,2392 )" id="entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a">
		<rect height="68" width="124.63719177246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(62.31859588623047,12)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a" class="er entityLabel">
			Categories
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="32.45933532714844" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-1-name" class="er entityLabel">
			Id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="101.94950866699219" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,35)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="32.45933532714844" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="101.94950866699219" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,57)" y="0" x="0" id="text-entity-Categories-4066dbfd-a08d-5786-b5c1-a5236eeca05a-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(3836.2129707336426,1803 )" id="entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159">
		<rect height="68" width="124.63719177246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(62.31859588623047,12)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159" class="er entityLabel">
			CostCenters
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="32.45933532714844" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="101.94950866699219" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,35)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="32.45933532714844" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="101.94950866699219" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,57)" y="0" x="0" id="text-entity-CostCenters-c0fd3206-02d7-5a08-823b-a90d2a9cf159-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(4275.427501678467,1803 )" id="entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae">
		<rect height="68" width="124.63719177246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(62.31859588623047,12)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae" class="er entityLabel">
			DefProjects
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="32.45933532714844" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="101.94950866699219" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,35)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="32.45933532714844" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="101.94950866699219" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,57)" y="0" x="0" id="text-entity-DefProjects-f672edf5-11bb-526d-80a9-2e8c9af544ae-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(4504.961570739746,1803 )" id="entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8">
		<rect height="68" width="124.63719177246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(62.31859588623047,12)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8" class="er entityLabel">
			Godown
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="32.45933532714844" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-1-name" class="er entityLabel">
			Id
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="101.94950866699219" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,35)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="32.45933532714844" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="101.94950866699219" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,57)" y="0" x="0" id="text-entity-Godown-f5d8b651-2a03-5e2b-8078-734f745128c8-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(4729.598762512207,1803 )" id="entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb">
		<rect height="68" width="124.63719177246094" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(62.31859588623047,12)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb" class="er entityLabel">
			DefJobs
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="32.45933532714844" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="101.94950866699219" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,35)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="32.45933532714844" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="101.94950866699219" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(106.94950866699219,57)" y="0" x="0" id="text-entity-DefJobs-e15715f5-83a9-5fa3-8f4a-272b537e0cdb-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(4954.235954284668,1803 )" id="entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c">
		<rect height="68" width="153.853515625" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(76.9267578125,12)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c" class="er entityLabel">
			DefClientItemCategory
		</text>
		<rect height="22" width="79.22894795735677" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="42.19810994466146" y="24" x="79.22894795735677" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(84.22894795735677,35)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="32.42645772298177" y="24" x="121.42705790201822" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(126.42705790201822,35)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="79.22894795735677" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="42.19810994466146" y="46" x="79.22894795735677" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(84.22894795735677,57)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="32.42645772298177" y="46" x="121.42705790201822" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(126.42705790201822,57)" y="0" x="0" id="text-entity-DefClientItemCategory-65cb473b-cef1-5937-a99d-7b10efcb595c-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(5208.089469909668,1803 )" id="entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd">
		<rect height="68" width="130.12890625" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(65.064453125,12)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd" class="er entityLabel">
			DefClientItemType
		</text>
		<rect height="22" width="71.32074483235677" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="34.28990681966146" y="24" x="71.32074483235677" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(76.32074483235677,35)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="24.51825459798177" y="24" x="105.61065165201822" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(110.61065165201822,35)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="71.32074483235677" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-2-type" class="er entityLabel">
			NVARCHAR90
		</text>
		<rect height="22" width="34.28990681966146" y="46" x="71.32074483235677" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(76.32074483235677,57)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-2-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="24.51825459798177" y="46" x="105.61065165201822" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(110.61065165201822,57)" y="0" x="0" id="text-entity-DefClientItemType-a53c4801-4223-5f54-a154-ec440b73a5dd-attr-2-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(1199.435203552246,452 )" id="entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2">
		<rect height="200" width="231.8789825439453" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(115.93949127197266,12)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2" class="er entityLabel">
			ClientPurpose
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="134.98817443847656" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="209.19129943847656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,35)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="134.98817443847656" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-2-name" class="er entityLabel">
			RefVoucherNo
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="209.19129943847656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,57)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-3-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="134.98817443847656" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-3-name" class="er entityLabel">
			DeliveryDateTime
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="209.19129943847656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,79)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-4-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="134.98817443847656" y="90" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,101)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-4-name" class="er entityLabel">
			InstallerDateTime
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="209.19129943847656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,101)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-5-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="134.98817443847656" y="112" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,123)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-5-name" class="er entityLabel">
			deliveryTimeUpdateReason
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="209.19129943847656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,123)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-6-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="134.98817443847656" y="134" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,145)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-6-name" class="er entityLabel">
			installerTimeUpdateReason
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="209.19129943847656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,145)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="134.98817443847656" y="156" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,167)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-7-name" class="er entityLabel">
			DeliveryPersonID
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="209.19129943847656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,167)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-8-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="134.98817443847656" y="178" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,189)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-8-name" class="er entityLabel">
			InstallerPersonID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="209.19129943847656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(214.19129943847656,189)" y="0" x="0" id="text-entity-ClientPurpose-2d4a88f0-c7d9-56fe-bca9-073e462b96b2-attr-8-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(1631.3141860961914,485 )" id="entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3">
		<rect height="134" width="170.7886505126953" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(85.39432525634766,12)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3" class="er entityLabel">
			RecoveryFollowUps
		</text>
		<rect height="22" width="74.203125" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="73.89784240722656" y="24" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,35)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="148.10096740722656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(153.10096740722656,35)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.203125" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="73.89784240722656" y="46" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,57)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-2-name" class="er entityLabel">
			RefVoucherNo
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="148.10096740722656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(153.10096740722656,57)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="74.203125" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-3-type" class="er entityLabel">
			DATETIME
		</text>
		<rect height="22" width="73.89784240722656" y="68" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,79)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-3-name" class="er entityLabel">
			FollowUpDate
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="148.10096740722656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(153.10096740722656,79)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-4-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="73.89784240722656" y="90" x="74.203125" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,101)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-4-name" class="er entityLabel">
			Comments
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="148.10096740722656" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(153.10096740722656,101)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.203125" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-5-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="73.89784240722656" y="112" x="74.203125" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.203125,123)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-5-name" class="er entityLabel">
			EmployeeID
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="148.10096740722656" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(153.10096740722656,123)" y="0" x="0" id="text-entity-RecoveryFollowUps-f9a40721-1f83-5435-95f9-dbe7342a0ea3-attr-5-key" class="er entityLabel">
			FK
		</text>
	</g>
	<g transform="translate(5538.009239196777,1748 )" id="entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5">
		<rect height="178" width="167.106689453125" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(83.5533447265625,12)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5" class="er entityLabel">
			NotificationTypes
		</text>
		<rect height="22" width="74.83918762207031" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="69.57981872558594" y="24" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,35)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="144.41900634765625" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,35)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="74.83918762207031" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-2-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="69.57981872558594" y="46" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,57)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-2-name" class="er entityLabel">
			TypeName
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="144.41900634765625" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,57)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-2-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-3-type" class="er entityLabel">
			NVARCHAR100
		</text>
		<rect height="22" width="69.57981872558594" y="68" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,79)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-3-name" class="er entityLabel">
			DisplayName
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="144.41900634765625" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,79)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-4-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="69.57981872558594" y="90" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,101)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-4-name" class="er entityLabel">
			Description
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="144.41900634765625" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,101)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-5-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="69.57981872558594" y="112" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,123)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-5-name" class="er entityLabel">
			IconName
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="144.41900634765625" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,123)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-6-type" class="er entityLabel">
			NVARCHAR20
		</text>
		<rect height="22" width="69.57981872558594" y="134" x="74.83918762207031" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,145)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-6-name" class="er entityLabel">
			ColorScheme
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="144.41900634765625" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,145)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="74.83918762207031" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-7-type" class="er entityLabel">
			BIT
		</text>
		<rect height="22" width="69.57981872558594" y="156" x="74.83918762207031" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(79.83918762207031,167)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-7-name" class="er entityLabel">
			IsActive
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="144.41900634765625" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(149.41900634765625,167)" y="0" x="0" id="text-entity-NotificationTypes-e4e49922-9fdf-58a1-a97f-1b37ece526d5-attr-7-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(5542.565944671631,1371 )" id="entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af">
		<rect height="156" width="193.75106811523438" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(96.87553405761719,12)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af" class="er entityLabel">
			NotificationTemplates
		</text>
		<rect height="22" width="80.18820190429688" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-1-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="90.87518310546875" y="24" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,35)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="171.06338500976562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,35)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="80.18820190429688" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-2-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="90.87518310546875" y="46" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,57)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-2-name" class="er entityLabel">
			TypeID
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="171.06338500976562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,57)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="80.18820190429688" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-3-type" class="er entityLabel">
			NVARCHAR100
		</text>
		<rect height="22" width="90.87518310546875" y="68" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,79)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-3-name" class="er entityLabel">
			TemplateName
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="171.06338500976562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,79)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-3-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-4-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="90.87518310546875" y="90" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,101)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-4-name" class="er entityLabel">
			TitleTemplate
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="171.06338500976562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,101)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-5-type" class="er entityLabel">
			NVARCHAR1000
		</text>
		<rect height="22" width="90.87518310546875" y="112" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,123)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-5-name" class="er entityLabel">
			MessageTemplate
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="171.06338500976562" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,123)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-6-type" class="er entityLabel">
			TINYINT
		</text>
		<rect height="22" width="90.87518310546875" y="134" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,145)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-6-name" class="er entityLabel">
			Priority
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="171.06338500976562" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(176.06338500976562,145)" y="0" x="0" id="text-entity-NotificationTemplates-c09e9dc7-8c28-563f-9f19-bd12ab0943af-attr-6-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(2302.9935607910156,862 )" id="entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c">
		<rect height="244" width="198.2185516357422" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(99.1092758178711,12)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c" class="er entityLabel">
			Notifications
		</text>
		<rect height="22" width="80.18820190429688" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="95.34266662597656" y="24" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,35)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="175.53086853027344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,35)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="80.18820190429688" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-2-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="95.34266662597656" y="46" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,57)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-2-name" class="er entityLabel">
			TypeID
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="175.53086853027344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,57)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="80.18820190429688" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-3-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="95.34266662597656" y="68" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,79)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-3-name" class="er entityLabel">
			TemplateID
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="175.53086853027344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,79)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-3-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="80.18820190429688" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-4-type" class="er entityLabel">
			NVARCHAR255
		</text>
		<rect height="22" width="95.34266662597656" y="90" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,101)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-4-name" class="er entityLabel">
			Title
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="175.53086853027344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,101)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-4-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-5-type" class="er entityLabel">
			NVARCHAR1000
		</text>
		<rect height="22" width="95.34266662597656" y="112" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,123)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-5-name" class="er entityLabel">
			Message
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="175.53086853027344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,123)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-5-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-6-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="95.34266662597656" y="134" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,145)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-6-name" class="er entityLabel">
			SenderUserID
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="175.53086853027344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,145)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-6-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="80.18820190429688" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-7-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="95.34266662597656" y="156" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,167)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-7-name" class="er entityLabel">
			SenderEmployeeID
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="175.53086853027344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,167)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-7-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="80.18820190429688" y="178" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,189)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-8-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="95.34266662597656" y="178" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,189)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-8-name" class="er entityLabel">
			ReferenceID
		</text>
		<rect height="22" width="22.68768310546875" y="178" x="175.53086853027344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,189)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-8-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="200" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,211)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-9-type" class="er entityLabel">
			NVARCHAR50
		</text>
		<rect height="22" width="95.34266662597656" y="200" x="80.18820190429688" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,211)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-9-name" class="er entityLabel">
			ReferenceType
		</text>
		<rect height="22" width="22.68768310546875" y="200" x="175.53086853027344" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,211)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-9-key" class="er entityLabel">
		</text>
		<rect height="22" width="80.18820190429688" y="222" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,233)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-10-type" class="er entityLabel">
			DATETIME2
		</text>
		<rect height="22" width="95.34266662597656" y="222" x="80.18820190429688" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(85.18820190429688,233)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-10-name" class="er entityLabel">
			CreatedAt
		</text>
		<rect height="22" width="22.68768310546875" y="222" x="175.53086853027344" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(180.53086853027344,233)" y="0" x="0" id="text-entity-Notifications-e553f5cf-b345-54b9-95cc-68255be8d56c-attr-10-key" class="er entityLabel">
		</text>
	</g>
	<g transform="translate(341.4907112121582,463 )" id="entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73">
		<rect height="178" width="199.0851287841797" y="0" x="0" class="er entityBox">
		</rect>
		<text style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;" transform="translate(99.54256439208984,12)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73" class="er entityLabel">
			NotificationRecipients
		</text>
		<rect height="22" width="69.49017333984375" y="24" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-1-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="106.90727233886719" y="24" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,35)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-1-name" class="er entityLabel">
			ID
		</text>
		<rect height="22" width="22.68768310546875" y="24" x="176.39744567871094" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,35)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-1-key" class="er entityLabel">
			PK
		</text>
		<rect height="22" width="69.49017333984375" y="46" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-2-type" class="er entityLabel">
			BIGINT
		</text>
		<rect height="22" width="106.90727233886719" y="46" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,57)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-2-name" class="er entityLabel">
			NotificationID
		</text>
		<rect height="22" width="22.68768310546875" y="46" x="176.39744567871094" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,57)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-2-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="68" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,79)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-3-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="106.90727233886719" y="68" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,79)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-3-name" class="er entityLabel">
			RecipientUserID
		</text>
		<rect height="22" width="22.68768310546875" y="68" x="176.39744567871094" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,79)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-3-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="90" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,101)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-4-type" class="er entityLabel">
			INT
		</text>
		<rect height="22" width="106.90727233886719" y="90" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,101)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-4-name" class="er entityLabel">
			RecipientRoleID
		</text>
		<rect height="22" width="22.68768310546875" y="90" x="176.39744567871094" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,101)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-4-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="112" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,123)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-5-type" class="er entityLabel">
			NVARCHAR32
		</text>
		<rect height="22" width="106.90727233886719" y="112" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,123)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-5-name" class="er entityLabel">
			RecipientEmployeeID
		</text>
		<rect height="22" width="22.68768310546875" y="112" x="176.39744567871094" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,123)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-5-key" class="er entityLabel">
			FK
		</text>
		<rect height="22" width="69.49017333984375" y="134" x="0" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,145)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-6-type" class="er entityLabel">
			BIT
		</text>
		<rect height="22" width="106.90727233886719" y="134" x="69.49017333984375" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,145)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-6-name" class="er entityLabel">
			IsRead
		</text>
		<rect height="22" width="22.68768310546875" y="134" x="176.39744567871094" class="er attributeBoxEven">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,145)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-6-key" class="er entityLabel">
		</text>
		<rect height="22" width="69.49017333984375" y="156" x="0" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,167)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-7-type" class="er entityLabel">
			DATETIME2
		</text>
		<rect height="22" width="106.90727233886719" y="156" x="69.49017333984375" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(74.49017333984375,167)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-7-name" class="er entityLabel">
			ReadAt
		</text>
		<rect height="22" width="22.68768310546875" y="156" x="176.39744567871094" class="er attributeBoxOdd">
		</rect>
		<text style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(181.39744567871094,167)" y="0" x="0" id="text-entity-NotificationRecipients-fad5e1e3-b91f-58f9-9284-76dd13e7de73-attr-7-key" class="er entityLabel">
		</text>
	</g>
	<rect height="14" width="42.9765625" y="1585.3009033203125" x="504.06036376953125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1592.3009033203125" x="525.5486450195312" id="rel57" class="er relationshipLabel">
		has role
	</text>
	<rect height="14" width="91.896484375" y="1611.313720703125" x="1752.6593017578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1618.313720703125" x="1798.6075439453125" id="rel58" class="er relationshipLabel">
		can be employee
	</text>
	<rect height="14" width="95.94921875" y="1715.95751953125" x="3113.447509765625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1722.95751953125" x="3161.422119140625" id="rel59" class="er relationshipLabel">
		contains accounts
	</text>
	<rect height="14" width="57.587890625" y="1976.5086669921875" x="2485.233642578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1983.5086669921875" x="2514.027587890625" id="rel60" class="er relationshipLabel">
		has details
	</text>
	<rect height="14" width="57.44140625" y="1937.9593505859375" x="4522.908203125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1944.9593505859375" x="4551.62890625" id="rel61" class="er relationshipLabel">
		references
	</text>
	<rect height="14" width="84.705078125" y="283.3644104003906" x="2493.171875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="290.3644104003906" x="2535.5244140625" id="rel62" class="er relationshipLabel">
		contains details
	</text>
	<rect height="14" width="101.908203125" y="858.4439697265625" x="3095.164794921875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="865.4439697265625" x="3146.118896484375" id="rel63" class="er relationshipLabel">
		bank/cash account
	</text>
	<rect height="14" width="61.95703125" y="381.2083740234375" x="3880.736328125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="388.2083740234375" x="3911.71484375" id="rel64" class="er relationshipLabel">
		cost center
	</text>
	<rect height="14" width="66.080078125" y="995.4546508789062" x="1869.0030517578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1002.4546508789062" x="1902.0430908203125" id="rel65" class="er relationshipLabel">
		prepared by
	</text>
	<rect height="14" width="61.568359375" y="669.1624145507812" x="1500.52978515625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="676.1624145507812" x="1531.31396484375" id="rel66" class="er relationshipLabel">
		checked by
	</text>
	<rect height="14" width="67.1875" y="387.8572082519531" x="1165.146484375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="394.8572082519531" x="1198.740234375" id="rel67" class="er relationshipLabel">
		approved by
	</text>
	<rect height="14" width="42.740234375" y="1218.968505859375" x="2680.73291015625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1225.968505859375" x="2702.10302734375" id="rel68" class="er relationshipLabel">
		account
	</text>
	<rect height="14" width="52.6328125" y="1326.0537109375" x="2075.78662109375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1333.0537109375" x="2102.10302734375" id="rel69" class="er relationshipLabel">
		employee
	</text>
	<rect height="14" width="85.951171875" y="678.0159301757812" x="2853.9306640625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="685.0159301757812" x="2896.90625" id="rel70" class="er relationshipLabel">
		adjusts voucher
	</text>
	<rect height="14" width="78.13671875" y="1104.146728515625" x="3889.3818359375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1111.146728515625" x="3928.4501953125" id="rel71" class="er relationshipLabel">
		contains items
	</text>
	<rect height="14" width="31.00390625" y="1358.7559814453125" x="2588.315185546875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1365.7559814453125" x="2603.817138671875" id="rel72" class="er relationshipLabel">
		client
	</text>
	<rect height="14" width="52.6328125" y="1367.778564453125" x="1980.4886474609375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1374.778564453125" x="2006.8050537109375" id="rel73" class="er relationshipLabel">
		employee
	</text>
	<rect height="14" width="61.95703125" y="1182.920654296875" x="3705.59521484375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1189.920654296875" x="3736.57373046875" id="rel74" class="er relationshipLabel">
		cost center
	</text>
	<rect height="14" width="39.67578125" y="1156.306884765625" x="3917.36083984375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1163.306884765625" x="3937.19873046875" id="rel75" class="er relationshipLabel">
		project
	</text>
	<rect height="14" width="66.080078125" y="1108.7728271484375" x="1844.2305908203125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1115.7728271484375" x="1877.2706298828125" id="rel76" class="er relationshipLabel">
		prepared by
	</text>
	<rect height="14" width="24.724609375" y="2019.008056640625" x="5213.69140625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="2026.008056640625" x="5226.0537109375" id="rel77" class="er relationshipLabel">
		item
	</text>
	<rect height="14" width="39.67578125" y="1596.5054931640625" x="4457.3671875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1603.5054931640625" x="4477.205078125" id="rel78" class="er relationshipLabel">
		project
	</text>
	<rect height="14" width="61.95703125" y="1579.637451171875" x="4246.99462890625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1586.637451171875" x="4277.97314453125" id="rel79" class="er relationshipLabel">
		cost center
	</text>
	<rect height="14" width="39.103515625" y="1562.0638427734375" x="3600.263427734375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1569.0638427734375" x="3619.815185546875" id="rel80" class="er relationshipLabel">
		vendor
	</text>
	<rect height="14" width="41.517578125" y="1662.5732421875" x="4620.2861328125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1669.5732421875" x="4641.044921875" id="rel81" class="er relationshipLabel">
		godown
	</text>
	<rect height="14" width="18.83984375" y="1707.144775390625" x="4783.205078125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1714.144775390625" x="4792.625" id="rel82" class="er relationshipLabel">
		job
	</text>
	<rect height="14" width="47.67578125" y="1669.5992431640625" x="4991.39892578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1676.5992431640625" x="5015.23681640625" id="rel83" class="er relationshipLabel">
		category
	</text>
	<rect height="14" width="24.361328125" y="1610.8033447265625" x="5137.6884765625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1617.8033447265625" x="5149.869140625" id="rel84" class="er relationshipLabel">
		type
	</text>
	<rect height="14" width="78.13671875" y="1568.8721923828125" x="3019.845458984375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1575.8721923828125" x="3058.913818359375" id="rel85" class="er relationshipLabel">
		contains items
	</text>
	<rect height="14" width="31.00390625" y="1692.5333251953125" x="2423.9111328125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1699.5333251953125" x="2439.4130859375" id="rel86" class="er relationshipLabel">
		client
	</text>
	<rect height="14" width="49.369140625" y="1592.82177734375" x="1789.96240234375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1599.82177734375" x="1814.64697265625" id="rel87" class="er relationshipLabel">
		salesman
	</text>
	<rect height="14" width="45.521484375" y="1576.711669921875" x="1792.3065185546875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1583.711669921875" x="1815.0672607421875" id="rel88" class="er relationshipLabel">
		installer
	</text>
	<rect height="14" width="83.001953125" y="1569.1807861328125" x="1748.9561767578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1576.1807861328125" x="1790.4571533203125" id="rel89" class="er relationshipLabel">
		delivery person
	</text>
	<rect height="14" width="24.724609375" y="1981.6639404296875" x="3578.056884765625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1988.6639404296875" x="3590.419189453125" id="rel90" class="er relationshipLabel">
		item
	</text>
	<rect height="14" width="31.00390625" y="1570.2071533203125" x="2778.01513671875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1577.2071533203125" x="2793.51708984375" id="rel91" class="er relationshipLabel">
		client
	</text>
	<rect height="14" width="39.67578125" y="1593.36328125" x="3887.353759765625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1600.36328125" x="3907.191650390625" id="rel92" class="er relationshipLabel">
		project
	</text>
	<rect height="14" width="61.95703125" y="1621.1365966796875" x="3660.115478515625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1628.1365966796875" x="3691.093994140625" id="rel93" class="er relationshipLabel">
		cost center
	</text>
	<rect height="14" width="49.369140625" y="1561.3380126953125" x="2224.03955078125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1568.3380126953125" x="2248.72412109375" id="rel94" class="er relationshipLabel">
		salesman
	</text>
	<rect height="14" width="41.517578125" y="1578.09423828125" x="4016.885986328125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1585.09423828125" x="4037.644775390625" id="rel95" class="er relationshipLabel">
		godown
	</text>
	<rect height="14" width="47.67578125" y="2335" x="3491.655029296875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="2342" x="3515.492919921875" id="rel96" class="er relationshipLabel">
		category
	</text>
	<rect height="14" width="94.466796875" y="1922.5797119140625" x="3032.4677734375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1929.5797119140625" x="3079.701171875" id="rel97" class="er relationshipLabel">
		purchase account
	</text>
	<rect height="14" width="67.6015625" y="2022.6190185546875" x="3012.14892578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="2029.6190185546875" x="3045.94970703125" id="rel98" class="er relationshipLabel">
		sale account
	</text>
	<rect height="14" width="104.830078125" y="651.404541015625" x="2124.233642578125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="658.404541015625" x="2176.648681640625" id="rel99" class="er relationshipLabel">
		references voucher
	</text>
	<rect height="14" width="83.001953125" y="1186.1474609375" x="657.9340209960938" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1193.1474609375" x="699.4349975585938" id="rel100" class="er relationshipLabel">
		delivery person
	</text>
	<rect height="14" width="83.9921875" y="1186.126708984375" x="557.4389038085938" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1193.126708984375" x="599.4349975585938" id="rel101" class="er relationshipLabel">
		installer person
	</text>
	<rect height="14" width="104.830078125" y="654.1486206054688" x="2268.66796875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="661.1486206054688" x="2321.0830078125" id="rel102" class="er relationshipLabel">
		references voucher
	</text>
	<rect height="14" width="52.6328125" y="983.91748046875" x="473.11859130859375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="990.91748046875" x="499.43499755859375" id="rel103" class="er relationshipLabel">
		employee
	</text>
	<rect height="14" width="77.072265625" y="1630.646484375" x="5600.26416015625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1637.646484375" x="5638.80029296875" id="rel104" class="er relationshipLabel">
		template type
	</text>
	<rect height="14" width="90.431640625" y="1125.0169677734375" x="4143.9287109375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1132.0169677734375" x="4189.14453125" id="rel105" class="er relationshipLabel">
		notification type
	</text>
	<rect height="14" width="75.982421875" y="1105.15283203125" x="4061.693359375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1112.15283203125" x="4099.6845703125" id="rel106" class="er relationshipLabel">
		uses template
	</text>
	<rect height="14" width="63.427734375" y="1110.1575927734375" x="1553.6400146484375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1117.1575927734375" x="1585.3538818359375" id="rel107" class="er relationshipLabel">
		sender user
	</text>
	<rect height="14" width="92.1015625" y="1189.178955078125" x="791.4930419921875" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1196.178955078125" x="837.5438232421875" id="rel108" class="er relationshipLabel">
		sender employee
	</text>
	<rect height="14" width="44.5390625" y="652.100830078125" x="1474.1680908203125" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="659.100830078125" x="1496.4376220703125" id="rel109" class="er relationshipLabel">
		receives
	</text>
	<rect height="14" width="76.11328125" y="949.5615234375" x="761.3111572265625" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="956.5615234375" x="799.3677978515625" id="rel110" class="er relationshipLabel">
		recipient user
	</text>
	<rect height="14" width="73.796875" y="1138.71337890625" x="62.53656005859375" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1145.71337890625" x="99.43499755859375" id="rel111" class="er relationshipLabel">
		recipient role
	</text>
	<rect height="14" width="104.787109375" y="1539.0478515625" x="258.7196350097656" class="er relationshipLabelBox">
	</rect>
	<text style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;" y="1546.0478515625" x="311.1131896972656" id="rel112" class="er relationshipLabel">
		recipient employee
	</text>
</svg>
