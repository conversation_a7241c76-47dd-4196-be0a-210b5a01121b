"use client";
import "../login/login.css";
import "@src/app/login/login.css";
import React, { useState } from "react";
import {
  Flex,
  Heading,
  Input,
  Button,
  InputGroup,
  Stack,
  Box,
  Avatar,
  FormControl,
  InputRightElement,
} from "@chakra-ui/react";
import { FaUserAlt, FaLock, FaEye, FaEyeSlash } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { useToast } from "@chakra-ui/react";
import Loader from "@components/Loader/Loader";
import { useUser } from "../provider/UserContext";
import log from "@src/app/login/log.svg";
import register from "@src/app/login/register.svg";
import Image from "next/image";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const handleShowClick = () => setShowPassword(!showPassword);

  const [ID, setID] = useState("");
  const [password, setPassword] = useState("");
  const { login, loading } = useUser();

  // const toast = useToast();
  // const router = useRouter();

  const Authentication = (e) => {
    e.preventDefault();
    login(ID, password);
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div class="container20">
          <div class="forms-container">
            <div class="signin-signup">
              <form onSubmit={Authentication} class="sign-in-form">
                <h2 class="title">Sign in</h2>
                <div class="input-field">
                  <div className="icon">
                    <FaUserAlt class="fas fa-user" />
                  </div>
                  {/* <input type="text" placeholder="Username" /> */}
                  <div style={{ paddingRight: "60px" }}>
                    <Input
                      value={ID}
                      onChange={(e) => setID(e.target.value)}
                      type="text"
                      placeholder="User Name"
                      style={{ height: "55px", border: "none" }}
                    />
                  </div>
                </div>
                <div class="input-field">
                  <div className="icon">
                    <FaLock class="fas fa-user" />
                  </div>
                  {/* <input type="password" placeholder="Password" /> */}
                  <InputGroup style={{ paddingRight: "60px" }}>
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      style={{ height: "55px", border: "none" }}
                    />
                    <InputRightElement
                      width="4.5rem"
                      style={{ height: "55px" }}
                    >
                      <Button colorScheme="blackAlpha" color="white" style={{ fontSize: "1.5rem" }} onClick={handleShowClick}>
                        {showPassword ? <FaEyeSlash /> : <FaEye /> }
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                </div>
                <Button
                  style={{ width: "150px", marginTop: "5px" }}
                  bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                  type="submit"
                >
                  LOGIN
                </Button>
              </form>
            </div>
          </div>

          <div class="panels-container">
            <div class="panel left-panel">
              <div class="content">
                <h3 style={{ fontFamily: "'Poppins'" }}>ECO ASSET MANAGER</h3>
                <p style={{ fontFamily: "'__Inter_d65c78'" }}>
                  Enterprise Resource Planning (ERP) software developed and maintaned by NexSol Tech.
                </p>
              </div>
              <Image src={register} className="image" alt="LoginImage" />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Login;
