"use client";
import React, { useEffect, useState } from "react";
import { Box, Button, FormControl, FormLabel, Input, Select, useToast } from "@chakra-ui/react";
import NumberInput  from "@components/Custom/NumberInput/NumberInput";
import { getData } from "@src/app/utils/functions";
import axiosInstance from "@src/app/axios";

const SplitFormField = ({ label1, label2, children1, children2, isDisabled }) => (
  <FormControl sx={{ display: "flex", alignItems: "flex-start", flexDirection: { base: "column", lg: "row" }, mt: 2 }} isDisabled={isDisabled}>
    <FormLabel sx={{ width: { base: "100%", lg: "15%" } }}>{label1}</FormLabel>
    <Box sx={{ width: { base: "100%", lg: "25%" } }}>{children1}</Box>
    <FormLabel sx={{ width: { base: "100%", lg: "10%" }, ml: { base: 0, lg: 2 } }}>{label2}</FormLabel>
    <Box sx={{ width: { base: "100%", lg: "47%" } }}>{children2}</Box>
  </FormControl>
);

const formInitialState = {
  employeeId: '',
  username: '',
  firstName: '',
  lastName: '',
  password: '',
  confirmPassword: '',
  gender: '',
  email: '',
  abn: '',
  licenseNo: '',
  phoneNo: '',
  roleId: '',
  location: ''
};

const DefineEmployees = () => {
  const toast = useToast();
  const [formData, setFormData] = useState(formInitialState);
  const [isDisabled, setIsDisabled] = useState(false);
  const [roles, setRoles] = useState([]);
  const [locations, setLocations] = useState([]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleClear = () => {
    const initialState = { ...formInitialState };
    initialState.employeeId = formData.employeeId;
    setFormData(initialState)
  };

  const isFormValid = () => {
    const requiredFields = { ...formData };
    delete requiredFields.abn; // Optional field
    delete requiredFields.location; // Optional field
    return Object.values(requiredFields).every(field => field !== '') &&
      formData.password === formData.confirmPassword;
  };

  const handleSave = () => {
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Password mismatch",
        description: "Password and confirm password do not match",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    console.log('Form Data:', formData);
    axiosInstance.post("employee/create", formData)
      .then(() => {
        toast({
          title: "Product created",
          description: "The product has been created successfully.",
          status: "success",
          duration: 5000,
          isClosable: true,
        });
        setIsDisabled(true);
      })
      .catch(error => {
        toast({
          title: "An error occurred.",
          description: "Unable to create product.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        console.error('Error creating product:', error);
      });
  };

  useEffect(() => {
    const fetchNextId = async () => {
      try {
        const { nextId } = await getData('employee/next-id');
        const roleList = await getData('getRecords/roles');
        setRoles(roleList);
        setFormData(prev => ({ ...prev, employeeId: nextId }));
      } catch (error) {
        console.error(error);
      }
    };
    const fetchLocations = async () => {
      try {
        const locations = await getData('getRecords/locations');
        setLocations(locations);
      } catch (error) {
        console.error(error);
      }
    };

    fetchNextId();
    fetchLocations();
  }, [])

  return (
    <div className="wrapper">
      <div className="page-inner">
        <Box className="bgWhite" textAlign="center" p={3}>
          <h1 style={{ color: "#2B6CB0", fontSize: "24px", fontWeight: "bold" }}>Define Employee</h1>
        </Box>

        <Box className="bgWhite" p={4} mt={2}>
          <SplitFormField
            label1="Id"
            label2="Username"
            isDisabled={isDisabled}
            children1={<Input name="username" value={formData.employeeId} onChange={handleInputChange} readOnly={true} />}
            children2={<Input name="username" value={formData.username} onChange={handleInputChange} />}
          />

          <SplitFormField
            label1="First Name"
            label2="Last Name"
            isDisabled={isDisabled}
            children1={<Input name="firstName" value={formData.firstName} onChange={handleInputChange} />}
            children2={<Input name="lastName" value={formData.lastName} onChange={handleInputChange} />}
          />

          <SplitFormField
            label1="Password"
            label2="Confirm Password"
            isDisabled={isDisabled}
            children1={<Input name="password" type="password" value={formData.password} onChange={handleInputChange} />}
            children2={<Input name="confirmPassword" type="password" value={formData.confirmPassword} onChange={handleInputChange} />}
          />

          <SplitFormField
            label1="Gender"
            label2="Email"
            isDisabled={isDisabled}
            children1={
              <Select name="gender" value={formData.gender} onChange={handleInputChange} placeholder="Select">
                <option value="0">Male</option>
                <option value="1">Female</option>
                <option value="2">Other</option>
              </Select>
            }
            children2={<Input name="email" type="email" value={formData.email} onChange={handleInputChange} />}
          />

          <SplitFormField
            label1="ABN"
            label2="License no."
            isDisabled={isDisabled}
            children1={<NumberInput name="abn" value={formData.abn} onChange={handleInputChange} placeholder="without hyphens (-)" />}
            children2={<Input name="licenseNo" value={formData.licenseNo} onChange={handleInputChange} />}
          />

          <SplitFormField
            label1="Phone no."
            label2="Role"
            isDisabled={isDisabled}
            children1={<NumberInput name="phoneNo" value={formData.phoneNo} onChange={handleInputChange} placeholder="without hyphens (-)" />}
            children2={
              <Select name="roleId" value={formData.roleId} onChange={handleInputChange} placeholder="Select">
                {roles && Array.isArray(roles) && roles.length > 0 && roles.map(role => (
                  <option key={role.id} value={role.id}>
                    {role.title}
                  </option>
                ))}
              </Select>
            }
          />

          <SplitFormField
            label1="Location"
            label2=""
            isDisabled={isDisabled}
            children2={<Box />}
            children1={<Select name="location" value={formData.location} onChange={handleInputChange} placeholder="Select">
              {locations && Array.isArray(locations) && locations.length > 0 && locations.map(location => (
                <option key={location.id} value={location.id}>
                  {location.title} ({location.id})
                </option>
              ))}
            </Select>}
          />
        </Box>

        <Box textAlign="right" p={3} mt={2}>
          <Button w="20%" colorScheme="red" mr={2} onClick={handleClear} isDisabled={isDisabled}>
            Clear Form
          </Button>
          <Button w="20%" bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} onClick={handleSave} isDisabled={isDisabled || !isFormValid()}>
            {isDisabled ? 'Edit Employee' : 'Create Employee'}
          </Button>
        </Box>
      </div>
    </div>
  );
};

export default DefineEmployees;
