"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx":
/*!************************************************!*\
  !*** ./src/app/follow-up/ComponentToPrint.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/assets/imgs/zipPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/zipPayLogo.jpg\");\n/* harmony import */ var _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @assets/assets/imgs/afterPayLogo.jpg */ \"(app-pages-browser)/./public/assets/imgs/afterPayLogo.jpg\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ComponentToPrint = (param)=>{\n    let { data } = param;\n    var _data_items;\n    _s();\n    const [logoSrc, setLogoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const logoImage = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat((data === null || data === void 0 ? void 0 : data.location) || \"EAM\", \"/logo?fallback=true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Preload the logo image to ensure it's available for PDF generation\n        const img = document.createElement(\"img\");\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            setLogoSrc(logoImage);\n        };\n        img.onerror = ()=>{\n            console.warn(\"Failed to load logo image:\", logoImage);\n            setLogoSrc(\"\"); // Set empty to hide broken image\n        };\n        img.src = logoImage;\n    }, [\n        logoImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        px: 8,\n        pb: 8,\n        pt: 2,\n        maxW: \"800px\",\n        mx: \"auto\",\n        bg: \"white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                justify: \"space-between\",\n                align: \"center\",\n                mb: 6,\n                borderBottom: \"2px solid\",\n                borderColor: \"gray.300\",\n                pb: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        children: logoSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: logoSrc,\n                            alt: \"Eco Assets Manager\",\n                            style: {\n                                height: \"100px\",\n                                width: \"auto\"\n                            },\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"3xl\",\n                                fontWeight: \"bold\",\n                                color: \"black\",\n                                children: \"QUOTATION\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                color: \"red.500\",\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: [\n                                    (data === null || data === void 0 ? void 0 : data.location) || \"EAM\",\n                                    \" \",\n                                    (data === null || data === void 0 ? void 0 : data.vno) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                mb: 2,\n                                textTransform: \"uppercase\",\n                                children: \"INVOICE TO\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.clientTitle) || \"No Client\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.email) || \"No Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.phoneNumber) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                children: (data === null || data === void 0 ? void 0 : data.address) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        textAlign: \"right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontWeight: \"bold\",\n                                fontSize: \"lg\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationTitle) || \"Eco Assets Manager PTY LTD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationABN) || \"No ABN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationAddress) || \"No Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationCity) || \"No City\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.600\",\n                                children: (data === null || data === void 0 ? void 0 : data.locationPhone) || \"No Phone\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                variant: \"simple\",\n                mb: 6,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Thead, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Tr, {\n                            bg: \"#2d6651 !important\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"center\",\n                                    children: \"Quantity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Th, {\n                                    color: \"white\",\n                                    borderColor: \"white\",\n                                    textAlign: \"right\",\n                                    children: \"Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Tbody, {\n                        children: (data === null || data === void 0 ? void 0 : (_data_items = data.items) === null || _data_items === void 0 ? void 0 : _data_items.length) > 0 ? data.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Tr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Td, {\n                                        borderColor: \"gray.300\",\n                                        children: item.Item_Title || item.details || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"center\",\n                                        children: item.Qty || \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Td, {\n                                        borderColor: \"gray.300\",\n                                        textAlign: \"right\",\n                                        children: item.Rate || item.Total ? \"$\".concat(parseFloat(item.Rate || item.Total).toFixed(2)) : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Tr, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Td, {\n                                borderColor: \"gray.300\",\n                                colSpan: 3,\n                                textAlign: \"center\",\n                                color: \"gray.500\",\n                                children: \"No items available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                fontSize: \"sm\",\n                color: \"gray.600\",\n                mb: 6,\n                children: \"* Additional charges may apply subject to installer's assessment *\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                templateColumns: \"1fr 1fr\",\n                gap: 8,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontSize: \"lg\",\n                                fontWeight: \"bold\",\n                                color: \"gray.700\",\n                                mb: 4,\n                                children: \"FINANCING AVAILABLE\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                        align: \"center\",\n                                        mb: 4,\n                                        mt: 2,\n                                        gap: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src || _assets_assets_imgs_zipPayLogo_jpg__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                alt: \"Zip\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src || _assets_assets_imgs_afterPayLogo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                alt: \"Afterpay\",\n                                                style: {\n                                                    width: \"100px\",\n                                                    objectFit: \"contain\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"bold\",\n                                        color: \"gray.700\",\n                                        mb: 2,\n                                        children: \"PAYMENT METHOD\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Bank Name: Bank of Melbourne\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"BSB: 193879\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: \"Account #: *********\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        mt: 2,\n                                        children: \"Please deposit 10% and balance must be paid on installation day.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        children: \"Subtotal Incl. GST\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.totalAmount) ? \"$\".concat(parseFloat(data.totalAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        children: \"EAM discount\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.discountAmount) ? \"$\".concat(parseFloat(data.discountAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                justify: \"space-between\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        children: [\n                                            \"Tax Included (\",\n                                            ((data === null || data === void 0 ? void 0 : data.salesTaxR) || 0) + ((data === null || data === void 0 ? void 0 : data.salesTaxA) || 0),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        fontWeight: \"bold\",\n                                        children: (data === null || data === void 0 ? void 0 : data.sTaxAmount) ? \"$\".concat(parseFloat(data.sTaxAmount).toFixed(2)) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                borderTop: \"1px solid\",\n                                borderColor: \"gray.300\",\n                                pt: 2,\n                                mt: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"VEEC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: (data === null || data === void 0 ? void 0 : data.veecDiscount) ? \"$\".concat(parseFloat(data.veecDiscount).toFixed(2)) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"STC DISCOUNT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                        justify: \"space-between\",\n                                        mb: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"SOLARVIC REBATE\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"#2d6651\",\n                                                fontWeight: \"bold\",\n                                                children: \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        bg: \"gray.100\",\n                                        p: 3,\n                                        borderRadius: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                        textAlign: \"left\",\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        children: \"Total out of pocket\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                        textAlign: \"right\",\n                                                        fontSize: \"sm\",\n                                                        color: \"gray.600\",\n                                                        children: \"incl. GST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                textAlign: \"right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                    fontSize: \"xl\",\n                                                    fontWeight: \"bold\",\n                                                    children: (data === null || data === void 0 ? void 0 : data.netPayableAmt) ? \"$\".concat(parseFloat(data.netPayableAmt).toFixed(2)) : (data === null || data === void 0 ? void 0 : data.netAmount) ? \"$\".concat(parseFloat(data.netAmount).toFixed(2)) : \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                justify: \"space-between\",\n                mt: 16,\n                pt: 8,\n                borderColor: \"gray.300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: (data === null || data === void 0 ? void 0 : data.signature) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.signature,\n                                    alt: \"Customer Signature\",\n                                    style: {\n                                        maxHeight: \"60px\",\n                                        maxWidth: \"100%\",\n                                        objectFit: \"contain\"\n                                    },\n                                    crossOrigin: \"anonymous\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Customer Signature\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        textAlign: \"center\",\n                        width: \"200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                borderBottom: \"1px solid\",\n                                borderColor: \"gray.400\",\n                                height: \"60px\",\n                                mb: 2,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"DD-MMM-YYYY\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                fontWeight: \"bold\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\follow-up\\\\ComponentToPrint.jsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponentToPrint, \"bQCJrah25Bx45JT2l5QhtQPkQ6I=\");\n_c = ComponentToPrint;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ComponentToPrint);\nvar _c;\n$RefreshReg$(_c, \"ComponentToPrint\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/follow-up/ComponentToPrint.jsx\n"));

/***/ })

});