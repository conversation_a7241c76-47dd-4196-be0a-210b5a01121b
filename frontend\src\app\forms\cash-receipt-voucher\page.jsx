"use client";
import React, { useEffect, useState } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import ConfirmDialog from "@components/Custom/ConfirmDialog/ConfirmDialog";
import axiosInstance from "../../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input, Textarea } from "@chakra-ui/react";
import {
  formatDate,
  validateArrayFields,
  validateObjectFields,
} from "@utils/functions";
import {
  CashReceiptVoucherHeaders,
  CashReceiptVoucherSectionFormFields,
  CashReceiptVoucherCalculationFields,
  CreateCashReceiptVoucherInitialFormData,
  CreateCashReceiptVoucherEmptyTableRow,
  CashReceiptVoucherRequiredFields,
  CashReceiptVoucherItemsRequiredFields,
} from "./constant";
import Loader from "@components/Loader/Loader"; // Import your Loader component
import ComponentToPrint from "./ComponentToPrint";
import PrintModal from "@components/PrintModal";

const CashReceiptVoucher = () => {
  const toast = useToast();
  const [cash, setCash] = useState([]);
  const [project, setProject] = useState([]);
  const [offer, setOffer] = useState([]);
  const [filteredOffer, setFilteredOffer] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [isNavigation, setIsNavigation] = useState(false);
  const [tableData, setTableData] = useState(
    CreateCashReceiptVoucherEmptyTableRow()
  );
  const [formData, setFormData] = useState(
    CreateCashReceiptVoucherInitialFormData()
  );
  const [loading, setLoading] = useState(false); // State to control loader visibility
  const [printData, setPrintData] = useState({});
  const [isPrintModalOpen, setPrintModalOpen] = useState(false);

  const dynamicData = {
    cash,
    project,
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;

      if (CashReceiptVoucherCalculationFields.includes(name)) {
        setFormData((prev) => {
          return {
            ...prev,
            [name]: value,
          };
        });
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }));
      }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const handlePrint = async () => {
    try {
      const { data } = await axiosInstance.post('cashVoucher/getbyvoucher', { voucherNo: formData.voucherNo });
      const updatedData = {
        ...data,
        offers: offer,
      };
      setPrintData(updatedData);
      setPrintModalOpen(true);
    } catch (error) {
      console.error(error);
    }
  };

  const transformData = (orderData, itemsArray, isNavigation = false) => {
    if (isNavigation) {
      return itemsArray.map((item) => {
        return {
          AC_ID: item.acc_id,
          Emp_ID: null,
          Narration: item.ln_rem,
          ReceivedAmt: item.Amt,
          Title: item.acc_name,
          Emp_Title: item.Emp_Name,
          AdjVoucherNo: item.adj_VoucherNo || "",
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          acc_id: item.AC_ID,
          adj_VoucherNo: item.AdjVoucherNo || null,
          Currency_ID: "PRs.",
          Emp_ID: null,
          ln_rem: item.Narration,
          Amt: Number(item.ReceivedAmt),
          TotalAmt: Number(item.ReceivedAmt),
        };
      });
    }
  };

  // Toolbar funtions starts here

  const handleDelete = async () => {
    const voucherNo = formData.voucherNo;
    if (!voucherNo) return
    setLoading(true);
    try {
      await axiosInstance.delete(`cashVoucher/delete?voucherNo=${voucherNo}`);
      setLoading(false);
      clearForm();
      toast({
        title: formData.voucherNo + ' voucher deleted successfully',
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: error.response.data || "Cash Payment Voucher Error",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      console.error("Error navigating to voucher form:", error);
      setLoading(false);
    }
  };

  const navigateVoucherForm = async (navigate, voucherNo) => {
    setLoading(true);
    setIsNavigation(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("cashVoucher/navigate", {
        [navigate]: true,
        voucher_vtp: "CR",
        ...(voucherNo && { voucher_no: voucherNo }),
      });
      const resData = response.data;
      const { items } = response.data;
      const form = {
        date: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.vtp,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        cashId: resData?.bc_id ?? "",
        cashTitle: resData?.bc_name ?? "",
        cCenterId: resData?.CostCenter_ID ?? "",
        cCenterName: resData?.CostCenter_Name ?? "",
        narration: resData?.nara ?? "",
        totalAmt: resData?.amt ?? 0,
        totalAmtReceived: resData?.Advance ?? 0,
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      setLoading(false); // Hide loader after login is successful
      toast({
        title: `${navigate.toUpperCase()} Voucher Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error(`Error fetching ${navigate} voucher:`, error);
      setLoading(false); // Hide loader if login fails
      toast({
        title: `${navigate} Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const clearForm = () => {
    setIsNavigation(false);
    setIsDisabled(false);
    setIsEdit(false);
    setFormData(CreateCashReceiptVoucherInitialFormData());
    setTableData(CreateCashReceiptVoucherEmptyTableRow());
    toast({
      title: `Form Cleared`,
      status: "success",
      variant: "left-accent",
      position: "top-right",
      isClosable: true,
    });
  };

  const editForm = () => {
    setIsDisabled((p) => !p);
    setIsEdit((p) => !p);
  };

  const handleSave = () => {
    const data = {
      Dated: formData.date ? formatDate(formData.date) : null,
      vtp: formData.vtp,
      Mnth: formData.mnth,
      Location: formData.location,
      vno: formData.vno,
      amt: formData.totalAmt,
      CostCenter_ID: "",
      bc_id: formData.cashId,
      nara: formData.narration,
      Advance: formData.totalAmtReceived,
      CreationDate: formatDate(new Date()),
      items: transformData(
        {
          Dated: formatDate(formData.date),
          VTP: formData.vtp,
          Mnth: formData.mnth,
          Location: formData.location,
          vno: formData.vno,
        },
        tableData
      ),
    };
    const isValidateObjectFields = validateObjectFields(
      data,
      CashReceiptVoucherRequiredFields
    );
    const isValidateArrayFields = validateArrayFields(
      data.items,
      CashReceiptVoucherItemsRequiredFields
    );
    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateArrayFields.error) {
      toast({
        title: isValidateArrayFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {
      setLoading(true); // Show loader when login is initiated
      if (isEdit) {
        const voucherNo = formData.voucherNo;
        axiosInstance({
          method: "put",
          url: `cashVoucher/update?voucherNo=${voucherNo}`,
          data: data
        })
          .then((response) => {
            editForm();
            toast({
              title: "Voucher modified successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error modifing voucher:", error);
            toast({
              title: "Error modifing voucher :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      } else {
        axiosInstance({
          method: "post",
          url: "cashVoucher/create",
          data: data,
        })
          .then((response) => {
            clearForm();
            toast({
              title: "Voucher saved successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false); // Hide loader after login is successful
          })
          .catch((error) => {
            console.error("Error saving voucher:", error);
            toast({
              title: "Error saving voucher :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false); // Hide loader if login fails
          });
      }
    } else {
      toast({
        title:
          "Please fill out all required fields and ensure all items have valid fields",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  // Toolbar funtions end here

  const accRowClickHandler = (data, rowIndex, colIndex) => {
    setTableData((prev) => {
      const updatedTableData = [...prev];
      const remainingAmount = data.remaining || 0;
      updatedTableData[rowIndex] = {
        ...updatedTableData[rowIndex],
        AC_ID: data.clientID || "",
        Title: data.clientName || "",
        AdjVoucherNo: data.voucherNo || "",
        TotalAmount: data.total || 0,
        PaidAmount: data.paid || 0,
        RemainingAmount: remainingAmount,
        ReceivedAmt: remainingAmount // Auto-populate with remaining amount
      };
      return updatedTableData;
    });
  };

  const empRowClickHandler = (data, rowIndex, colIndex) => {
    setTableData((prev) => {
      const updatedTableData = [...prev];
      updatedTableData[rowIndex] = {
        ...updatedTableData[rowIndex],
        Emp_ID: data.ID ? data.ID : "",
        Emp_Title: data.Title ? data.Title : "",
      };
      return updatedTableData;
    });
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    if (["AC_ID", "Title", "AdjVoucherNo"].includes(key)) {
      return (
        <TableComboBox
          setTableData={setTableData}
          rowIndex={rowIndex}
          colIndex={colIndex}
          inputWidth={cellData.width}
          value={value}
          onChange={(val) => handleInputChange(val)}
          modalData={filteredOffer}
          tableData={tableData}
          modalHeaders={['Voucher No', 'Client ID','Client Name', 'Total', 'Paid', 'Remaining']}
          rowClickHandler={accRowClickHandler}
          isDisabled={isDisabled}
        />
      );
    }
    return (
      <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        isDisabled={isDisabled}
      />
    );
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          let updatedRow = { ...r, [header.key]: value };

          // Validation for ReceivedAmt field
          if (header.key === 'ReceivedAmt') {
            const remainingAmount = parseFloat(r.RemainingAmount) || 0;
            const receivedAmount = parseFloat(value) || 0;

            if (receivedAmount > remainingAmount) {
              toast({
                title: "Invalid Amount",
                description: `Received amount (${receivedAmount}) cannot be greater than remaining amount (${remainingAmount})`,
                status: "error",
                duration: 3000,
                isClosable: true,
              });
              // Reset to remaining amount
              updatedRow = { ...updatedRow, ReceivedAmt: remainingAmount };
            }


          }

          return updatedRow;
        }
        return r;
      });
    });
  };

  useEffect(() => {
    let cashAmount = 0;

    tableData.forEach((data) => {
      cashAmount += Number(data.ReceivedAmt) || 0;
    });

    setFormData((prev) => ({
      ...prev,
      totalAmt: cashAmount,
    }));
  }, [tableData]);

  // Filter offers to exclude already selected ones
  useEffect(() => {
    const selectedVoucherNos = tableData
      .map(row => row.AdjVoucherNo)
      .filter(voucherNo => voucherNo && voucherNo.trim() !== '');

    const filtered = offer.filter(offerItem =>
      !selectedVoucherNos.includes(offerItem.voucherNo)
    );

    setFilteredOffer(filtered);
  }, [tableData, offer]);

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true); // Show loader when login is initiated
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post("cashVoucher/getVoucherNo", {
          Mnth: year.toString(),
          vtp: "CR",
        });
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            location,
            vno,
            vtp,
            Mnth,
            voucherNo,
            mnth: Mnth,
          }));
          setLoading(false); // Hide loader after login is successful
          toast({
            title: "Voucher No. Generated Successfully !",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false); // Hide loader if login fails
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const salesManData = await getData("getRecords/salesMan");
    const projects = await getData("getRecords/projects");
    const offerData = await getData("getRecords/offers");
    const cashData = await getData("getRecords/cashAccounts");
    setOffer(offerData);
    setProject(projects);
    setCash(cashData);
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? ( // Show loader when loading is true
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1 className="PageHeading">Cash Receipt Voucher</h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(30% - 5px) !important",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <form>
                        {CashReceiptVoucherSectionFormFields[0].map((field) => (
                          <FormControl
                            key={field.id}
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                md: "row",
                              },
                              marginTop: "10px",
                            }}
                            isRequired={field.isRequired}
                          >
                            <FormLabel
                              htmlFor={field.id}
                              sx={{
                                marginBottom: "0",
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  md: "20%",
                                  lg: "35%",
                                },
                              }}
                            >
                              {field.label}
                            </FormLabel>
                            {field.type === "date" ? (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                // min={field.minDate}
                                // max={field.maxDate}
                                disabled={isEdit || isDisabled}
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            ) : (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                disabled={field.name === 'voucherNo' ? (isEdit || isDisabled) : isDisabled}
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            )}
                          </FormControl>
                        ))}
                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                            onClick={() => getVoucherNo(formData.date)}
                            isDisabled={isEdit || isDisabled}>
                            Generate Voucher No
                          </Button>
                        </FormControl>
                      </form>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(70% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      {CashReceiptVoucherSectionFormFields[1].map(
                        (control, index) => (
                          <FormControl
                            key={index}
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                              marginTop: "10px",
                              flexWrap: "nowrap",
                            }}
                            isRequired={control.isRequired}
                          >
                            <FormLabel
                              htmlFor={control.fields[0].name}
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "20%" },
                              }}
                            >
                              {control.label}
                            </FormLabel>
                            <Box
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "80%" },
                                display: "flex",
                                gap: control.fields.length > 1 ? "10px" : "0",
                              }}
                            >
                              {control.fields.map((field, fieldIndex) =>
                                field.component === "ComboBox" ? (
                                  <ComboBox
                                    key={fieldIndex}
                                    target={true}
                                    onChange={handleInputChange}
                                    name={field.name}
                                    inputWidths={field.inputWidths}
                                    buttonWidth={field.buttonWidth}
                                    styleButton={{ padding: "3px !important" }}
                                    tableData={dynamicData[field.tableData]}
                                    tableHeaders={field.tableHeaders}
                                    nameFields={field.nameFields}
                                    placeholders={field.placeholders}
                                    keys={field.keys}
                                    form={formData}
                                    isDisabled={isDisabled}
                                  />
                                ) : (
                                  <Button
                                    bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                                    key={fieldIndex}
                                    style={{ width: field.inputWidth }}
                                    isDisabled={isDisabled}
                                  >
                                    {field.value}
                                  </Button>
                                )
                              )}
                            </Box>
                          </FormControl>
                        )
                      )}
                    </Box>
                  </div>
                  <div className="row">
                    <div
                      style={{ padding: "0" }}
                      className="bgWhite col-md-12 mt-2"
                    >
                      <AanzaDataTable
                        tableData={tableData}
                        setTableData={setTableData}
                        headers={CashReceiptVoucherHeaders}
                        tableWidth="100%"
                        tableHeight="400px"
                        fontSize="lg"
                        cellRender={cellRender}
                        onSave={handleSave}
                        styleHead={{
                          background: "#3275bb",
                          color: "white !important",
                        }}
                        styleBody={{ background: "white !important" }}
                        calculation={calculation}
                        isDisabled={isDisabled}
                      />
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      <div
                        style={{
                          display: "grid",
                          gridTemplateColumns:
                            "repeat(auto-fit,minmax(300px,1fr))",
                          gap: "5px",
                        }}
                      >
                        {CashReceiptVoucherSectionFormFields[2].map(
                          (group, index) => (
                            <FormControl
                              key={index}
                              style={{ display: "flex", gap: "4px" }}
                            >
                              {group.fields ? (
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "flex-start",
                                    width: "100%",
                                    flexDirection: {
                                      base: "column",
                                      sm: "column",
                                      lg: "row",
                                    },
                                  }}
                                >
                                  <FormLabel
                                    sx={{
                                      width: {
                                        base: "100%",
                                        sm: "100%",
                                        lg: "15%",
                                      },
                                    }}
                                  >
                                    {group.label}
                                  </FormLabel>
                                  <Box
                                    sx={{
                                      width: {
                                        base: "100%",
                                        sm: "100%",
                                        lg: "85%",
                                      },
                                      display: "flex",
                                    }}
                                  >
                                    {group.fields.map((field, fieldIndex) =>
                                      field.isPercentage ? (
                                        <Input
                                          key={fieldIndex}
                                          onChange={(e) => {
                                            handleInputChange({
                                              target: {
                                                name: field.name,
                                                value:
                                                  e.target.value.toString(),
                                              },
                                            });
                                          }}
                                          name={field.name}
                                          placeholder={field.label}
                                          value={field.value}
                                          _placeholder={{ color: "gray.500" }}
                                          type="text"
                                          sx={field.style}
                                          isReadOnly={field.isReadOnly}
                                          min={0}
                                          max={100}
                                          isDisabled={isDisabled}
                                        />
                                      ) : (
                                        <Input
                                          key={fieldIndex}
                                          onChange={handleInputChange}
                                          name={field.name}
                                          placeholder={field.label}
                                          value={field.value}
                                          _placeholder={{ color: "gray.500" }}
                                          type="number"
                                          sx={field.style}
                                          isReadOnly={field.isReadOnly}
                                          isDisabled={isDisabled}
                                        />
                                      )
                                    )}
                                  </Box>
                                </Box>
                              ) : (
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "flex-start",
                                    width: "100%",
                                    marginTop: "10px",
                                    flexDirection: {
                                      base: "column",
                                      sm: "column",
                                      lg: "row",
                                    },
                                  }}
                                >
                                  <FormLabel
                                    sx={{
                                      width: {
                                        base: "100%",
                                        sm: "100%",
                                        lg: "30%",
                                      },
                                    }}
                                  >
                                    {group.label}
                                  </FormLabel>
                                  <Input
                                    onChange={handleInputChange}
                                    name={group.name}
                                    value={formData[group.value]}
                                    placeholder=""
                                    _placeholder={{ color: "gray.500" }}
                                    type="number"
                                    sx={group.style}
                                    isReadOnly={group.isReadOnly}
                                    isDisabled={isDisabled}
                                  />
                                </Box>
                              )}
                            </FormControl>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "10%" },
                          }}
                        >
                          Narration
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "90%" },
                          }}
                          onChange={handleInputChange}
                          name={"narration"}
                          value={formData.narration}
                          isDisabled={isDisabled}
                        />
                      </FormControl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Toolbar
            save={handleSave}
            clear={clearForm}
            edit={editForm}
            first={() => navigateVoucherForm("first")}
            last={() => navigateVoucherForm("last")}
            previous={() => navigateVoucherForm("prev", formData.voucherNo)}
            next={() => navigateVoucherForm("next", formData.voucherNo)}
            remove={() => setDialogOpen(true)}
            isNavigation={isNavigation}
            isEdit={isEdit}
            print={handlePrint}
          />
        </>
      )}
      <ConfirmDialog
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Voucher"
        message={
          <>Are you sure you want to delete voucher no <b>{formData.voucherNo}</b> ?</>
        }
        confirmText="Delete"
        cancelText="Cancel"
        confirmColorScheme="red"
      />
      <PrintModal isOpen={isPrintModalOpen} onClose={() => setPrintModalOpen(false)} formName="Cash Receipt Voucher">
        <ComponentToPrint data={printData} />
      </PrintModal>
    </>
  );
};

export default CashReceiptVoucher;
