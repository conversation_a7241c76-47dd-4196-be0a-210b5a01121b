const express = require('express');
const router = express.Router();
const employeeController = require('../controllers/employeeController');
const employeeValidationRules = require('../validations/employeeValidation');
const authorization = require('../middleware/authorization');

router.use(authorization);

/**
 * @swagger
 * /api/employee:
 *   post:
 *     summary: Create a new employee
 *     tags: [Employees]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Employee'
 *     responses:
 *       201:
 *         description: Employee created successfully
 *       400:
 *         description: Bad request - Invalid input
 *       500:
 *         description: Server error
 */
router.post('/create', employeeValidationRules.create, employeeController.createEmployee);

/**
 * @swagger
 * /api/employee/next-id:
 *   get:
 *     summary: Get the next employee ID
 *     tags: [Employees]
 *     responses:
 *       200:
 *         description: Next employee ID generated successfully
 *       500:
 *         description: Server error
 */
router.get('/next-id', employeeController.getNextEmployeeId);

/**
 * @swagger
 * /api/employee/{id}:
 *   get:
 *     summary: Get an employee by ID
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee retrieved successfully
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 */
router.get('/:id', employeeController.getEmployeeById);

/**
 * @swagger
 * /api/employee:
 *   get:
 *     summary: Get all employees
 *     tags: [Employees]
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/', employeeController.getAllEmployees);

/**
 * @swagger
 * /api/employee/{id}:
 *   put:
 *     summary: Update an employee by ID
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               phoneNo:
 *                 type: string
 *               location:
 *                 type: string
 *               roleId:
 *                 type: integer
 *               abn:
 *                 type: string
 *               licenseNo:
 *                 type: string
 *     responses:
 *       200:
 *         description: Employee updated successfully
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 */
router.put('/:id', employeeValidationRules.update, employeeController.updateEmployee);

module.exports = router;
