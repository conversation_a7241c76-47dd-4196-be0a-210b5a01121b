"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/follow-up/page",{

/***/ "(app-pages-browser)/./src/components/PrintModal.jsx":
/*!***************************************!*\
  !*** ./src/components/PrintModal.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-to-print */ \"(app-pages-browser)/./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/provider/UserContext */ \"(app-pages-browser)/./src/app/provider/UserContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PrintModal = (param)=>{\n    let { isOpen, onClose, children, formName = \"Form Name\", showHeader = true, buttonText = \"Print\", callback, paddingTop = 12 } = param;\n    _s();\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { user } = (0,_src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const userLocation = (user === null || user === void 0 ? void 0 : user.Location) || (user === null || user === void 0 ? void 0 : user.location) || \"\";\n    const logoSrc = \"\".concat( true ? \"http://localhost:5002/api/\" : 0, \"godown/\").concat(userLocation, \"/logo?fallback=true\");\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint)({\n        contentRef: componentRef\n    });\n    // Helper function to convert image URL to base64\n    const convertImageToBase64 = async (url)=>{\n        try {\n            const response = await fetch(url);\n            const blob = await response.blob();\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = ()=>resolve(reader.result);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        } catch (error) {\n            console.warn(\"Failed to convert image to base64:\", error);\n            return null;\n        }\n    };\n    // Helper function to wait for all images to load\n    const waitForImages = async (element)=>{\n        const images = element.querySelectorAll(\"img\");\n        const imagePromises = Array.from(images).map(async (img)=>{\n            if (img.complete) return;\n            return new Promise((resolve, reject)=>{\n                const timeout = setTimeout(()=>{\n                    console.warn(\"Image load timeout:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                }, 5000);\n                img.onload = ()=>{\n                    clearTimeout(timeout);\n                    resolve();\n                };\n                img.onerror = ()=>{\n                    clearTimeout(timeout);\n                    console.warn(\"Image load error:\", img.src);\n                    resolve(); // Resolve anyway to not block the process\n                };\n            });\n        });\n        await Promise.all(imagePromises);\n    };\n    const handleGeneratePDF = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            console.log(\"Starting PDF generation...\");\n            // Wait for all images to load\n            await waitForImages(componentRef.current);\n            console.log(\"All images loaded\");\n            // Convert external images to base64 to avoid CORS issues\n            const images = componentRef.current.querySelectorAll(\"img\");\n            console.log(\"Found \".concat(images.length, \" images to process\"));\n            for (const img of images){\n                if (img.src.startsWith(\"http\") && !img.src.includes(\"data:\")) {\n                    console.log(\"Converting image to base64:\", img.src);\n                    try {\n                        const base64 = await convertImageToBase64(img.src);\n                        if (base64) {\n                            img.src = base64;\n                            console.log(\"Successfully converted image to base64\");\n                        }\n                    } catch (error) {\n                        console.warn(\"Failed to convert image:\", img.src, error);\n                    }\n                }\n            }\n            // Small delay to ensure DOM updates\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            console.log(\"Starting html2canvas...\");\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(componentRef.current, {\n                scale: 2,\n                useCORS: true,\n                allowTaint: true,\n                backgroundColor: \"#ffffff\",\n                logging: false,\n                onclone: (clonedDoc)=>{\n                    // Ensure all images in the cloned document are properly loaded\n                    const clonedImages = clonedDoc.querySelectorAll(\"img\");\n                    console.log(\"Found \".concat(clonedImages.length, \" images in cloned document\"));\n                    clonedImages.forEach((img)=>{\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                    });\n                }\n            });\n            console.log(\"html2canvas completed successfully\");\n            const imgData = canvas.toDataURL(\"image/png\");\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"](\"p\", \"mm\", \"a4\");\n            // Calculate dimensions to fit the page properly\n            const pdfWidth = pdf.internal.pageSize.getWidth();\n            const pdfHeight = pdf.internal.pageSize.getHeight();\n            const canvasAspectRatio = canvas.height / canvas.width;\n            const pdfAspectRatio = pdfHeight / pdfWidth;\n            let imgWidth, imgHeight;\n            if (canvasAspectRatio > pdfAspectRatio) {\n                imgHeight = pdfHeight - 20; // 10mm margin on top and bottom\n                imgWidth = imgHeight / canvasAspectRatio;\n            } else {\n                imgWidth = pdfWidth - 20; // 10mm margin on left and right\n                imgHeight = imgWidth * canvasAspectRatio;\n            }\n            const x = (pdfWidth - imgWidth) / 2;\n            const y = (pdfHeight - imgHeight) / 2;\n            pdf.addImage(imgData, \"PNG\", x, y, imgWidth, imgHeight);\n            const pdfBlob = pdf.output(\"blob\");\n            if (callback) {\n                callback(pdfBlob);\n            } else {\n                handlePrint();\n            }\n        } catch (error) {\n            console.error(\"Error generating PDF:\", error);\n            toast({\n                title: \"Error generating PDF\",\n                description: \"Please try again or contact support if the issue persists.\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true,\n                position: \"top-right\"\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalContent, {\n                height: \"100vh\",\n                width: \"auto\",\n                overflow: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalHeader, {\n                        children: \"Print Preview\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalBody, {\n                        maxHeight: \"90vh\",\n                        overflow: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Box, {\n                            ref: componentRef,\n                            minWidth: \"700px\",\n                            className: \"print-container\",\n                            paddingTop: paddingTop,\n                            children: [\n                                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            style: {\n                                                display: \"flex\",\n                                                justifyContent: \"center\",\n                                                alignItems: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                alt: \"logo\",\n                                                src: logoSrc,\n                                                style: {\n                                                    height: \"70px\",\n                                                    width: \"auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            fontSize: \"xl\",\n                                            fontStyle: \"italic\",\n                                            textAlign: \"center\",\n                                            margin: \"20px 0 20px 0 !important\",\n                                            children: formName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                children\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalFooter, {\n                        gap: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            bg: \"#2d6651\",\n                            color: \"white\",\n                            _hover: {\n                                bg: \"#3a866a\"\n                            },\n                            onClick: handleGeneratePDF,\n                            isLoading: isGenerating,\n                            loadingText: \"Generating PDF...\",\n                            children: buttonText\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\PrintModal.jsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrintModal, \"GEIWinyei48BORxa3W6Bbzad+uA=\", false, function() {\n    return [\n        _src_app_provider_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        react_to_print__WEBPACK_IMPORTED_MODULE_2__.useReactToPrint\n    ];\n});\n_c = PrintModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PrintModal);\nvar _c;\n$RefreshReg$(_c, \"PrintModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PrintModal.jsx\n"));

/***/ })

});