import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>lay,
    <PERSON>dal<PERSON>ontent,
    Modal<PERSON>eader,
    ModalCloseButton,
    ModalBody,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Badge,
    Text,
    VStack,
    HStack,
    Divider,
    Spinner,
    Box,
    Grid,
    GridItem,
    Card,
    CardBody,
    Icon,
    Flex
} from '@chakra-ui/react';
import { keyframes } from "@emotion/react";
import dayjs from 'dayjs';
import { FiDollarSign, FiUser, FiFileText, FiCalendar, FiCreditCard, FiTrendingUp } from 'react-icons/fi';

// Define animations
const slideIn = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const VoucherDetailsModal = ({
    isOpen,
    onClose,
    voucherDetails,
    paymentHistory,
    modalLoading
}) => {
    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'green';
            case 'pending': return 'red';
            case 'partial': return 'yellow';
            default: return 'gray';
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount || 0);
    };

    const getPaymentTypeColor = (type) => {
        switch (type) {
            case 'CR': return 'green';
            case 'CP': return 'blue';
            case 'BR': return 'orange';
            case 'BP': return 'red';
            default: return 'gray';
        }
    };

    const getPaymentTypeLabel = (type) => {
        switch (type) {
            case 'CR': return 'Cash Receipt';
            case 'CP': return 'Cash Payment';
            case 'BR': return 'Bank Receipt';
            case 'BP': return 'Bank Payment';
            default: return type || 'Unknown';
        }
    };

    return (
        <Modal 
            isOpen={isOpen} 
            onClose={onClose} 
            size="6xl"
            scrollBehavior="inside"
            motionPreset="scale"
            isCentered
        >
            <ModalOverlay 
                bg="blackAlpha.300"
                backdropFilter="blur(10px)"
                sx={{
                    animation: `${fadeIn} 0.2s ease-out`
                }}
            />
            <ModalContent
                maxH="90vh"
                my={4}
                sx={{
                    animation: `${slideIn} 0.3s ease-out`,
                    bg: "white",
                    boxShadow: "xl",
                    display: "flex",
                    flexDirection: "column"
                }}
            >
                <ModalHeader
                    bgGradient="linear(to-r, #3a866a, #2d6651)"
                    color="white"
                    borderTopRadius="md"
                    px={6}
                    py={4}
                    position="sticky"
                    top={0}
                    zIndex={1}
                    flexShrink={0}
                >
                    <Flex align="center" gap={3}>
                        <Icon as={FiFileText} boxSize={6} />
                        <VStack align="start" spacing={0}>
                            <Text fontSize="xl" fontWeight="bold">
                                Voucher Details
                            </Text>
                        </VStack>
                    </Flex>
                </ModalHeader>
                <ModalCloseButton 
                    color="white" 
                    _hover={{
                        bg: "whiteAlpha.300",
                        transform: "rotate(90deg)"
                    }}
                    transition="all 0.2s"
                />
                
                <ModalBody
                    px={6}
                    py={6}
                    flex="1"
                    overflowY="auto"
                    sx={{
                        "&::-webkit-scrollbar": {
                            width: "6px",
                        },
                        "&::-webkit-scrollbar-track": {
                            background: "#f1f1f1",
                            borderRadius: "4px",
                        },
                        "&::-webkit-scrollbar-thumb": {
                            background: "#2d6651",
                            borderRadius: "4px",
                            "&:hover": {
                                background: "#3a866a",
                            },
                        },
                    }}
                >
                    {modalLoading ? (
                        <Flex justify="center" align="center" minH="300px">
                            <VStack spacing={4}>
                                <Spinner size="xl" color="green.500" thickness="4px" />
                                <Text color="gray.600" fontSize="lg">Loading voucher details...</Text>
                            </VStack>
                        </Flex>
                    ) : voucherDetails ? (
                        <VStack spacing={8} align="stretch">
                            {/* Voucher Summary Card */}
                            <Card 
                                variant="elevated" 
                                shadow="lg"
                                borderTop="4px solid"
                                borderTopColor="green.500"
                            >
                                <CardBody p={6}>
                                    <VStack spacing={6} align="stretch">
                                        <Flex justify="space-between" align="center">
                                            <Text fontSize="xl" fontWeight="bold" color="green.600">
                                                Voucher Summary
                                            </Text>
                                            <Badge 
                                                colorScheme={getStatusColor(voucherDetails.status)}
                                                fontSize="md"
                                                px={4}
                                                py={2}
                                                borderRadius="full"
                                                textTransform="uppercase"
                                                fontWeight="bold"
                                            >
                                                {voucherDetails.status}
                                            </Badge>
                                        </Flex>
                                        
                                        <Grid templateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={6}>
                                            <GridItem>
                                                <VStack align="start" spacing={2}>
                                                    <HStack>
                                                        <Icon as={FiFileText} color="purple.500" />
                                                        <Text fontSize="sm" color="gray.600" fontWeight="600">Quotation No</Text>
                                                    </HStack>
                                                    <Text fontWeight="bold" fontSize="lg">{voucherDetails.quotation_no || 'N/A'}</Text>
                                                </VStack>
                                            </GridItem>
                                            
                                            <GridItem>
                                                <VStack align="start" spacing={2}>
                                                    <HStack>
                                                        <Icon as={FiUser} color="green.500" />
                                                        <Text fontSize="sm" color="gray.600" fontWeight="600">Client</Text>
                                                    </HStack>
                                                    <Text fontWeight="bold" fontSize="lg">{voucherDetails.client_name}</Text>
                                                    <Text fontSize="sm" color="gray.500">ID: {voucherDetails.client_id}</Text>
                                                </VStack>
                                            </GridItem>
                                            
                                            <GridItem>
                                                <VStack align="start" spacing={2}>
                                                    <HStack>
                                                        <Icon as={FiCalendar} color="orange.500" />
                                                        <Text fontSize="sm" color="gray.600" fontWeight="600">Created Date</Text>
                                                    </HStack>
                                                    <Text fontWeight="bold" fontSize="lg">
                                                        {voucherDetails.created_at ? 
                                                            dayjs(voucherDetails.created_at).format('DD MMM, YYYY') : 
                                                            'N/A'
                                                        }
                                                    </Text>
                                                </VStack>
                                            </GridItem>
                                        </Grid>
                                        
                                        <Divider />
                                        
                                        {/* Amount Summary */}
                                        <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6}>
                                            <GridItem>
                                                <Card bg="blue.50" borderLeft="4px solid" borderLeftColor="blue.500">
                                                    <CardBody p={4}>
                                                        <VStack align="start" spacing={2}>
                                                            <HStack>
                                                                <Icon as={FiDollarSign} color="blue.500" />
                                                                <Text fontSize="sm" color="blue.600" fontWeight="600">Gross Amount</Text>
                                                            </HStack>
                                                            <Text fontWeight="bold" fontSize="2xl" color="blue.700">
                                                                {formatCurrency(voucherDetails.gross_amount)}
                                                            </Text>
                                                        </VStack>
                                                    </CardBody>
                                                </Card>
                                            </GridItem>
                                            
                                            <GridItem>
                                                <Card bg="green.50" borderLeft="4px solid" borderLeftColor="green.500">
                                                    <CardBody p={4}>
                                                        <VStack align="start" spacing={2}>
                                                            <HStack>
                                                                <Icon as={FiTrendingUp} color="green.500" />
                                                                <Text fontSize="sm" color="green.600" fontWeight="600">Paid Amount</Text>
                                                            </HStack>
                                                            <Text fontWeight="bold" fontSize="2xl" color="green.700">
                                                                {formatCurrency(voucherDetails.paid_amount)}
                                                            </Text>
                                                        </VStack>
                                                    </CardBody>
                                                </Card>
                                            </GridItem>
                                            
                                            <GridItem>
                                                <Card bg="red.50" borderLeft="4px solid" borderLeftColor="red.500">
                                                    <CardBody p={4}>
                                                        <VStack align="start" spacing={2}>
                                                            <HStack>
                                                                <Icon as={FiCreditCard} color="red.500" />
                                                                <Text fontSize="sm" color="red.600" fontWeight="600">Remaining Amount</Text>
                                                            </HStack>
                                                            <Text fontWeight="bold" fontSize="2xl" color="red.700">
                                                                {formatCurrency(voucherDetails.remaining_amount)}
                                                            </Text>
                                                        </VStack>
                                                    </CardBody>
                                                </Card>
                                            </GridItem>
                                        </Grid>
                                    </VStack>
                                </CardBody>
                            </Card>

                            {/* Payment History Card */}
                            <Card variant="elevated" shadow="lg">
                                <CardBody p={6}>
                                    <VStack spacing={4} align="stretch">
                                        <Flex justify="space-between" align="center">
                                            <Text fontSize="xl" fontWeight="bold" color="green.600">
                                                Payment History
                                            </Text>
                                            <Badge 
                                                bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} 
                                                variant="subtle"
                                                fontSize="sm"
                                                px={3}
                                                py={1}
                                                borderRadius="full"
                                            >
                                                {paymentHistory.length} payment{paymentHistory.length !== 1 ? 's' : ''}
                                            </Badge>
                                        </Flex>
                                        
                                        {paymentHistory.length > 0 ? (
                                            <TableContainer 
                                                border="1px solid" 
                                                borderColor="gray.200" 
                                                borderRadius="lg"
                                                bg="white"
                                            >
                                                <Table variant="simple" size="md">
                                                    <Thead bg="gray.50">
                                                        <Tr>
                                                            <Th color="gray.700" fontWeight="bold">Payment Voucher</Th>
                                                            <Th color="gray.700" fontWeight="bold">Type</Th>
                                                            <Th color="gray.700" fontWeight="bold" isNumeric>Amount</Th>
                                                            <Th color="gray.700" fontWeight="bold">Date</Th>
                                                            <Th color="gray.700" fontWeight="bold">Description</Th>
                                                        </Tr>
                                                    </Thead>
                                                    <Tbody>
                                                        {paymentHistory.map((payment, index) => (
                                                            <Tr key={index} _hover={{ bg: "green.50" }} transition="all 0.2s">
                                                                <Td>
                                                                    <Badge 
                                                                        colorScheme={getPaymentTypeColor(payment.payment_type)}
                                                                        variant="subtle"
                                                                        px={2}
                                                                        py={1}
                                                                        borderRadius="md"
                                                                    >
                                                                        {getPaymentTypeLabel(payment.payment_type)}
                                                                    </Badge>
                                                                </Td>
                                                                <Td isNumeric fontWeight="bold" color="green.600" fontSize="lg">
                                                                    {formatCurrency(payment.payment_amount)}
                                                                </Td>
                                                                <Td color="gray.600">
                                                                    {payment.payment_date ? 
                                                                        dayjs(payment.payment_date).format('DD MMM, YYYY hh:mm A') : 
                                                                        'N/A'
                                                                    }
                                                                </Td>
                                                                <Td color="gray.600">
                                                                    {payment.payment_description || 'No description'}
                                                                </Td>
                                                            </Tr>
                                                        ))}
                                                    </Tbody>
                                                </Table>
                                            </TableContainer>
                                        ) : (
                                            <Card bg="gray.50" border="2px dashed" borderColor="gray.300">
                                                <CardBody p={12} textAlign="center">
                                                    <VStack spacing={4}>
                                                        <Icon as={FiCreditCard} boxSize={12} color="gray.400" />
                                                        <VStack spacing={2}>
                                                            <Text color="gray.600" fontSize="lg" fontWeight="semibold">
                                                                No payments found
                                                            </Text>
                                                            <Text color="gray.500" fontSize="sm">
                                                                This voucher is still pending payment
                                                            </Text>
                                                        </VStack>
                                                    </VStack>
                                                </CardBody>
                                            </Card>
                                        )}
                                    </VStack>
                                </CardBody>
                            </Card>
                        </VStack>
                    ) : (
                        <Card bg="gray.50" border="2px dashed" borderColor="gray.300">
                            <CardBody p={12} textAlign="center">
                                <VStack spacing={4}>
                                    <Icon as={FiFileText} boxSize={12} color="gray.400" />
                                    <Text color="gray.600" fontSize="lg">No details available</Text>
                                </VStack>
                            </CardBody>
                        </Card>
                    )}
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};

export default VoucherDetailsModal;
