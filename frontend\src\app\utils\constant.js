// Constants Here-----------------------------------------------------------------------------

// Client Order Starts Here----------------------------------------------------------------

export const ClientOrderHeaders = [
    {
        label: "Item ID",
        key: "Item_ID",
        width: "100px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Item Title",
        key: "Item_Title",
        width: "300px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Unit",
        key: "unit",
        width: "60px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Remarks",
        key: "remarks",
        width: "320px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Charges",
        key: "Installation_Charges",
        width: "100px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Qty",
        key: "qty",
        width: "70px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Rate",
        key: "rate",
        width: "70px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Total",
        key: "total",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
];

export const ClientOrderSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "Date",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Voucher No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Client",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "clients",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    keys: ["id", "title"],
                    value: {
                        clientId: "clientId",
                        clientTitle: "clientTitle",
                    },
                    nameFields: ["clientId", "clientTitle"],
                },
            ],
        },
        {
            label: "Sales Man",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "salesMan",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    keys: ["ID", "Title"],
                    value: {
                        salesManId: "salesManId",
                        salesManName: "salesManName",
                    },
                    nameFields: ["salesManId", "salesManName"],
                },
            ],
        },
        {
            label: "Delivery Person",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "deliveryMen",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    keys: ["ID", "Title"],
                    value: {
                        deliveryPersonId: "deliveryPersonId",
                        deliveryPersonName: "deliveryPersonName",
                    },
                    nameFields: ["deliveryPersonId", "deliveryPersonName"],
                },
            ],
        },
        {
            label: "Delivery Time",
            isRequired: true,
            fields: [
                {
                    component: "Input",
                    name: "deliveryPersonTime",
                    value: "deliveryPersonTime",
                    type: "date",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Installer",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "installers",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    keys: ["ID", "Title"],
                    value: {
                        installerId: "installerId",
                        installerName: "installerName",
                    },
                    nameFields: ["installerId", "installerName"],
                },
            ],
        },
        {
            label: "Installation Time",
            isRequired: true,
            fields: [
                {
                    component: "Input",
                    name: "installationTime",
                    value: "installationTime",
                    type: "date",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "PO No.",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "poNo",
                    value: "poNo",
                    type: "text",
                    placeholder: "",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "PO Date",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "po_date",
                    value: "po_date",
                    type: "date",
                    placeholder: "",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
    ], [
        {
            label: "Gross Amt",
            name: "grossAmt",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "grossAmt",
            isReadOnly: true
        },
        {
            label: "S.Tax%",
            style: {
                width: { base: "100%", sm: "100%", lg: "70%" },
                display: "flex",
            },
            fields: [
                {
                    label: "R",
                    name: "salesTaxR",
                    style: { width: { base: "100%", sm: "100%", lg: "30%" } },
                    value: "salesTaxR",
                    isReadOnly: false,
                    isPercentage: true
                },
                {
                    label: "A",
                    name: "salesTaxA",
                    style: { width: { base: "100%", sm: "100%", lg: "calc(70% - 5px)" }, marginLeft: "5px" },
                    value: "salesTaxA",
                    isReadOnly: false,
                    isPercentage: true
                },
            ],
        },
        {
            label: "S.Tax Amt.",
            name: "sTaxAmount",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "sTaxAmount",
            isReadOnly: true
        },
        {
            label: "Net Amt",
            name: "netAmount",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "netAmount",
            isReadOnly: true
        },
        {
            label: "Discount",
            fields: [
                {
                    label: "%",
                    name: "discountPercent",
                    style: { width: { base: "100%", sm: "100%", lg: "30%" } },
                    value: "discountPercent",
                    isReadOnly: false,
                    isPercentage: true
                },
                {
                    label: "Amount",
                    name: "discountAmount",
                    style: { width: { base: "100%", sm: "100%", lg: "calc(70% - 5px)" }, marginLeft: "5px" },
                    value: "discountAmount",
                    isReadOnly: true
                },
            ],
        },
        {
            label: "Freight",
            name: "freight",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "freight",
            isReadOnly: false
        },
        {
            label: "Net Payable Amt",
            name: "netPayableAmt",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "netPayableAmt",
            isReadOnly: true
        },
        {
            label: "Payable Amount",
            name: "payableAmount",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "payableAmount",
            isReadOnly: false
        },
        {
            label: "Cash Rcvd",
            name: "cashReceived",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "cashReceived",
            isReadOnly: false
        },
        {
            label: "Credit Card",
            name: "creditCard",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "creditCard",
            isReadOnly: false
        },
        {
            label: "Balance",
            name: "balance",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "balance",
            isReadOnly: true
        },
        {
            label: "Total Qty",
            name: "totalQty",
            style: { width: { base: "100%", sm: "100%", lg: "70%" } },
            value: "totalQty",
            isReadOnly: true
        },
        // {
        //     label: "SM.C%",
        //     style: { width: { base: "100%", sm: "100%", lg: "70%" } },
        //     fields: [
        //         {
        //             label: "SM1",
        //             name: "smCommission1",
        //             style: { width: { base: "100%", sm: "100%", lg: "30%" } },
        //             value: "smCommission1",
        //             isReadOnly: false,
        //             isPercentage: true
        //         },
        //         {
        //             label: "SM2",
        //             name: "smCommission2",
        //             style: { width: { base: "100%", sm: "100%", lg: "calc(70% - 5px)" }, marginLeft: "5px" },
        //             value: "smCommission2",
        //             isReadOnly: false,
        //             isPercentage: true
        //         },
        //     ],
        // },
    ], [
        {
            label: "Cash",
            style: { width: "70%", display: "flex" },
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "clients",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    value: { cashId: "cashId", cashName: "cashName" },
                    nameFields: ["cashId", "cashName"],
                    keys: ["id", "title"],
                },
            ],
        },
    ]
];

export const ClientOrderItemTableHeader = ["ID", "Title", "Unit"];

export const ClientOrderCalculationFields = [
    "grossAmt", "salesTaxR", "salesTaxA", "sTaxAmount",
    "discountPercent", "discountAmount", "freight", "netAmount",
    "netPayableAmt", "cashReceived", "creditCard", "balance"
];

export const createClientOrderEmptyTableRow = () => ([{
    Item_ID: "", Item_Title: "", unit: "",
    remarks: "", qty: "", rate: "", total: "", Installation_Charges: 0
}]);

export const createClientOrderInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "",
    mnth: "", tenderNo: "", contractNo: "", po_date: "", poNo: "", clientId: "",
    clientTitle: "", cashId: "", cashName: "", salesManId: "", salesManName: "",
    deliveryPersonId: "", deliveryPersonName: "", installerId: "", installerName: "", installationTime: "", deliveryPersonTime: "",
    grossAmt: 0, salesTaxR: "", salesTaxA: "", sTaxAmount: 0,
    discountPercent: "", discountAmount: 0, poNo: "", po_date: "",
    freight: 0, netAmount: 0, cashReceived: 0, netPayableAmt: 0,
    totalQty: 0, creditCard: 0, smCommission1: "", smCommission2: "",
    balance: 0, narration: "", copyVoucherNo: ""
});

export const ClientOrderRequiredFields = [
    "Dated",
    "VTP",
    "Mnth",
    "Location",
    "vno",
    "client_id",
    "SalesMan_ID"
];

export const ClientOrderItemsRequiredFields = [
    "Dated",
    "VTP",
    "Mnth",
    "Location",
    "vno",
    "item_ID",
    "Qty",
    "GrossRate",
    "Rate",
    "Total"
];
// Client Order Ends Here----------------------------------------------------------------

// Goods Receipt Note Starts here------------------
export const GoodsReceiptNoteHeaders = [
    {
        label: "Item ID",
        key: "Item_ID",
        width: "200px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Item Title",
        key: "Item_Title",
        width: "350px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "U.O.M",
        key: "UOM",
        width: "100px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Qty Rcvd",
        key: "qtyRcvd",
        width: "150px",
        isReadOnly: false,
        type: "number",
    },
];

export const GoodsReceiptNoteSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "Date",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Voucher No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Suppliers",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "suppliers",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Title"],
                    keys: ["id", "title"],
                    value: {
                        suppliersId: "suppliersId",
                        suppliersTitle: "suppliersTitle",
                    },
                    nameFields: ["suppliersId", "suppliersTitle"],
                },
            ],
        },
        {
            label: "Employee",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "salesMan",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Title"],
                    keys: ["ID", "Title"],
                    value: {
                        employeeId: "employeeId",
                        employeeTitle: "employeeTitle",
                    },
                    nameFields: ["employeeId", "employeeTitle"],
                },
            ],
        },
    ],
];

export const GoodsReceiptNoteItemTableHeader = ["ID", "Title"];

export const createGoodsReceiptNoteEmptyTableRow = () => ([{
    Item_ID: "", Item_Title: "",
    qtyRcvd: "", UOM: "",
}]);

export const createGoodsReceiptNoteInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "", mnth: "",
    suppliersId: "", suppliersTitle: "", employeeId: "", employeeTitle: "",
    narration: ""
});

export const GoodsReceiptNoteRequiredFields = [
    "Dated",
    "VTP",
    "mnth",
    "Location",
    "vno",
    "Emp_ID",
    "Supp_id",
];

export const GoodsReceiptNoteItemsRequiredFields = [
    "Dated",
    "VTP",
    "Mnth",
    "Location",
    "vno",
    "Item_ID",
    "Unit",
    "Qty",
];
// Goods Receipt Note Ends here--------------------


// Recovery Follow Up Starts here------------------
export const RecoveryFollowUpHeaders = [
    {
        label: "Commitment Date",
        key: "Commitment_Date",
        isReadOnly: false,
        type: "date",
    },
    {
        label: "Commitment Amount",
        key: "Commitment_Amount",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Remarks",
        key: "Remarks",
        isReadOnly: false,
        type: "text",
    },
];

export const RecoveryFollowUpSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Quotation No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Client",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "client",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Name"],
                    keys: ["id", "title"],
                    value: {
                        clientId: "clientId",
                        clientTitle: "clientTitle",
                    },
                    nameFields: ["clientId", "clientTitle"],
                },
            ],
        },
        {
            label: "Email",
            fields: [
                {
                    component: "Input",
                    type: "email",
                    placeholder: "",
                    width: "100%",
                    value: "email",
                    name: "email"
                },
            ]
        },
        {
            label: "Phone No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "phoneNo",
                    name: "phoneNo"
                },
            ]
        },
        {
            label: "Landline No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "landlineNo",
                    name: "landlineNo"
                },
            ]
        },
        {
            label: "Contact Person",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "contact",
                    name: "contact"
                },
            ]
        },
        {
            label: "Address",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "address",
                    name: "address"
                },
            ]
        },
        {
            label: "Comments",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "comments",
                    name: "comments"
                },
            ]
        },
    ], [
        {
            label: "Advance Payment",
            fields: [
                {
                    component: "Input",
                    type: 'number',
                    placeholder: "",
                    width: "100%",
                    value: "initialPayment",
                    name: "initialPayment"
                },
            ]
        },
        {
            label: "Balance Amount",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    type: 'number',
                    width: "100%",
                    value: "balanceAmount",
                    name: "balanceAmount"
                },
            ]
        },
    ], [
        {
            label: "Total Outstandings",
            fields: [
                {
                    component: "Input",
                    type: 'number',
                    placeholder: "",
                    width: "100%",
                    value: "totalOutstandings",
                    name: "totalOutstandings"
                },
            ]
        },
        {
            label: "Due Date",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    type: 'date',
                    width: "100%",
                    value: "overDue",
                    name: "overDue"
                },
            ]
        },
    ], [
        {
            label: "Last Commitment Date",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    type: 'date',
                    value: "lastCommitmentDate",
                    name: "lastCommitmentDate"
                },
            ]
        },
        {
            label: "Last Commitment Amount",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "lastCommitmentAmount",
                    name: "lastCommitmentAmount"
                },
            ]
        },
        {
            label: "Remarks",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "remarks",
                    name: "remarks"
                },
            ]
        },
    ]
];

export const createRecoveryFollowUpEmptyTableRow = () => ([{
    Commitment_Date: "", Commitment_Amount: 0,
    Remarks: ""
}]);

export const createRecoveryFollowUpInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "", mnth: "",
    clientId: "", clientTitle: "", phoneNo: "", mobileNo: "", faxNo: "", contact: "", email: "",
    designation: "", department: "", installer: "", profession: "", totalOutstandings: "", overdue: "", lastCommitmentDate: "",
    lastCommitmentAmount: "", remarks: "", initialPayment: 0, balanceAmount: 0, subject: "", quotation: ""
});

export const RecoveryFollowUpRequiredFields = [
    "Dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
    // "Client_ID",
    // "ContactPerson",
    // "Designation",
    // "ClientDepartment",
];

// Recovery Follow Up Starts here------------------
export const DeliveryFollowUpHeaders = [
    {
        label: "Item ID",
        key: "Item_ID",
        width: "100px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Item Title",
        key: "Item_Title",
        width: "300px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Unit",
        key: "unit",
        width: "60px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Remarks",
        key: "remarks",
        width: "320px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Charges",
        key: "Installation_Charges",
        width: "100px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Qty",
        key: "qty",
        width: "70px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Rate",
        key: "rate",
        width: "70px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Total",
        key: "total",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Remaining",
        key: "remaining",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Delivered",
        key: "delivered",
        width: "90px",
        isReadOnly: false,
        type: "Select",
    },
];


export const DeliveryFollowUpSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Quotation No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Client",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "client",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Name"],
                    keys: ["id", "title"],
                    value: {
                        clientId: "clientId",
                        clientTitle: "clientTitle",
                    },
                    nameFields: ["clientId", "clientTitle"],
                },
            ],
        },
        {
            label: "Email",
            fields: [
                {
                    component: "Input",
                    type: "email",
                    placeholder: "",
                    width: "100%",
                    value: "email",
                    name: "email"
                },
            ]
        },
        {
            label: "Phone No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "phoneNo",
                    name: "phoneNo"
                },
            ]
        },
        {
            label: "Landline No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "landlineNo",
                    name: "landlineNo"
                },
            ]
        },
        {
            label: "Contact Person",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "contact",
                    name: "contact"
                },
            ]
        },
        {
            label: "Address",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "address",
                    name: "address"
                },
            ]
        },
        {
            label: "Comments",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "comments",
                    name: "comments"
                },
            ]
        },
    ], [
        {
            label: "Remarks",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "remarks",
                    name: "remarks"
                },
            ]
        },
    ]
];

export const createDeliveryFollowUpEmptyTableRow = () => ([{
    qty: 0, rate: 0, total: 0, Item_Title: "", unit: "",
    Installation_Charges: 0, Item_ID: "", remarks: "",
    delivered: 0, remaining: 0
}]);

export const createDeliveryFollowUpInitialFormData = () => ({
    location: "", vno: "", vtp: "", voucherNo: "", date: "", mnth: "",
    clientId: "", clientTitle: "", phoneNo: "", mobileNo: "", faxNo: "", contact: "", email: "",
    designation: "", department: "", installer: "", profession: "", totalOutstandings: "", overdue: "", lastCommitmentDate: "",
    lastCommitmentAmount: "", remarks: "", initialPayment: 0, balanceAmount: 0
});

export const DeliveryFollowUpRequiredFields = [
    "Dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
    // "Client_ID",
    // "ContactPerson",
    // "Designation",
    // "ClientDepartment",
];

// Recovery Follow Up Ends here--------------------
// Installer Follow Up Starts here------------------
export const InstallerFollowUpHeaders = [
    {
        label: "Item ID",
        key: "Item_ID",
        width: "100px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Item Title",
        key: "Item_Title",
        width: "300px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Unit",
        key: "unit",
        width: "60px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Remarks",
        key: "remarks",
        width: "320px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Charges",
        key: "Installation_Charges",
        width: "100px",
        isReadOnly: true,
        type: "text",
    },
    {
        label: "Qty",
        key: "qty",
        width: "70px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Rate",
        key: "rate",
        width: "70px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Total",
        key: "total",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Delivered Remaining",
        key: "DeliveredRemaining",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Delivered",
        key: "DeliveredItems",
        width: "90px",
        isReadOnly: true,
        type: "Select",
    },
    {
        label: "Installed Remaining",
        key: "InstalledRemaining",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
    {
        label: "Installed",
        key: "InstalledItems",
        width: "90px",
        isReadOnly: false,
        type: "Select",
    },
];


export const InstallerFollowUpSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Quotation No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
    ], [
        {
            label: "Client",
            isRequired: true,
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "client",
                    tableHeaders: ["ID", "Title"],
                    placeholders: ["ID", "Name"],
                    keys: ["id", "title"],
                    value: {
                        clientId: "clientId",
                        clientTitle: "clientTitle",
                    },
                    nameFields: ["clientId", "clientTitle"],
                },
            ],
        },
        {
            label: "Email",
            fields: [
                {
                    component: "Input",
                    type: "email",
                    placeholder: "",
                    width: "100%",
                    value: "email",
                    name: "email"
                },
            ]
        },
        {
            label: "Phone No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "phoneNo",
                    name: "phoneNo"
                },
            ]
        },
        {
            label: "Landline No",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "landlineNo",
                    name: "landlineNo"
                },
            ]
        },
        {
            label: "Contact Person",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "contact",
                    name: "contact"
                },
            ]
        },
        {
            label: "Address",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "address",
                    name: "address"
                },
            ]
        },
        {
            label: "Comments",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "comments",
                    name: "comments"
                },
            ]
        },
    ], [
        {
            label: "Remarks",
            fields: [
                {
                    component: "Input",
                    placeholder: "",
                    width: "100%",
                    value: "remarks",
                    name: "remarks"
                },
            ]
        },
    ]
];

export const createInstallerFollowUpEmptyTableRow = () => ([{
    qty: 0, rate: 0, total: 0, Item_Title: "", unit: "",
    Installation_Charges: 0, Item_ID: "", remarks: "",
    delivered: 0, remaining: 0
}]);

export const createInstallerFollowUpInitialFormData = () => ({
    location: "", vno: "", VTP: "", voucherNo: "", date: "", mnth: "",
    clientId: "", clientTitle: "", phoneNo: "", mobileNo: "", faxNo: "", contact: "", email: "",
    designation: "", department: "", installer: "", profession: "", totalOutstandings: "", overdue: "", lastCommitmentDate: "",
    lastCommitmentAmount: "", remarks: "", initialPayment: 0, balanceAmount: 0
});

export const InstallerFollowUpRequiredFields = [
    "Dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
    // "Client_ID",
    // "ContactPerson",
    // "Designation",
    // "ClientDepartment",
];

// Recovery Follow Up Ends here--------------------

// Registration Form Starts here-------------------

export const RegistrationFormHeaders = [
    {
        label: "Item ID",
        key: "Item_ID",
        width: "110px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Item Title",
        key: "Item_Title",
        width: "270px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Unit",
        key: "unit",
        width: "60px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Remarks",
        key: "remarks",
        width: "320px",
        isReadOnly: false,
        type: "text",
    },
    {
        label: "Qty",
        key: "qty",
        width: "70px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Rate",
        key: "rate",
        width: "70px",
        isReadOnly: false,
        type: "number",
    },
    {
        label: "Total",
        key: "total",
        width: "90px",
        isReadOnly: true,
        type: "number",
    },
];

export const RegistrationFormSectionFormFields = [
    [
        {
            id: "date",
            name: "date",
            label: "Date",
            type: "date",
            isReadOnly: false,
            value: "date",
            isRequired: true,
            placeholder: "Date",
            minDate: `${new Date().getFullYear()}-01-01`,
            maxDate: `${new Date().getFullYear()}-12-31`,
        },
        {
            id: "voucherNo",
            name: "voucherNo",
            label: "Voucher No.",
            type: "text",
            isReadOnly: true,
            value: "voucherNo",
            isRequired: true,
            placeholder: "",
        },
        {
            id: "TSSRegNo",
            name: "TSSRegNo",
            label: "TSS Reg #",
            type: "text",
            isReadOnly: false,
            value: "TSSRegNo",
            isRequired: false,
            placeholder: "",
        },
    ], [
        {
            label: "Full Name",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "StudentName",
                    value: "StudentName",
                    type: "text",
                    placeholder: "",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "D.O.B",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "DateOfBirth",
                    value: "DateOfBirth",
                    type: "date",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Gender",
            isRequired: false,
            fields: [
                {
                    component: "Select",
                    name: "Sex",
                    value: "Sex",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
    ],
    [
        {
            label: "Father's Name",
            isRequired: true,
            fields: [{
                component: "Input",
                name: "FatherName",
                value: "FatherName",
                type: "text",
                placeholder: "",
                _placeholder: { color: "gray.500" },
            }]
        },
        {
            label: "Occupation",
            isRequired: true,
            fields: [
                {
                    component: "Input",
                    name: "FatherOccupation",
                    value: "FatherOccupation",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        // {
        //     label: "Father's CNIC",
        //     isRequired: true,
        //     fields: [
        //         {
        //             component: "Input",
        //             name: "GuardianCNIC",
        //             value: "GuardianCNIC",
        //             type: "number",
        //             placeholder: "Enter without Hyphens (-)",
        //             _placeholder: { color: "gray.500" },
        //         },
        //     ],
        // },
        {
            label: "Business Address",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "BusinessAddress",
                    value: "BusinessAddress",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Tel. Home",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "TelHome",
                    value: "TelHome",
                    type: "number",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Tel. Business",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "TelBusiness",
                    value: "TelBusiness",
                    type: "number",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Mother's Name",
            isRequired: true,
            fields: [
                {
                    component: "Input",
                    name: "MotherName",
                    value: "MotherName",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        // {
        //     label: "Mother's CNIC",
        //     isRequired: true,
        //     fields: [
        //         {
        //             component: "Input",
        //             name: "CNIC",
        //             value: "CNIC",
        //             type: "number",
        //             placeholder: "Enter without hyphens (-)",
        //             _placeholder: { color: "gray.500" },
        //         },
        //     ],
        // },
    ],
    [
        {
            label: "Emergency Contact",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "EmergencyContactNo",
                    value: "EmergencyContactNo",
                    type: "number",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Permanent Address",
            isRequired: true,
            fields: [
                {
                    component: "Input",
                    name: "PermanantAddress",
                    value: "PermanantAddress",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
    ],
    [
        {
            label: "Mailing Address",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "MailingAddress",
                    value: "MailingAddress",
                    type: "email",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
        {
            label: "Email Address",
            isRequired: false,
            fields: [
                {
                    component: "Input",
                    name: "EmailAddress",
                    value: "EmailAddress",
                    type: "text",
                    _placeholder: { color: "gray.500" },
                },
            ],
        },
    ],
    [
        {
            label: "Cash",
            style: { width: "70%", display: "flex" },
            fields: [
                {
                    component: "ComboBox",
                    inputWidth: ["calc(30% - 5px)", "calc(70% - 5px)"],
                    buttonWidth: "20px",
                    tableData: "cash",
                    tableHeaders: ["ID", "Name"],
                    placeholders: ["ID", "Name"],
                    value: { Cash_ID: "Cash_ID", Cash_Title: "Cash_Title" },
                    nameFields: ["Cash_ID", "Cash_Title"],
                    keys: ["id", "title"],
                },
            ],
        },
    ],
];

export const RegistrationFormItemTableHeader = ["Student ID", "Name", "Father Name"];

export const createRegistrationFormInitialFormData = () => ({
    date: "",
    vtp: "",
    Mnth: "",
    Location: "",
    vno: "",
    voucherNo: "",
    StudentName: "",
    DateOfBirth: "",
    Age: 0,
    Sex: "",
    MailingAddress: "",
    EmailAddress: "",
    TelHome: "",
    Cash_ID: "",
    Cash_Title: "",
    TSSRegNo: "",
    FatherName: "",
    FatherOccupation: "",
    FatherQualification: "",
    BusinessAddress: "",
    PermanantAddress: "",
    TelNo: "",
    TelBusiness: "",
    MotherName: "",
    MotherQualification: "",
    EmergencyContactNo: "",
    Narration: "",
    CreationDate: ""
});

export const RegistrationFormRequiredFields = [
    "dated",
    "vtp",
    "Mnth",
    "Location",
    "vno",
];

// Registration Form Ends here-------------------