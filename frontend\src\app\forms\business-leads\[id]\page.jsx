"use client";
import { useEffect, useRef, useState } from "react";
import Loader from "@components/Loader/Loader";
import {
  Box,
  Flex,
  Divider,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Input,
  InputGroup,
  InputRightElement,
  IconButton,
  Badge,
} from "@chakra-ui/react";
import { SearchIcon } from "@chakra-ui/icons";
import { useParams } from 'next/navigation';
import { formatDate, generateRandomColorRGBA, getData } from "@utils/functions";
import axiosInstance from "@src/app/axios";
import dayjs from 'dayjs';
import AddPurposeModal from './AddPurposeModal';
import { LuCirclePlus } from "react-icons/lu";

const Contact = () => {
  const [isAddPurposeModalOpen, setIsAddPurposeModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [assessors, setAssessors] = useState([]);
  const { id } = useParams();
  const [searchTerm, setSearchTerm] = useState("");
  const clientIdAndIndex = useRef({ id: 0, index: 0 });
  const [contact, setContact] = useState({});
  const [tableData, setTableData] = useState([]);

  const getInitials = (name) => {
    if (name) {
      return name.replace(/[^a-zA-Z ]/g, "").split(" ").map(part => part[0]).join("").toUpperCase().substring(0, 2);
    }
  };

  useEffect(() => {
    if (id) {
      const [clientId, clientIndex] = id.split("_");
      clientIdAndIndex.current = { id: clientId, index: clientIndex };
    }
  }, [id]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const { data: clientData } = await getData("client/" + clientIdAndIndex.current.id);
      const assessorsData = await getData("getRecords/assessors?includeAdmin=true");
      setAssessors(assessorsData || []);
      if (clientData) {
        const { data: reportsData } = await getData("purpose/client/" + clientData.id);
        const data = {
          name: clientData.title,
          phone: clientData.tel,
          email: clientData.email,
          website: clientData.url,
          portallink: clientData.url,
          mobile: clientData.Mobile,
          tags: clientData.tags,
          shipping: clientData.add1,
          currency: 'AUD',
          id: clientData.id
        };
        setContact(data);
        const filteredData = reportsData.map((report, index) => {
          return {
            id: index + 1,
            assignedTo: report.assessorTitle ?? 'N/A',
            status: report.status || 'in-progress',
            contact: report.contactPerson ?? 'N/A',
            startDate: report?.assessmentTime
              ? dayjs(report?.assessmentTime).format('YYYY-MM-DD hh:mm:ss A')
              : null,
          };
        });
        setTableData(filteredData || []);
      }
      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'rejected': return 'red';
      case 'pending': return 'yellow';
      case 'in-progress': return 'blue';
      case 'completed': return 'green';
      default: return 'gray';
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const filteredData = tableData.filter(row =>
    row.assignedTo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.contact.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div className="wrapper">
          <div>
            <div>
              <div className="page-inner">
                <div className="row">
                  <div className="bgWhite">
                    <h1 style={{ margin: "0", textAlign: "center", color: "#2B6CB0", fontSize: "24px", fontWeight: "bold", padding: "10px" }}>
                      Contact Details
                    </h1>
                  </div>
                </div>
                <div className="row" style={{ gap: "10px", paddingTop: "8px" }}>
                  <Box
                    sx={{
                      padding: "15px",
                      width: {
                        base: "100% !important",
                        sm: "100%",
                        lg: "calc(40% - 5px) !important",
                      },
                    }}
                    className="bgWhite col-md-5 col-sm-12">
                    <Flex alignItems="center" gap={4}>
                      <Box width="50px" height="50px" background={generateRandomColorRGBA(100 + Number(clientIdAndIndex.current.index), 1)} borderRadius="50%" display="flex" justifyContent="center" alignItems="center" color="white">
                        {getInitials(contact.name)}
                      </Box>
                      <Flex direction="column" ml={2}>
                        <Box fontSize={30} fontWeight={900}>{contact.name}</Box>
                        <Box color="#a0a0a0">Main Contact</Box>
                      </Flex>
                    </Flex>
                    <Divider my={4} sx={{ background: "#000", height: '1px', opacity: .2 }} />
                    <Flex justifyContent="space-between" alignItems="center" gap={4}>
                      <Box fontSize={20} fontWeight={600}>Contact Details</Box>
                      {/* <Button bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} variant="outline" borderRadius="20px" paddingX={6}>Edit</Button> */}
                    </Flex>
                    {["Phone", "Email", "Mobile", "Website", "Tags", "Portal Link"].map((field, idx) => (
                      <Flex direction="column" mt={4} key={idx}>
                        <Box fontSize={14} fontWeight={600}>{field}</Box>
                        <Box fontSize={14}>{contact[field.toLowerCase().replace(" ", "")] || 'N/A'}</Box>
                      </Flex>
                    ))}
                    <Flex justifyContent="space-between" alignItems="center" gap={4}>
                      <Box fontSize={20} fontWeight={600}>Customer/Vendor Details</Box>
                      {/* <Button bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} variant="outline" borderRadius="20px" paddingX={6}>Edit</Button> */}
                    </Flex>
                    {["Shipping", "Currency",].map((field, idx) => (
                      <Flex direction="column" mt={4} key={idx}>
                        <Box fontSize={14} fontWeight={600}>{field}</Box>
                        <Box fontSize={14}>{contact[field.toLowerCase().replace(" ", "")] || 'N/A'}</Box>
                      </Flex>
                    ))}
                  </Box>
                  <Box
                    sx={{
                      padding: "15px",
                      width: {
                        base: "100% !important",
                        sm: "100%",
                        lg: "calc(60% - 5px) !important",
                      },
                    }}
                    className="bgWhite col-md-5 col-sm-12">
                    <Box
                      display={'flex'}
                      justifyContent={'flex-end'}
                      alignItems={'center'}
                      gap={4}
                      marginBottom={5}
                    >
                      <InputGroup>
                        <Input
                          type="text"
                          placeholder="Search"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <InputRightElement>
                          <IconButton
                            aria-label="Search"
                            icon={<SearchIcon />}
                          />
                        </InputRightElement>
                      </InputGroup>
                      <Box
                        display={'flex'}
                        justifyContent={'flex-end'}
                        alignItems={'center'}
                      >
                        <Button
                          bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }}
                          variant="outline"
                          borderRadius="8px"
                          paddingX={6}
                          display={'flex'}
                          justifyContent={'center'}
                          alignItems={'center'}
                          gap={2}
                          sx={{
                            'svg': {
                              height: '16px',
                              width: '16px'
                            }
                          }}
                          onClick={() => setIsAddPurposeModalOpen(true)}
                        >
                          <LuCirclePlus />
                          <Box fontSize={12}>Add Lead</Box>
                        </Button>
                      </Box>
                    </Box>
                    <TableContainer>
                      <Table variant="simple">
                        <Thead>
                          <Tr>
                            <Th>#</Th>
                            <Th>Assigned To</Th>
                            <Th>Contact</Th>
                            <Th>Assessment Date</Th>
                            <Th>Status</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {filteredData.map((row) => (
                            <Tr key={row.id}>
                              <Td>{row.id}</Td>
                              <Td>{row.assignedTo}</Td>
                              <Td>{row.contact}</Td>
                              <Td>{row.startDate}</Td>
                              <Td>
                                <Badge colorScheme={getStatusColor(row.status)}>
                                  {row.status}
                                </Badge>
                              </Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </Box>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <AddPurposeModal
        isOpen={isAddPurposeModalOpen}
        onClose={() => setIsAddPurposeModalOpen(false)}
        onSave={() => loadInitialData()}
        assessors={assessors}
        clientID={clientIdAndIndex.current.id}
      />
    </>
  );
};

export default Contact;
