"use client";
import { useEffect, useMemo, useRef, useState } from "react";
import axiosInstance from "../../axios";
import Loader from "@components/Loader/Loader";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableCaption,
  TableContainer,
  InputGroup,
  Input,
  IconButton,
  Flex,
  HStack,
  Button,
  Text,
  Badge,
  InputLeftElement,
} from "@chakra-ui/react";
import { PiExportBold } from "react-icons/pi";
import { BiImport, BiSearch } from "react-icons/bi";
import { FiUserPlus } from "react-icons/fi";
import { CSVLink } from "react-csv";
import CSVReader from "react-csv-reader";
import Link from "next/link";
import { generateRandomColorRGBA } from "@utils/functions";
import AddClientModal from '@src/components/Custom/Modal/AddClientModal';
import { keyframes } from "@emotion/react";

// Define subtle animations
const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const slideUp = keyframes`
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
`;

const Contact = () => {
  const [clients, setClients] = useState([]);
  const [isAddClientModalOpen, setIsAddClientModalOpen] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [clientsPerPage] = useState(10);
  const fileInputRef = useRef(null);

  const getInitials = (name) => {
    const nameParts = name.split(" ");
    const initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];
    return initials.toUpperCase();
  };

  const filterData = useMemo(() => {
    return clients.filter((item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, clients]);

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const loadInitialData = async () => {
    const { data: clientsData } = await getData("client");
    if (clientsData) {
      const formattedData = clientsData.map((client) => ({
        id: client.id,
        name: client.title,
        phone: client.tel || client.Mobile,
        email: client.email,
        tags: client.surName || "Client",
      }));
      setClients(formattedData);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const handleImportCSV = (data) => {
    data.shift();
    const formattedData = data.map((item) => ({
      name: item[0],
      phone: item[1],
      email: item[2],
      tags: item[3],
    }));
    setClients((prevData) => [...prevData, ...formattedData]);
    fileInputRef.current.value = null;
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const indexOfLastClient = currentPage * clientsPerPage;
  const indexOfFirstClient = indexOfLastClient - clientsPerPage;
  const currentClients = filterData.slice(indexOfFirstClient, indexOfLastClient);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  const paginateNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const paginatePrev = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const totalPages = Math.ceil(filterData.length / clientsPerPage);
  const maxPageButtons = 5;
  const startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
  const endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <Box 
                      className="bgWhite"
                      sx={{
                        animation: `${fadeIn} 0.5s ease-out`,
                        boxShadow: 'sm',
                        borderRadius: 'lg',
                        overflow: 'hidden'
                      }}
                    >
                      <Text
                        margin="0"
                        textAlign="center"
                        fontSize="2xl"
                        fontWeight="bold"
                        padding="6"
                        bgGradient="linear(to-r, #2d6651, #2d6651)"
                        bgClip="text"
                        letterSpacing="tight"
                      >
                        Business Leads
                      </Text>
                    </Box>
                  </div>
                  <div className="row">
                    <Box 
                      className="bgWhite mt-2" 
                      sx={{
                        animation: `${slideUp} 0.5s ease-out`,
                        boxShadow: 'sm',
                        borderRadius: 'lg',
                        overflow: 'hidden'
                      }}
                    >
                      <Box className="pt-4 pb-2" p={4}>
                        <Flex gap={4} flexWrap={{ base: 'wrap', md: 'nowrap' }} mb={6}>
                          <InputGroup flex="1">
                            <InputLeftElement pointerEvents="none">
                              <BiSearch color="#2d6651" />
                            </InputLeftElement>
                            <Input
                              type="text"
                              placeholder="Search leads..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              borderColor="gray.200"
                              style={{ paddingLeft: "30px !important" }}
                              _hover={{ borderColor: "#2d6651" }}
                              _focus={{ 
                                borderColor: "#2d6651",
                                boxShadow: "0 0 0 1px #2d6651"
                              }}
                              transition="all 0.2s"
                            />
                          </InputGroup>
                          <HStack spacing={2}>
                            <IconButton 
                              title="Import CSV" 
                              icon={<BiImport />}
                              onClick={handleButtonClick}
                              variant="outline"
                              borderColor="#2d6651"
                              color="#2d6651"
                              _hover={{
                                bg: "rgba(58, 134, 106, 0.1)",
                                transform: "translateY(-1px)"
                              }}
                              transition="all 0.2s"
                            />
                            <CSVLink data={filterData} filename={"contacts.csv"}>
                              <IconButton 
                                title="Export CSV" 
                                icon={<PiExportBold />}
                                variant="outline"
                                borderColor="#2d6651"
                                color="#2d6651"
                                _hover={{
                                  bg: "rgba(58, 134, 106, 0.1)",
                                  transform: "translateY(-1px)"
                                }}
                                transition="all 0.2s"
                              />
                            </CSVLink>
                            <Button 
                              leftIcon={<FiUserPlus />}
                              onClick={() => setIsAddClientModalOpen(true)}
                              bgGradient="linear(to-r, #2d6651, #2d6651)"
                              color="white"
                              _hover={{
                                bgGradient: "linear(to-r, #2d6651, #2d6651)",
                                transform: "translateY(-1px)",
                                boxShadow: "md"
                              }}
                              transition="all 0.2s"
                            >
                              Add Client
                            </Button>
                          </HStack>
                        </Flex>

                        <CSVReader
                          cssClass="csv-reader-input"
                          onFileLoaded={handleImportCSV}
                          onError={console.error}
                          accept=".csv"
                          inputId="csvInput"
                          inputStyle={{ display: "none" }}
                          ref={fileInputRef}
                        />

                        <Box 
                          borderRadius="lg" 
                          overflow="hidden" 
                          border="1px" 
                          borderColor="gray.200"
                          sx={{
                            animation: `${fadeIn} 0.5s ease-out`,
                            animationDelay: '0.2s'
                          }}
                        >
                          <TableContainer>
                            <Table variant="simple">
                              <TableCaption p={4}>
                                <Text color="gray.600" fontSize="sm">
                                  Showing {indexOfFirstClient + 1} to {indexOfFirstClient + currentClients.length} of {filterData.length} records
                                </Text>
                              </TableCaption>
                              <Thead>
                                <Tr bg="gray.50">
                                  <Th width="60px"></Th>
                                  <Th color="#2d6651">Contact Name</Th>
                                  <Th color="#2d6651">Phone</Th>
                                  <Th color="#2d6651">Email</Th>
                                  <Th color="#2d6651">Tags</Th>
                                </Tr>
                              </Thead>
                              <Tbody>
                                {currentClients.map((item, index) => (
                                  <Tr 
                                    key={index}
                                    _hover={{ bg: "gray.50" }}
                                    transition="background 0.2s"
                                  >
                                    <Td>
                                      <Box 
                                        width="40px" 
                                        height="40px" 
                                        background={generateRandomColorRGBA(10 + index, 0.9)}
                                        borderRadius="full" 
                                        display="flex" 
                                        justifyContent="center" 
                                        alignItems="center" 
                                        color="white"
                                        fontSize="sm"
                                        fontWeight="600"
                                        transition="transform 0.2s"
                                        _hover={{ transform: "scale(1.1)" }}
                                      >
                                        {getInitials(item.name)}
                                      </Box>
                                    </Td>
                                    <Td>
                                      <Link 
                                        href={`/forms/business-leads/${item.id + '_' + index}`}
                                        style={{
                                          color: "#2d6651",
                                          fontWeight: "500",
                                          textDecoration: "none"
                                        }}
                                      >
                                        {item.name}
                                      </Link>
                                    </Td>
                                    <Td>{item.phone}</Td>
                                    <Td>
                                      <Text color="blue.600">{item.email}</Text>
                                    </Td>
                                    <Td>
                                      <Badge
                                        colorScheme="teal"
                                        px={2}
                                        py={1}
                                        borderRadius="full"
                                        fontSize="xs"
                                      >
                                        {item.tags}
                                      </Badge>
                                    </Td>
                                  </Tr>
                                ))}
                              </Tbody>
                            </Table>
                          </TableContainer>
                        </Box>

                        <Box mt={6}>
                          <HStack spacing={2} justifyContent="center">
                            <Button
                              onClick={paginatePrev}
                              isDisabled={currentPage === 1}
                              size="sm"
                              variant="outline"
                              borderColor="#2d6651"
                              color="#2d6651"
                              _hover={{
                                bg: "rgba(58, 134, 106, 0.1)",
                                transform: "translateY(-1px)"
                              }}
                              transition="all 0.2s"
                            >
                              &laquo;
                            </Button>
                            {Array.from({ length: endPage - startPage + 1 }, (_, index) => (
                              <Button
                                key={startPage + index}
                                onClick={() => paginate(startPage + index)}
                                size="sm"
                                variant={currentPage === startPage + index ? "solid" : "outline"}
                                bg={currentPage === startPage + index ? "#2d6651" : "white"}
                                color={currentPage === startPage + index ? "white" : "#2d6651"}
                                borderColor="#2d6651"
                                _hover={{
                                  bg: currentPage === startPage + index ? "#2d6651" : "rgba(58, 134, 106, 0.1)",
                                  transform: "translateY(-1px)"
                                }}
                                transition="all 0.2s"
                              >
                                {startPage + index}
                              </Button>
                            ))}
                            <Button
                              onClick={paginateNext}
                              isDisabled={currentPage === totalPages}
                              size="sm"
                              variant="outline"
                              borderColor="#2d6651"
                              color="#2d6651"
                              _hover={{
                                bg: "rgba(58, 134, 106, 0.1)",
                                transform: "translateY(-1px)"
                              }}
                              transition="all 0.2s"
                            >
                              &raquo;
                            </Button>
                          </HStack>
                        </Box>
                      </Box>
                    </Box>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <AddClientModal
            isOpen={isAddClientModalOpen}
            onClose={() => setIsAddClientModalOpen(false)}
            onSave={() => loadInitialData()}
          />
        </>
      )}
    </>
  );
};

export default Contact;
