"use client";
import React, { useEffect, useState } from "react";
import { Box, Button, FormControl, FormLabel, Input, Textarea, useToast } from "@chakra-ui/react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import NumberInput from "@components/Custom/NumberInput/NumberInput";
import { getData } from "@src/app/utils/functions";
import axiosInstance from "@src/app/axios";
import { RepeatIcon } from '@chakra-ui/icons';

const FormField = ({ label, children, width = "85%", isDisabled }) => (
  <FormControl
    sx={{ display: "flex", alignItems: "flex-start", flexDirection: { base: "column", lg: "row" }, mt: 2 }}
    isDisabled={isDisabled}
  >
    <FormLabel sx={{ width: { base: "100%", lg: "15%" } }}>{label}</FormLabel>
    <Box sx={{ width: { base: "100%", lg: width }, display: "flex", gap: 0 }}>{children}</Box>
  </FormControl>
);

const SplitFormField = ({ label1, label2, children1, children2, isDisabled, extraContent }) => (
  <FormControl
    sx={{ display: "flex", alignItems: "flex-start", flexDirection: { base: "column", lg: "row" }, mt: 2 }}
    isDisabled={isDisabled}
  >
    <FormLabel sx={{ width: { base: "100%", lg: "15%" } }}>{label1}</FormLabel>
    <Box sx={{ width: { base: "100%", lg: "25%" }, display: "flex", gap: 1 }}>
      {children1}
      {extraContent}
    </Box>
    <FormLabel sx={{ width: { base: "100%", lg: "15%" }, ml: { base: 0, lg: 2 } }}>{label2}</FormLabel>
    <Box sx={{ width: { base: "100%", lg: "42.5%" } }}>{children2}</Box>
  </FormControl>
);

const formInitialState = {
  manufacturerId: '',
  manufacturerTitle: '',
  categoryId: '',
  categoryTitle: '',
  productId: '',
  productName: '',
  modelNo: '',
  country: '',
  rate: '',
  description: ''
};

const DefineProducts = () => {
  const toast = useToast();
  const [formData, setFormData] = useState(formInitialState);
  const [categories, setCategories] = useState([]);
  const [manufacturers, setManufacturers] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;
      setFormData((prev) => ({ ...prev, [name]: value }));
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const handleClear = () => setFormData(formInitialState);

  const handleSave = () => {
    const transformedData = {
      id: formData.productId,
      Category_ID: formData.categoryId,
      Manufacturer_ID: formData.manufacturerId,
      Title: formData.productName,
      Model: formData.modelNo,
      Country: formData.country,
      Sale_Rate: parseFloat(formData.rate),
      Unit: "PCS",
      Details: formData.description,
      OMaterial: 1,
      CreationDate: new Date().toISOString(),
    };

    axiosInstance.post("product/create", transformedData)
      .then(response => {
        toast({
          title: "Product created",
          description: "The product has been created successfully.",
          status: "success",
          duration: 5000,
          isClosable: true,
        });
        setIsDisabled(true);
      })
      .catch(error => {
        toast({
          title: "An error occurred.",
          description: "Unable to create product.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        console.error('Error creating product:', error);
      });
  };

  const isFormValid = () => {
    return Object.values(formData).every(field => field !== '' && field !== undefined);
  };

  const fetchNextProductId = async () => {
    try {
      const { nextId } = await getData("product/next-id");
      setFormData(prev => ({ ...prev, productId: nextId }));
      toast({
        title: "ID Updated",
        description: "Product ID has been refreshed.",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch next product ID.",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await getData("getRecords/categories");
        setCategories(categoriesData);
        const manufacturersData = await getData("getRecords/manufacturers");
        const { nextId } = await getData("product/next-id");
        setFormData(prev => ({ ...prev, productId: nextId }));
        setManufacturers(manufacturersData);
      }
      catch (error) {
        console.error("Error fetching initial data:", error);
      }
    }
    fetchCategories();
  }, []);

  return (
    <div className="wrapper">
      <div className="page-inner">
        <Box className="bgWhite" textAlign="center" p={3}>
          <h1 style={{ color: "#2B6CB0", fontSize: "24px", fontWeight: "bold" }}>Define Products</h1>
        </Box>

        <Box className="bgWhite" p={4} mt={2}>
          <FormField label="Category" isDisabled={isDisabled}>
            <ComboBox
              target={true}
              onChange={handleInputChange}
              name={"category"}
              styleButton={{ padding: "3px !important" }}
              tableData={categories}
              tableHeaders={["ID", "Title"]}
              nameFields={["categoryId", "categoryTitle"]}
              placeholders={["ID", "Title"]}
              keys={["id", "title"]}
              form={formData}
              isDisabled={isDisabled}
            />
          </FormField>

          <FormField label="Manufacturer" isDisabled={isDisabled}>
            <ComboBox
              target={true}
              onChange={handleInputChange}
              name={"manufacturer"}
              styleButton={{ padding: "3px !important" }}
              tableData={manufacturers}
              tableHeaders={["ID", "Title"]}
              nameFields={["manufacturerId", "manufacturerTitle"]}
              placeholders={["ID", "Title"]}
              keys={["id", "title"]}
              form={formData}
              isDisabled={isDisabled}
            />
          </FormField>

          <FormField label="Model No" isDisabled={isDisabled}>
            <Input name="modelNo" value={formData.modelNo} onChange={handleInputChange} isDisabled={isDisabled} />
          </FormField>

          <SplitFormField
            label1="Product Id."
            label2="Product Name"
            isDisabled={isDisabled}
            children1={<Input name="productId" value={formData.productId} onChange={handleInputChange} isDisabled={true} />}
            children2={<Input name="productName" value={formData.productName} onChange={handleInputChange} isDisabled={isDisabled} />}
            extraContent={
              <Button
                size="sm"
                onClick={fetchNextProductId}
                isDisabled={isDisabled}
                ml={1}
              >
                <RepeatIcon />
              </Button>
            }
          />


          <SplitFormField
            label1="Country"
            label2="Rate"
            isDisabled={isDisabled}
            children1={<Input name="country" value={formData.country} onChange={handleInputChange} isDisabled={isDisabled} />}
            children2={<NumberInput name="rate" value={formData.rate} onChange={handleInputChange} isDisabled={isDisabled} />}
          />

          <FormField label="Product Description" isDisabled={isDisabled}>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter product description"
              isDisabled={isDisabled}
            />
          </FormField>
        </Box>

        <Box textAlign="right" p={3} mt={2}>
          <Button w="20%" colorScheme="red" mr={2} onClick={handleClear} isDisabled={isDisabled}>
            Clear Form
          </Button>
          <Button 
            w="20%" 
            bg="#2d6651"  color="white" _hover={{ bg: "#3a866a" }} 
            onClick={handleSave}
            isDisabled={isDisabled || !isFormValid()}
          >
            {isDisabled ? 'Edit Product' : 'Save Product'}
          </Button>
        </Box>
      </div>
    </div>
  );
};

export default DefineProducts;
